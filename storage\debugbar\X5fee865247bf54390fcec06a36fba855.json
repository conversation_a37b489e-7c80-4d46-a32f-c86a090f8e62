{"__meta": {"id": "X5fee865247bf54390fcec06a36fba855", "datetime": "2025-07-29 05:42:29", "utime": **********.269577, "method": "GET", "uri": "/contact-groups/available-contacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753767748.365183, "end": **********.269625, "duration": 0.9044418334960938, "duration_str": "904ms", "measures": [{"label": "Booting", "start": 1753767748.365183, "relative_start": 0, "end": **********.173129, "relative_end": **********.173129, "duration": 0.807945966720581, "duration_str": "808ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.173143, "relative_start": 0.807959794998169, "end": **********.269629, "relative_end": 4.0531158447265625e-06, "duration": 0.09648609161376953, "duration_str": "96.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46645160, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET contact-groups/available-contacts", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\ContactGroupController@getAvailableContacts", "namespace": null, "prefix": "", "where": [], "as": "contact-groups.available-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=221\" onclick=\"\">app/Http/Controllers/ContactGroupController.php:221-260</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01042, "accumulated_duration_str": "10.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.230128, "duration": 0.00872, "duration_str": "8.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 83.685}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.249916, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 83.685, "width_percent": 8.541}, {"sql": "select `id`, `name`, `email`, `phone` from `leads` where `created_by` = 84 and `contact_group_id` is null and `is_active` = 1 and `is_deleted` = 0 order by `name` asc", "type": "query", "params": [], "bindings": ["84", "1", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ContactGroupController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactGroupController.php", "line": 231}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2578142, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "ContactGroupController.php:231", "source": "app/Http/Controllers/ContactGroupController.php:231", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=231", "ajax": false, "filename": "ContactGroupController.php", "line": "231"}, "connection": "omx_sass_systam_db", "start_percent": 92.226, "width_percent": 7.774}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contact-groups\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/contact-groups/available-contacts", "status_code": "<pre class=sf-dump id=sf-dump-1471595410 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1471595410\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-252178628 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-252178628\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1458329570 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1458329570\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1924789836 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IkN3dkJadXNXM1YxejVmNUlGSnFEeGc9PSIsInZhbHVlIjoiU1BwY3g0SUFHSW4wRUtnSWs2VU5LV2xIV1ozNmovOFlLd0pWeExjb2UreEdVUUFhTGh1NEI5QTlXNWIwMmV0d1VBRTlQM2FvOVczUFF4elpsVDJPZU9GS1BPYnB3c0pRZ3Y5d1J4b2RmSzBNd2NPU3J4aHViY28vMjc2R3M5RFN6bUtuY3AycW92R0xHdnV0ZmNLb0MzelFxTjlncjBTSlR1VWxvS2hJcnVPVEZXN2FPUnl5c2Qxdno0L1dzTE50UzNsRHhDRGRVSlorN1NGOTJtQTA1dnM4RGdPTmlVMUE1RTcrUU5yZlVpL0t5OHNDUTIyaTZxbW56dFVFTnp2UWhmZmt1Wjd3K3JpdnJTbElnNVpzVU5MOFpIcnlzN0cvRDNodys4dUw3b2RKYUh1OFNtT1ozN3IxL2FjaXIrZWY0aFJkTnhUbjNqUk1xSlI5ZW5Cc2hTTnlYYm9ieDJlVkFLMisxa3U0R3VoK3FPZFU0OTNIKzJJVExGeFBPQlVhdHV4WUQ5RzlQTDhzN0V4OC8vdnNmaGsvUmxFR3BWM255eVQ5Z1FmazRyNEFPc1RSRDA2RUlITWxQS29LYWVmRVRST1VLYWZIdlBXdlUyNi85VGgza29USmZJTS9xNkZSYWJwb1YzQzFvWGJXRldqRjhPdE12a3c4VXlCTVdPY1oiLCJtYWMiOiJhNmU4OTEyNDcwZWQ3NTg3N2RjNWVkMDRmZmNhN2YzZmYzNmJiYTk4MWIxY2ZmZDBhN2QyNDFkMzczNTg4NTJjIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImVwOEVNVkVydjhLNENrdWJMWEd3UHc9PSIsInZhbHVlIjoiVml5eTdidVZWeE9zL0l6NGpUZVFXT2tiMWlaZGZRUnpQUUFaTm80QmZFNjRmeGFyZ1E1UEtkV3o0NUhVZHpZaDRmdU5xQzlVb3MrWHFwL1J0NDcxNjB3c2ZncFhuOTNtc2RQVWF3d3g3WGE5SG80L09xTEY0NkV0bFJKQnk5dzlzTzBsYTlsUEU2TnZkYWc1UjdJZUo3MWxielQwdFdWeC92Vnd0VXp0Z3ptd1h6RDZiR3ZPUVpKUnRad243K25ZNWlmR1BDbUVTS2VXYmR3U1FTaEdHNjhEdXhZN1M3SGYwV3RjdEFJdXlpUENIQXZXemcyNERDbXdyQ0xMdUM1YU43TGQxdHVQSVpUNU5SWkhrUSthSHc2V29LSW5ZS1lSWGc3NmZ5NHNLaXFRNEtFWVVLY3RHR2NzUUVEMzRmVVBnK0poZzRNT0hzcCtkQVV2eGlKY0FVUWFXaWFDeWxxMm40Z1dteHFSS2hDMTRqQllJd2lEbEdtdHkwS2cwamVoa2V3WHpwNU9JVEc3NUViQUxDeWVPVW1hWUFFSkRsV2pzRlNEYTAySkNVK1RONC96eFJNOEpvOHZCbDhWcFpYakpKMVc4QVRLc3cwb29zVHppL2NTZmVTTXg5UW9BY3JNOEQyZGI4OGx5UThJVG9VRzdsRE8vTUlpZFYwUmhieE8iLCJtYWMiOiI1NGFmMzRhYjllYmM2M2FkNjc3NDQ0MzgxN2Y0MTQ1ZmUzZTA5YmNhZTdjNTVhYzFhZGU4ZTUyODFkN2JiMTRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1924789836\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1113410503 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1113410503\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1552825822 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:42:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtHRXFvNTFwR1lDWU53UVo0bjR3VWc9PSIsInZhbHVlIjoiYUs5bHVBT3BQMkZQYmhCbEJFQ2xSN1BuTlFaVzByTDg2OFdkakc4eFd5Zk5vZnRRZG41czJ3S09JNHQzcVhDRThDTkZVZURVdERKU0ZtNVJkMjdvdzNrSkt5NXY5TTVpZEg4ZVVRMTByNy83dm1CVExMeTd0L01oakNyM1RsQUNLbWNkOXNTT0hpODdDQjhUOEZIUld0dmo0WU1xUDIvWUprQjAwelZPTXhob00vakpSNkRiRXNJMERWc0QwanEwWlJoMkFOeHA3Nk5FOFZYa3VaZzU4RlpIV1ErRHFJQ3kyUkJrdy9sZHJPWGdPQ0hhTER1bVdjK2tQME1jMXU1TmtkTW8zV1cxWmY5RnVSQVlrWTV2RjRkYkdCUHduM3dyT0M0VWVpVEc0Q3ZULzJ2c05lTGFDejNSdENMLzV4NGx0N0U1WUk3ZnZ1TVBTelExYW4rdlBkZy95NHIwd29BSTVrZVptckt4bVpXUjNSdTdlbGlsY1gvbFJiWTA0bG95V1hQL3VoUW0vL0p6dDZjeS9kUzcyTnplVmtialFzTzhSSEhrRmc0QTVKZ01jT3Nta1NxT2p6L2p2Q0hjaW9YcGNDdWdDQ2ZrQUJZUlAyMnR4d0ZqZGtOdUhmRjE4N1ZEd0JUc2NZWnZUQkpnV0RtMG1ZcVhiQzRBK0tIMDY4UWkiLCJtYWMiOiIzZGViOTBhMjNkNTZmNTZkZTgzOWFmMDE2YzZkMDExODhlMTI2NjMzNjY1ZTcwOGVlOTFmNzcyMmNmN2QzYjY5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:42:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjlQbS9GNUtGRVd1VDd5R1VNTGZzREE9PSIsInZhbHVlIjoiL1ZLZlJEWitjSUV5aWxrS3NGYVV5T1RiZDd2YVVEejM3ZGNJMXllS0VBSWUzUCs4K3MwZy9XRE1uZi92VmJHK0NaaTdQWVhKV1hLLytUT2sxS082NzVjTm1ueUVQL3hVSjZjTUsxYmxQYUVrUEhGd2pFa2VJOStBSUNFSDZ5cnpzUkdKcy92bVE1ZzBLQjJSdis1Si9nelpzR1RMMGl1ck44MFQ3bzlETjNBTWpLUVhrWHdKU0ZHMHllWDJVYkRNR1lmd0E0NmJXTldlWjFUNkZiWkthZ2NrZU9LKy9lZENGaXdCUUNFOUJlSWFoc1htT2wrYVZBaFRRdTZackVweS9xQXVkYXp6SFVVWHF3WXhIWCtVYUliVlNyR1V6eE5UZEliUkZFUFdJVjBqWmllZTk4NkpEZEczMGlBTDFFSFJuRkFZMGxXZ2NCQ2h2cDY1M2tSd0Z1T2sxNS9VeDVYV0cvSDVmWEJYMjd3Q1h4WE5FZmR3RzBIY2xFWEdKT0JsUGNTUGV0alcyZHdkZnE2S1ZkUDFLdTVRbE9mY3U3R3MvTlJjRnMyNGhGRTNpK0tjMDUwakZDQ2R2MDVxdSttdGxEd1VUS0VVb1FZZ2lITEJ4YjNVUDQ3NjRrZ2FKSVNQVE93UXI2M3pFaFppV1creDJRMld3WW13Yk85cHYwZE4iLCJtYWMiOiI0Zjg5N2Y0NTg1YTY0MWZjN2IwNWZmY2QzMjBiZGNhMDIzYjE1OTBjNzk2Nzk2NjViZDUzZDhiMmFkOGU3ZTI1IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:42:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtHRXFvNTFwR1lDWU53UVo0bjR3VWc9PSIsInZhbHVlIjoiYUs5bHVBT3BQMkZQYmhCbEJFQ2xSN1BuTlFaVzByTDg2OFdkakc4eFd5Zk5vZnRRZG41czJ3S09JNHQzcVhDRThDTkZVZURVdERKU0ZtNVJkMjdvdzNrSkt5NXY5TTVpZEg4ZVVRMTByNy83dm1CVExMeTd0L01oakNyM1RsQUNLbWNkOXNTT0hpODdDQjhUOEZIUld0dmo0WU1xUDIvWUprQjAwelZPTXhob00vakpSNkRiRXNJMERWc0QwanEwWlJoMkFOeHA3Nk5FOFZYa3VaZzU4RlpIV1ErRHFJQ3kyUkJrdy9sZHJPWGdPQ0hhTER1bVdjK2tQME1jMXU1TmtkTW8zV1cxWmY5RnVSQVlrWTV2RjRkYkdCUHduM3dyT0M0VWVpVEc0Q3ZULzJ2c05lTGFDejNSdENMLzV4NGx0N0U1WUk3ZnZ1TVBTelExYW4rdlBkZy95NHIwd29BSTVrZVptckt4bVpXUjNSdTdlbGlsY1gvbFJiWTA0bG95V1hQL3VoUW0vL0p6dDZjeS9kUzcyTnplVmtialFzTzhSSEhrRmc0QTVKZ01jT3Nta1NxT2p6L2p2Q0hjaW9YcGNDdWdDQ2ZrQUJZUlAyMnR4d0ZqZGtOdUhmRjE4N1ZEd0JUc2NZWnZUQkpnV0RtMG1ZcVhiQzRBK0tIMDY4UWkiLCJtYWMiOiIzZGViOTBhMjNkNTZmNTZkZTgzOWFmMDE2YzZkMDExODhlMTI2NjMzNjY1ZTcwOGVlOTFmNzcyMmNmN2QzYjY5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:42:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjlQbS9GNUtGRVd1VDd5R1VNTGZzREE9PSIsInZhbHVlIjoiL1ZLZlJEWitjSUV5aWxrS3NGYVV5T1RiZDd2YVVEejM3ZGNJMXllS0VBSWUzUCs4K3MwZy9XRE1uZi92VmJHK0NaaTdQWVhKV1hLLytUT2sxS082NzVjTm1ueUVQL3hVSjZjTUsxYmxQYUVrUEhGd2pFa2VJOStBSUNFSDZ5cnpzUkdKcy92bVE1ZzBLQjJSdis1Si9nelpzR1RMMGl1ck44MFQ3bzlETjNBTWpLUVhrWHdKU0ZHMHllWDJVYkRNR1lmd0E0NmJXTldlWjFUNkZiWkthZ2NrZU9LKy9lZENGaXdCUUNFOUJlSWFoc1htT2wrYVZBaFRRdTZackVweS9xQXVkYXp6SFVVWHF3WXhIWCtVYUliVlNyR1V6eE5UZEliUkZFUFdJVjBqWmllZTk4NkpEZEczMGlBTDFFSFJuRkFZMGxXZ2NCQ2h2cDY1M2tSd0Z1T2sxNS9VeDVYV0cvSDVmWEJYMjd3Q1h4WE5FZmR3RzBIY2xFWEdKT0JsUGNTUGV0alcyZHdkZnE2S1ZkUDFLdTVRbE9mY3U3R3MvTlJjRnMyNGhGRTNpK0tjMDUwakZDQ2R2MDVxdSttdGxEd1VUS0VVb1FZZ2lITEJ4YjNVUDQ3NjRrZ2FKSVNQVE93UXI2M3pFaFppV1creDJRMld3WW13Yk85cHYwZE4iLCJtYWMiOiI0Zjg5N2Y0NTg1YTY0MWZjN2IwNWZmY2QzMjBiZGNhMDIzYjE1OTBjNzk2Nzk2NjViZDUzZDhiMmFkOGU3ZTI1IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:42:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1552825822\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2026444624 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2026444624\", {\"maxDepth\":0})</script>\n"}}