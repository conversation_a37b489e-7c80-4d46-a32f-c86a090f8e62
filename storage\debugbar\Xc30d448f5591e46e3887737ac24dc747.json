{"__meta": {"id": "Xc30d448f5591e46e3887737ac24dc747", "datetime": "2025-07-29 05:30:28", "utime": **********.927925, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:30:28] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 84,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.923166, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753767027.7634, "end": **********.927957, "duration": 1.1645569801330566, "duration_str": "1.16s", "measures": [{"label": "Booting", "start": 1753767027.7634, "relative_start": 0, "end": **********.694318, "relative_end": **********.694318, "duration": 0.9309179782867432, "duration_str": "931ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.694372, "relative_start": 0.9309718608856201, "end": **********.92796, "relative_end": 2.86102294921875e-06, "duration": 0.23358798027038574, "duration_str": "234ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48263616, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.032830000000000005, "accumulated_duration_str": "32.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.773123, "duration": 0.022670000000000003, "duration_str": "22.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 69.053}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.81738, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 69.053, "width_percent": 2.802}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.826227, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "omx_sass_systam_db", "start_percent": 71.855, "width_percent": 6.458}, {"sql": "select * from `ch_favorites` where `user_id` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.832861, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "omx_sass_systam_db", "start_percent": 78.313, "width_percent": 2.041}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.854206, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "omx_sass_systam_db", "start_percent": 80.353, "width_percent": 2.711}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (84) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.867713, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 83.064, "width_percent": 3.076}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (84) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.876866, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 86.141, "width_percent": 4.112}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.883625, "duration": 0.0032, "duration_str": "3.2ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 90.253, "width_percent": 9.747}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 543, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 546, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-736702235 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-736702235\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-541096529 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-541096529\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-749495799 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-749495799\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-872278969 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/leads/14</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6InBKVmxsTmxSNGY5cmFUMXFQZWk4bmc9PSIsInZhbHVlIjoiNm5YR0s0M1VIc0J4OEhBalhxeVY1cWRXWmlVR25uQ2kzRHIzeUNkV1hRYzhUd3c0WTMrTEFOZG53M3hSUkR6OEN3WXEvRUtKMENFZWx5U1YvdWVnNzBYeGp3QlY0eUdxeUNlYWgxOWFQR0lFcHEzTWlOalZmQkZqSFlqNHgzTVBWemJOMVBKb0tsSk5VWnRzaktUdVJEbnhJUC81OEwrcFdkdUxLLzVBNHNoTVkwclJNUDEvOWxvRWhvTzlPdXA4Mno3OXlLbkcrbzJlQXdRUTNQU3RJeE9FVDVPcHJGYjRMUzVteXo2am1sL0t5S1p2K0NNS1JBZ24zSFNkNWZCWGgyNDBTMEFlbnhHaFZBd3pIbnNKV2dORXFML0tFZzFFd0ZkZHo1SHlYazZhOXJzU3ViOGRtMStxYVpUN2xTb29kUytZTlBVdi96RWFJcjNqaWNOcFNOWnN4YzZJKzRXNGo3NWgySlYwcmh0NFBEREpEZjU1SUJWOHNkTkJsd2FCQXlkNHQ2bzFRYVl2RFhNSjl4VURrWTFNQjVCekdjMEJzZGFLV2VDdEZGSVd0SlBnVk12R21rVkNwVDJBbmEyRVBjNHRKQmZQZlp5N0hvZ1IwdiswakttbTNraWNHaG5MYnRkM3pFbW51L2d0bFp0UHFTa0hWVXZXdFFJcmxyd0EiLCJtYWMiOiIwNWFkNWE1ZjVjY2RhZmM0ZTAwZmNjN2M5NzMxODBhMzJkOWQwNjMzZGRiYWI4MDZhNTkzZDMyNGM0MjY5NjMxIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ikh2cUcxSFpjMmd2K1ZrcUljQjF2Rmc9PSIsInZhbHVlIjoieEtqUjlXbVJqYVJhZjR1TytzM05vdFVITy82N21ncDNTSjN5T0s5emFieDdDZml5Q09ISkVHQlNDTjcydVlEL3UrdXh1bEI3NDdjZHUxQXpyek1SbjJqQ25IUHZuVUlLdFRRQlQ1OThpOWoxU2Y0dkc1QncwWTd6bHZ4cmw1RDdDT3Zkd1RJaHNhei9ObGNkajFmV0owZ0k3dDIrUzlScjFyU3RCbGd5Vmw1WmZwY1dvQzl5VFE3Q21sc2xJRnVmUUs1TTh2S0w3Mk0vS2dtdDYvd0wvTkowcWZXY2VHc0lPaHNIN01CWStRaWdtS2duV0ZSNlkzSDM2TlJXQklHQy9JYmhpajEveVZ0R3NhcS91Zm92S0pUNDVsZXowbEE1aHZ0TnhRTXc3UEtvOUw4MGpQWjZ0TTEvYm8xQ3ozMks0QW41Mnc2b0hoU05kRkVnUUM2WnJoN25zdXJOeHV5YjB6MDNaWWpITkhHSXQzMUdoY0FXQzNZMjRMWjdkc0t4M1U5QnZWVWhUK1JGYmdQaHc4L1V6SnE1VGVCYXZQN0YxRlZvZ0tyMWtSYUhKaHk3ajlteDJaamVsT3FFd2xuNGlPUkJlZkNGYXR2V2gwSm1TUS9CbXg4K3l3RjNhajZjWm1laVZRUGU3YUZRL1hOWTdCcjRvaG4zRVVrd09oMDQiLCJtYWMiOiI3YzQxMjY4NGQ5MmRiNzRjZTE4NWYwM2Y5NDU0YjAyMmNhNjJhNDVlOTI0Nzg0MjdiN2QzNTQ5ZTYyOTkyYjllIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-872278969\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1292956572 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1292956572\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:30:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inl2MWpmVzkzNHlzZW00ekdoSGtmZ2c9PSIsInZhbHVlIjoiWGNFamRYa3JGR0NMM05FV0lSMlRLOE0zcGw2bWppRVVmYWszc3dTVUQ1aWNHQkd6ZDMzWFl0V3BmNlRkc25yVUVzeGxxbFVKS1hRRFJLZEJTYXp3SWJyckhyY1ZDRmxOR21tSTdGL0xWb3kzc1doSGphcWh0V2hoSEpKbWNKeWpLMklvYTczbUlGamVWbnB0REc3V0d2M2o0N3FDUzE2djhxdjM1bVpzT0ZTYW5QdlYxZ1hWQW1yWkpzd1kxUmNlRWFwYlgxc1FIQ1NkeXZiTjUrREsxSWI2aWp1NXBxcXFvc0pGTDROblBQWWFsTVd4akUrQlVZWXZLZjRVQ2R3RE1PbGpCR0xyaThCNzM5Sk42QnFXNmljbjZnMlAwZVdYNVJYbTl6a2NrL1NZOXlnYk96N1hFNUJwVmQ1L0VCSWt6MGRPNEtncU5XV1p4NWxTR2xMdG5hcXJhb0M4dDNyZFlvWnl0TzF6YnIxOVZhYjhHa3hLRVlqWnF5NEJYaDAyVmdaMEdCQzBSNUJQVHRKcEhENytXaXVJWnFUT3RZMnFQY3daL0NENndaNDVEblNieWN1S2tBdmZBbXJQNjdLUWl3TUJmSUVmQ1dqVlZ3RWlobXZVVTZTU244aGpXdU51NW1aWERibkJiSWZ0MFJJRkpUaXZ3VnZjKzZ4MElVZnciLCJtYWMiOiJjZDhjMzY2YTAxYjNiNTBhYTdlNDIyYTc0YTgyNTNlMDYwMmQ1NjY3ZWE1YzZjNTY0ZWJlYzliMThjNmVkYmVjIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:30:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImxvL25VK21Ia29xTmlDWFJOS0o3UlE9PSIsInZhbHVlIjoiQkUvWlhYa0J3UHphMVBFNGRWOG4rUGJRdml1WEFuZzBKM3I3c0NKMDFkV09kQmdYa0M2dWdCVWVZZVhTVHUra29VMWR3cC9MWUtMMThpNWhNUUVKc3JaNGxJUUQxY0w1REpQRTB5MlFPalZjQmU3Wmcrcjk2dlY2T3hOS1FhY1lRYk1zbkN3a01GcU1VazRyS0hSSG9udWcxSmhxN3paNjVCcVhreGcrbVMrQkxsQlJNUkFXSzZNODlsTHpBOU1WNUtpODkvdVI0N0gwcmxiTGhUdTFYYkZ5ek91dEdrUElHQi9PMjBWbXV2ejN1WFdBdmtwVHlhOFZwbmpTd0xtOGRzN0NQVmJHZ0NQWHJzM2ZidWRCMkpOZXRFY3poZHI3cTg3NEF3VjNqclU4M2VCTGhSckVKUHB3d09ybWx1T0xaYXpsKzlUSkROUlViRlBudDVxZGIxcnZrYWxXS0syekhLWitYeVVLMDBoTi9VTFNIVi81L0w3T25FY1lwbmg1NUZlWkJMMUljTzVTblhZbE5rakkwcU9uL29KUVdWa3Vqc05lQjZwREpaaDBFNEtjdHlSYUhKaHplcFk5L3FOL2hZWVUzbUxjZ3BEVUQ3Z05XN3NjSlVBMzVXRnRXUHRPRDBtK1J2dmtoeG1IWE5oUGZsNmk2QkFwK2Z2RGtxTE0iLCJtYWMiOiJlZDNlZjgzNDllYTUyNzliMDVkZWU5MDIyOWUzOWRhNzNkYjlmZGJlZmRiYWIwMjNlYzRiNDg2N2NjNjU3NjQxIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:30:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inl2MWpmVzkzNHlzZW00ekdoSGtmZ2c9PSIsInZhbHVlIjoiWGNFamRYa3JGR0NMM05FV0lSMlRLOE0zcGw2bWppRVVmYWszc3dTVUQ1aWNHQkd6ZDMzWFl0V3BmNlRkc25yVUVzeGxxbFVKS1hRRFJLZEJTYXp3SWJyckhyY1ZDRmxOR21tSTdGL0xWb3kzc1doSGphcWh0V2hoSEpKbWNKeWpLMklvYTczbUlGamVWbnB0REc3V0d2M2o0N3FDUzE2djhxdjM1bVpzT0ZTYW5QdlYxZ1hWQW1yWkpzd1kxUmNlRWFwYlgxc1FIQ1NkeXZiTjUrREsxSWI2aWp1NXBxcXFvc0pGTDROblBQWWFsTVd4akUrQlVZWXZLZjRVQ2R3RE1PbGpCR0xyaThCNzM5Sk42QnFXNmljbjZnMlAwZVdYNVJYbTl6a2NrL1NZOXlnYk96N1hFNUJwVmQ1L0VCSWt6MGRPNEtncU5XV1p4NWxTR2xMdG5hcXJhb0M4dDNyZFlvWnl0TzF6YnIxOVZhYjhHa3hLRVlqWnF5NEJYaDAyVmdaMEdCQzBSNUJQVHRKcEhENytXaXVJWnFUT3RZMnFQY3daL0NENndaNDVEblNieWN1S2tBdmZBbXJQNjdLUWl3TUJmSUVmQ1dqVlZ3RWlobXZVVTZTU244aGpXdU51NW1aWERibkJiSWZ0MFJJRkpUaXZ3VnZjKzZ4MElVZnciLCJtYWMiOiJjZDhjMzY2YTAxYjNiNTBhYTdlNDIyYTc0YTgyNTNlMDYwMmQ1NjY3ZWE1YzZjNTY0ZWJlYzliMThjNmVkYmVjIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:30:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImxvL25VK21Ia29xTmlDWFJOS0o3UlE9PSIsInZhbHVlIjoiQkUvWlhYa0J3UHphMVBFNGRWOG4rUGJRdml1WEFuZzBKM3I3c0NKMDFkV09kQmdYa0M2dWdCVWVZZVhTVHUra29VMWR3cC9MWUtMMThpNWhNUUVKc3JaNGxJUUQxY0w1REpQRTB5MlFPalZjQmU3Wmcrcjk2dlY2T3hOS1FhY1lRYk1zbkN3a01GcU1VazRyS0hSSG9udWcxSmhxN3paNjVCcVhreGcrbVMrQkxsQlJNUkFXSzZNODlsTHpBOU1WNUtpODkvdVI0N0gwcmxiTGhUdTFYYkZ5ek91dEdrUElHQi9PMjBWbXV2ejN1WFdBdmtwVHlhOFZwbmpTd0xtOGRzN0NQVmJHZ0NQWHJzM2ZidWRCMkpOZXRFY3poZHI3cTg3NEF3VjNqclU4M2VCTGhSckVKUHB3d09ybWx1T0xaYXpsKzlUSkROUlViRlBudDVxZGIxcnZrYWxXS0syekhLWitYeVVLMDBoTi9VTFNIVi81L0w3T25FY1lwbmg1NUZlWkJMMUljTzVTblhZbE5rakkwcU9uL29KUVdWa3Vqc05lQjZwREpaaDBFNEtjdHlSYUhKaHplcFk5L3FOL2hZWVUzbUxjZ3BEVUQ3Z05XN3NjSlVBMzVXRnRXUHRPRDBtK1J2dmtoeG1IWE5oUGZsNmk2QkFwK2Z2RGtxTE0iLCJtYWMiOiJlZDNlZjgzNDllYTUyNzliMDVkZWU5MDIyOWUzOWRhNzNkYjlmZGJlZmRiYWIwMjNlYzRiNDg2N2NjNjU3NjQxIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:30:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1807747763 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1807747763\", {\"maxDepth\":0})</script>\n"}}