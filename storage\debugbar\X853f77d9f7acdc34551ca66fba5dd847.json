{"__meta": {"id": "X853f77d9f7acdc34551ca66fba5dd847", "datetime": "2025-07-29 05:41:12", "utime": **********.573474, "method": "GET", "uri": "/api/leads/13", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753767671.437418, "end": **********.573496, "duration": 1.136078119277954, "duration_str": "1.14s", "measures": [{"label": "Booting", "start": 1753767671.437418, "relative_start": 0, "end": **********.446405, "relative_end": **********.446405, "duration": 1.0089869499206543, "duration_str": "1.01s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.446418, "relative_start": 1.0090000629425049, "end": **********.573499, "relative_end": 2.86102294921875e-06, "duration": 0.12708091735839844, "duration_str": "127ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46062112, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/leads/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@getLeadForPreview", "namespace": null, "prefix": "", "where": [], "as": "api.leads.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=440\" onclick=\"\">app/Http/Controllers/ContactController.php:440-476</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.021660000000000002, "accumulated_duration_str": "21.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5022511, "duration": 0.01856, "duration_str": "18.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 85.688}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.539632, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 85.688, "width_percent": 3.832}, {"sql": "select * from `leads` where `id` = '13' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["13", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 446}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.544478, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:446", "source": "app/Http/Controllers/ContactController.php:446", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=446", "ajax": false, "filename": "ContactController.php", "line": "446"}, "connection": "omx_sass_systam_db", "start_percent": 89.52, "width_percent": 4.155}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` in ('')", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 446}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5519412, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:446", "source": "app/Http/Controllers/ContactController.php:446", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=446", "ajax": false, "filename": "ContactController.php", "line": "446"}, "connection": "omx_sass_systam_db", "start_percent": 93.675, "width_percent": 2.909}, {"sql": "select * from `pipelines` where `pipelines`.`id` in (27)", "type": "query", "params": [], "bindings": ["27"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 446}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.557098, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:446", "source": "app/Http/Controllers/ContactController.php:446", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=446", "ajax": false, "filename": "ContactController.php", "line": "446"}, "connection": "omx_sass_systam_db", "start_percent": 96.584, "width_percent": 3.416}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/api/leads/13", "status_code": "<pre class=sf-dump id=sf-dump-988594225 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-988594225\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1264261377 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1264261377\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1478575112 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1478575112\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Ikt0QjMwcnVMRHhnZEI5bFg3R1RmQ3c9PSIsInZhbHVlIjoiQmU2dUE3ZGJaS2R3N0NIM0ozSitqTWtZUGYzRGFMMU83WkZOY0tZaytJNUVmNjY1R3VZWTJiZlRJUEJudCtEZzJIU3hNTThlcjd3ZEdjRHhnSHFkQVB1dU9zd2ZObTB6Y3BPMHBLWDNBK1o3NENqeDk5MGVjaWpndHNtRW9QWDlnckVzYWxNZS9MZUhVQk5DRWhsZFU4eWp4S05UZ0ZBa3U0dTJUSWZzc1RIR1JqK3VrdVd6SzFENVlROXdFeWxBMXkzZzVNZUJobUNteXgxTzF6bEpYYVZTVGxybjZWL2ZWK09lQXZPNGRBUDl2TXVRMVY5SUhXMVhVemlmemRUMnVqZmlGTWI2UUlnekROZk41WWtiUThtQXZSd2F4NHhYSjE0RDAvTElzbXAvSE1EZHVCZzI3RlhIYWpnL0FkdnVDYUJqKzRLZ0JtSERPZnZ1K25iUS91dC85V2cvQmc2M2lZQzQ1WUh6OU5WL3F0QU5ha29VMEczNW5XRFJiMjJ0ejd0UUFxYm9hTGZjT2JpazRwWHdYSmJ5SlMxWGZYb3lERWEwR3NMZmY2ZmNtZ2FkUEFRdEZKYjdaMHU2TktvYlRxZmNkSHdaUHV4Q2RyZ0Zxb3FQSlg1WlU2MHhCcU00c1pnTit4ZjhPVE55VGVhSXM4NldyMEtOUVdDTHJhK28iLCJtYWMiOiI2YTZiYjhjOGJhNTY4MWM2NGMzZTdmNzFkOWUyOWVmYTI4MzZhNjEwMDA4M2Y0NDkwYzkwMTAwOWZlYTcwN2QwIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InVCblVCZkd1SnlvVWFWdmpKbmVOSWc9PSIsInZhbHVlIjoiL0FIT2d3ZUpralRMZ0RvMHZVVkJ3bHZVanNCY2tYNWw5ZFBIMkxocmQ3OWtoZm9hVmpHNmFXcEJET1hLdzZ6Vk1SK245dW9BNFJURm5nMzBqZEIxL3A4OXRXNzFKY1VsQTNjTlgrMTdtNGVZejNGR3ZxS2gvNjRBeGpnYzViSkxhY0pCaVR1bVpNcW5kaldRSXpBYXhzM1AxZTBqY3dRUDNnMGs1OWtyS1hSZm1lSjRiZ2UrVWF3cFlTTFJVdy9GUFZBcjFTT2xQM0pyeVdEVU9ZSmpBMG5zWXhvTGtSR1JIaDNCRWlPcDdRZmdDbFZ6aEZzZDhoMDZQR2NtZFAzd1lJdlBORmUrUUlta1Y2MU1HWUcxcnVWcERLa1BuNHF0QzAzOFVocXE4NVNSeDBGV045a1JEaE5mYmoyK2NxNGU4MzBDU3czYXh4Y010RTI3WDBJamc3OGlVMWdZM0VKb1UxYUVXSk9WckUvNUlLeGZlRW8zcWwzR3l6Tms1RCtrR2ErLzVkWmdBQTVRTkcwcWthRkFrVUh1UGNTMG9uc0tSWVphbTgrOE9sMUsyV3FsV2dsQlptOUxEMkIrV0JIdFVoRlhqSkV4OGZOLzVEM085OW4zb1lRbDIwQlh0YXJDNUgwWnZTRy9rb2VRUER1N1dkckNQYzl6WjFGUjI5WkYiLCJtYWMiOiIzMGQ1MGRhYzkwYjNhOTk5YzUwZDgxMTMzMzgwOWIxMzQ3MWRhYjBiMjQ1YjRhMjc1ZTAxMmQyNTEyMGRjNGRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:41:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZoa24xU0FvZ2s1bkRUY0pubktZSmc9PSIsInZhbHVlIjoiK3NkZSswMzV6SXFCZzZrbTRjQzhmV1Ewc0UwSTlRbXd2Q1NubVNpaDJJY0FCUW8xeWwzZWdDNWVKNUxCQ1dlYWJXUkZXQ2haREZXbnJxOWtVUU12RWtNLzZBRk5NZjhhTk0xeml4MjJwWkVlRWJ1bjNCMGxLaW8yR3EzeUxENDZCV29CY1UvUk15NjVLMENLNm9MRE5iZEdUZURsSW5XYllXWTJpTkJGN0ZLbUpCeEFiNW1DSEdtRmJ5ejBUcUZ6Qm1pN3BKZWs2Y3krQVZFaGZIV3BpVlk4QzZna28rclFEVm9SNVhJL0RXVWZHL3k3VmEzQSt2ajcrUHhmejBaOFVFSEZFU0N3M0JnYzdkM1MwRVVnZjhHakNPa2tnUVQ2bXRocWIzc3JPNE02SVMyTEdHNlUvQ0krRDl0SnpFNmJWS3AyT1JMWWovVlZDa2JUUGRhMjVvd0phNS9hYnhOZjRPODJScWd1TjRLYUNmK2x4dlZ4YUJzZHREbitBU2hNZmltemZwd0dOek0rQ3hNZkhNMGRGZEc4MWhvbWpiSitZV3JQcVlXaHJ1clNxd3B2SlFJNEdVUVJzQ3VQR0JEb2w2bnhFYjRhQUxyRlZWQUVOakpCYUhtMGdCODJwYkVzUXNuaytRYTNNQkI0SzBRbTVmMlk2cGhUN3FrNllvREgiLCJtYWMiOiJlOTAwNzkxODZiNmVjMmVlN2ViOGNlZGVkNTFkMmIwZDk2YjVlNzlkM2ZhODQzZjVmNzdkYTAzMmM3NjJlM2JhIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:41:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImpCbzEvcUV1dUJxV1ZHMVNxQ0I4R1E9PSIsInZhbHVlIjoiN1gwUnhqcm1KajRmTWtzTXdwbS9tWEc0REJ2blM2VWJoZnhnUzBCblhOTlRpL2M1LzJYY0hlZmlacHdmeEN5TFdteTJFV3dvdFJYS285YThibjJBb1NOamppcENvcFErZUFXNklGOHdlV0d6VXZFNS9PcUFYc1BGRHVVMmpIRThyWkhPS2hiUTZZcDlsaU9iWk1iYWdZRkJTNS80ZnBObGRzWTJGeEVpbE9qTXJROXJucjYvR3N3MjFBNlVOZitRdXVRbnNWb2J0SU94a2IvY3dhTGlSOFRydWtmYTdMNHh6dmFleit1bzdFY3c5Zm9laXZnd3lnSFhZdUkrbTR4RzBJSjlSSVdvRXE3bUNXTHhHa2RrY3ZJKzZqSjVvVFJLLzUvVzBhWjN0SUhtS2lZa0EwK3ZmVkgyL2ZROU1hdFl6RktCZUN0M3RibXc5VU1MOHpXYi9RU2pSekcrc2hjZmxiUVVHWDJqdGRySWxBZnBEY2ZndG9TRUhLOVY5NXJoRmw4MDYxZ2lVMWpDdFVWQzBvWlFlYXE4Z1lIaEM0NXh0WU9iOW9sa05ENmVDOGRDQnlsMlljNnplM1NidUNuV2NOUW5UUXRSWVJrZUhHVmptMkN6THBFVm1RY1haSHEwUmRWMlAwSkoyTDVISnkwVlYyOWpLSmpiY1haWFEwQnYiLCJtYWMiOiI1NjU4MDJhNjJhYTM3MzFkZDJlMDBiNWJiNjk3MzBhYTRmZjdiZGExNDFmNzdhMTQwMzNiZjI5ODBhYmVkNmFiIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:41:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZoa24xU0FvZ2s1bkRUY0pubktZSmc9PSIsInZhbHVlIjoiK3NkZSswMzV6SXFCZzZrbTRjQzhmV1Ewc0UwSTlRbXd2Q1NubVNpaDJJY0FCUW8xeWwzZWdDNWVKNUxCQ1dlYWJXUkZXQ2haREZXbnJxOWtVUU12RWtNLzZBRk5NZjhhTk0xeml4MjJwWkVlRWJ1bjNCMGxLaW8yR3EzeUxENDZCV29CY1UvUk15NjVLMENLNm9MRE5iZEdUZURsSW5XYllXWTJpTkJGN0ZLbUpCeEFiNW1DSEdtRmJ5ejBUcUZ6Qm1pN3BKZWs2Y3krQVZFaGZIV3BpVlk4QzZna28rclFEVm9SNVhJL0RXVWZHL3k3VmEzQSt2ajcrUHhmejBaOFVFSEZFU0N3M0JnYzdkM1MwRVVnZjhHakNPa2tnUVQ2bXRocWIzc3JPNE02SVMyTEdHNlUvQ0krRDl0SnpFNmJWS3AyT1JMWWovVlZDa2JUUGRhMjVvd0phNS9hYnhOZjRPODJScWd1TjRLYUNmK2x4dlZ4YUJzZHREbitBU2hNZmltemZwd0dOek0rQ3hNZkhNMGRGZEc4MWhvbWpiSitZV3JQcVlXaHJ1clNxd3B2SlFJNEdVUVJzQ3VQR0JEb2w2bnhFYjRhQUxyRlZWQUVOakpCYUhtMGdCODJwYkVzUXNuaytRYTNNQkI0SzBRbTVmMlk2cGhUN3FrNllvREgiLCJtYWMiOiJlOTAwNzkxODZiNmVjMmVlN2ViOGNlZGVkNTFkMmIwZDk2YjVlNzlkM2ZhODQzZjVmNzdkYTAzMmM3NjJlM2JhIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:41:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImpCbzEvcUV1dUJxV1ZHMVNxQ0I4R1E9PSIsInZhbHVlIjoiN1gwUnhqcm1KajRmTWtzTXdwbS9tWEc0REJ2blM2VWJoZnhnUzBCblhOTlRpL2M1LzJYY0hlZmlacHdmeEN5TFdteTJFV3dvdFJYS285YThibjJBb1NOamppcENvcFErZUFXNklGOHdlV0d6VXZFNS9PcUFYc1BGRHVVMmpIRThyWkhPS2hiUTZZcDlsaU9iWk1iYWdZRkJTNS80ZnBObGRzWTJGeEVpbE9qTXJROXJucjYvR3N3MjFBNlVOZitRdXVRbnNWb2J0SU94a2IvY3dhTGlSOFRydWtmYTdMNHh6dmFleit1bzdFY3c5Zm9laXZnd3lnSFhZdUkrbTR4RzBJSjlSSVdvRXE3bUNXTHhHa2RrY3ZJKzZqSjVvVFJLLzUvVzBhWjN0SUhtS2lZa0EwK3ZmVkgyL2ZROU1hdFl6RktCZUN0M3RibXc5VU1MOHpXYi9RU2pSekcrc2hjZmxiUVVHWDJqdGRySWxBZnBEY2ZndG9TRUhLOVY5NXJoRmw4MDYxZ2lVMWpDdFVWQzBvWlFlYXE4Z1lIaEM0NXh0WU9iOW9sa05ENmVDOGRDQnlsMlljNnplM1NidUNuV2NOUW5UUXRSWVJrZUhHVmptMkN6THBFVm1RY1haSHEwUmRWMlAwSkoyTDVISnkwVlYyOWpLSmpiY1haWFEwQnYiLCJtYWMiOiI1NjU4MDJhNjJhYTM3MzFkZDJlMDBiNWJiNjk3MzBhYTRmZjdiZGExNDFmNzdhMTQwMzNiZjI5ODBhYmVkNmFiIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:41:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-952928395 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-952928395\", {\"maxDepth\":0})</script>\n"}}