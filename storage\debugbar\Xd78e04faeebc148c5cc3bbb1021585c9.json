{"__meta": {"id": "Xd78e04faeebc148c5cc3bbb1021585c9", "datetime": "2025-07-29 05:42:10", "utime": **********.94683, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753767729.828297, "end": **********.946876, "duration": 1.1185791492462158, "duration_str": "1.12s", "measures": [{"label": "Booting", "start": 1753767729.828297, "relative_start": 0, "end": **********.870758, "relative_end": **********.870758, "duration": 1.0424611568450928, "duration_str": "1.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.870788, "relative_start": 1.****************, "end": **********.946888, "relative_end": 1.1920928955078125e-05, "duration": 0.*****************, "duration_str": "76.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "SugqqTJ7ECKGbz4cQ6JiI7Lva8WKND7ROtTVAff3", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-104361484 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-104361484\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-679230646 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-679230646\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-414336831 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-414336831\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-71790047 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-71790047\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1999311991 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1999311991\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-52255675 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:42:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjE5aWJRdFJPTjcwUEVlRmN0NUdlL1E9PSIsInZhbHVlIjoibTAvUUFoVmdkVVZYUjlaek1ReHZPZkhhemdkdXNYY1Noelo4UDNNSWcwdFZsdVI5MFhuaThGNmZCVjdDcXphNFB2Q2llZDR3T2N5REt5VnJtTlNiV1Zqc0pkYWRpYWlyWmErc0U4dzhvTUFpY0kwREJxSUhsTGVRU0puOEg4ZEt1WnJOZ1Bvb2pSQWlOVFE5Z1JZRGpkb1FZQ1lGcTRqVjBQTE5DeTR0UkYwUlQzbHZmcGpkcGZhN2dNOWhnVThxV284NStDbXAwWk5RWGVKa0hGenRLT0hxWmFzZWk3MS94ajZMejJTeEZVYnBMR2tQNlEvZFdOeVRIeGwvVDVBNE1jMy94SGdiWmZMOGFtS2x3ZFBTQ3FkVDJLTUhyakhTdVB1UkFXaEdnY0F0Uk9HSEltRXc0Y0djMm1IdzRjUHRPUU1MMVhwVENBNFBTajNNMkZrWExYZnFxSjdhUTVXMm9ESC9rei9BMFppZXU1Vm4zQlAwaENmTXlNU2p3d01lL0JKMVJCZzBYRkY3SGQyNkhpUWNqMC82ZU9nZFJpSW02WWxyYmVUNlUwRS8wZ09OT3p1Sm5nbXg1WWU3N21Bd2NCTjVQa1JrKzhDcW9Zelk3RTJTSmJCN2t4NkQ0dGNUNml0akdNUFhoVllGWStEWXd0bGxxL043czlySTNUWEUiLCJtYWMiOiJjZTc5NmVmZWJiYTZmZjMwZWEwNTE5OGM0NTFkNzNkOGZiN2ZkZWMxNTdiZGFlZDJlNmQ0YjQ3NGYxZGExNWE2IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:42:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkVlTCtrYXg5U0x4S1dKUDB3ZU5IQ0E9PSIsInZhbHVlIjoib0tNTitaaXFqQWlobXNGelJ0UTdEcTl2UGRERFR1bXFxYkdRQ3pzSXZZcHorUnNFTVp4WG1NLzVMTGt1MjhzdStJck9EY3hDOEhsQWFSMTVnems5YVRtamVPQjZhbzFsUVpZSThpT0o4TkRoZFhESHJwVkxISXRRYld1K1c0NllGVjBXa2tkOHNXZ0JUYytOeUxUM1hDd1IvcVpvMXR2SE8rYjBRUFA3N3RQQlF1M21pZmpFQVg4eXFXeGk1b3VkQ3JPZkdCRkhtZVJuODZuOFcrL1kwbWs2aVJFTHZzRHNyUXlIemhNVDYrVDltYTVPUVR6QUo2L2pkdVB5MytmSnhCRzNEcXFlT21pMkJQNm8rQ3ZxZXYvRklsMWdrSHZ1SEFUQU9wRUlVTkdqNXhmbkl6NHl6aWhQV1dhM1dEV3RkZ2orb3lLWVExL3hGTFRoSllsRkhZKzZac20vZ1BqQm5qSE15eUFCMitMQ282VlIwbTFBRTVTaHhxMHNxR1VpbWVZWXV6MFBrQ0swbENzK09SU0cyYnByMVRlaldvaXBGTEhZUkREb2U1VEtiSkNTN0VCS1hnVlNGS0g2RDJnQThuZHk1dXhVb1dvdHlRcGdYUWJwdW1UQTZ2Z1U2VTBqa2I5ak9MUG1iWnh2Z1FsLzlxamtZQW9KQ0lLY3l3VGUiLCJtYWMiOiJlMzEyOWUyNjBhMDEyNjE0ZWMxYWNlZTM2YjY0ZTFiMWYwNjU1ODgxYTExMDViYjU1YjdlYzY3ZGY5YWE3ZmIxIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:42:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjE5aWJRdFJPTjcwUEVlRmN0NUdlL1E9PSIsInZhbHVlIjoibTAvUUFoVmdkVVZYUjlaek1ReHZPZkhhemdkdXNYY1Noelo4UDNNSWcwdFZsdVI5MFhuaThGNmZCVjdDcXphNFB2Q2llZDR3T2N5REt5VnJtTlNiV1Zqc0pkYWRpYWlyWmErc0U4dzhvTUFpY0kwREJxSUhsTGVRU0puOEg4ZEt1WnJOZ1Bvb2pSQWlOVFE5Z1JZRGpkb1FZQ1lGcTRqVjBQTE5DeTR0UkYwUlQzbHZmcGpkcGZhN2dNOWhnVThxV284NStDbXAwWk5RWGVKa0hGenRLT0hxWmFzZWk3MS94ajZMejJTeEZVYnBMR2tQNlEvZFdOeVRIeGwvVDVBNE1jMy94SGdiWmZMOGFtS2x3ZFBTQ3FkVDJLTUhyakhTdVB1UkFXaEdnY0F0Uk9HSEltRXc0Y0djMm1IdzRjUHRPUU1MMVhwVENBNFBTajNNMkZrWExYZnFxSjdhUTVXMm9ESC9rei9BMFppZXU1Vm4zQlAwaENmTXlNU2p3d01lL0JKMVJCZzBYRkY3SGQyNkhpUWNqMC82ZU9nZFJpSW02WWxyYmVUNlUwRS8wZ09OT3p1Sm5nbXg1WWU3N21Bd2NCTjVQa1JrKzhDcW9Zelk3RTJTSmJCN2t4NkQ0dGNUNml0akdNUFhoVllGWStEWXd0bGxxL043czlySTNUWEUiLCJtYWMiOiJjZTc5NmVmZWJiYTZmZjMwZWEwNTE5OGM0NTFkNzNkOGZiN2ZkZWMxNTdiZGFlZDJlNmQ0YjQ3NGYxZGExNWE2IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:42:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkVlTCtrYXg5U0x4S1dKUDB3ZU5IQ0E9PSIsInZhbHVlIjoib0tNTitaaXFqQWlobXNGelJ0UTdEcTl2UGRERFR1bXFxYkdRQ3pzSXZZcHorUnNFTVp4WG1NLzVMTGt1MjhzdStJck9EY3hDOEhsQWFSMTVnems5YVRtamVPQjZhbzFsUVpZSThpT0o4TkRoZFhESHJwVkxISXRRYld1K1c0NllGVjBXa2tkOHNXZ0JUYytOeUxUM1hDd1IvcVpvMXR2SE8rYjBRUFA3N3RQQlF1M21pZmpFQVg4eXFXeGk1b3VkQ3JPZkdCRkhtZVJuODZuOFcrL1kwbWs2aVJFTHZzRHNyUXlIemhNVDYrVDltYTVPUVR6QUo2L2pkdVB5MytmSnhCRzNEcXFlT21pMkJQNm8rQ3ZxZXYvRklsMWdrSHZ1SEFUQU9wRUlVTkdqNXhmbkl6NHl6aWhQV1dhM1dEV3RkZ2orb3lLWVExL3hGTFRoSllsRkhZKzZac20vZ1BqQm5qSE15eUFCMitMQ282VlIwbTFBRTVTaHhxMHNxR1VpbWVZWXV6MFBrQ0swbENzK09SU0cyYnByMVRlaldvaXBGTEhZUkREb2U1VEtiSkNTN0VCS1hnVlNGS0g2RDJnQThuZHk1dXhVb1dvdHlRcGdYUWJwdW1UQTZ2Z1U2VTBqa2I5ak9MUG1iWnh2Z1FsLzlxamtZQW9KQ0lLY3l3VGUiLCJtYWMiOiJlMzEyOWUyNjBhMDEyNjE0ZWMxYWNlZTM2YjY0ZTFiMWYwNjU1ODgxYTExMDViYjU1YjdlYzY3ZGY5YWE3ZmIxIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:42:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-52255675\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2032073737 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">SugqqTJ7ECKGbz4cQ6JiI7Lva8WKND7ROtTVAff3</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2032073737\", {\"maxDepth\":0})</script>\n"}}