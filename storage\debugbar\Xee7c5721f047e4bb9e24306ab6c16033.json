{"__meta": {"id": "Xee7c5721f047e4bb9e24306ab6c16033", "datetime": "2025-07-29 05:39:01", "utime": **********.576029, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753767540.658539, "end": **********.576054, "duration": 0.9175150394439697, "duration_str": "918ms", "measures": [{"label": "Booting", "start": 1753767540.658539, "relative_start": 0, "end": **********.467656, "relative_end": **********.467656, "duration": 0.8091168403625488, "duration_str": "809ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.467669, "relative_start": 0.****************, "end": **********.576094, "relative_end": 3.981590270996094e-05, "duration": 0.*****************, "duration_str": "108ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "veBeTW15SEAKORdDMlAjy1kl3TyPPSnDpGrAter2", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-970710177 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-970710177\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-258791269 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-258791269\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1908564108 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1908564108\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-222267641 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-222267641\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1394905357 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1394905357\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-298351956 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:39:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZrRWtaVG5zMzRCRHpBcnZNVnBLOVE9PSIsInZhbHVlIjoiQW1Sc1pmeVFHUnBlanBlbm52cmJ2dWV3L2VOZDZOdGJGbzBMOEU3N21hVGE5WUs0QkpnLzJrNGRYdjJEVDB3dUhiRTFXM0g2NGZGTng2MHVQakRVZ0JKOU1MRXN0dmlsOVVTa2RmYmxBSG5KbHdBcWo3S01YdFY2Nk9FZ2d6clV3NURQYVdpdmZVcit0dnhIR3NNbzZCZzdRT205NGJsbm0zSTQrR0dWQXdSRmxucUxaRnMwZ3IycVRDdG9DMkR3aURQSHpwY21jRUdjdEZWWnVjU0UzdGpWUkg3YTdIQ2RZUG1HMXNDZDFsSTYyV3dJT0N4RkIwV0VrYXEvYzZseHJYUjRyN2M5RG1VSE1yc0hRMGhEU1VDVTUwVENjRDl6Sk54L0xHczBqUWdGak9mcHNEOFhSblVsMkdBRnE0bVBtc09PZjgxcXhTY3dDeFpWNitwS0NKcmFoNWxVcGdUUEFGcTdZRkNiU1ZlSURSZkJKOXN0Wi9XNlhtcDVTSVQ5ZDFLVURCVlVQZGU0TFJsdnNCSlJLOTBlUC9PTlE5cWdmajN5aVZQeW90Q3k1alh4VndyejI4QzF4R29ta0l2Mml2eHJjTW90TGl6K1dpYVdVRmtXTjQ3OTRXTjBhUkR6NFk3WldwNFI0cVJMYWI3SEZORVJydFZQVEhtZ3c0aEkiLCJtYWMiOiIwNjBmZTUwZGNhNjlhZmJlZTgwOGZhMzlmNjRjMzljMDkxOWJjZjEwZWQ5ZDQzNGQ0YmRjZmE0YmI3ZTgwZDk3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:39:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlZ4dmpZcTRqQW54anIzVit0WGREbXc9PSIsInZhbHVlIjoicHRtMFNkckljdU00enRuWm13SmpWckJoMEZTb2YvcFVTbXplSGpBZ3dNWUptd0tsektXSndVTzl2ZkR6RnRxUnpzMThhTExzaHd4NHhCeFZnenRaSHIwMmFrR0FOVHFFL2J3bnlQVHJoT3ZaNUpDRzl0cjg1Sms3UlQxUm1VaDZUUmovaFU4a3FFRTZiZUZhT3Btdnc1eExwMndYL240WUZQck1PNVhGQWpXZHkxNnh0U2V5Tm1rTHE5azBITXptZXJqOXcvVnFUbi9DNWJLMkFkM0JteUkxbXI4UEh2QTNSM0hFU0w1Q0t5cGN1dUNYUnJjSzR1Um9KY0pKZ0EvcWZHSTBHdTdtbUJXYnZGdXVMbk1nc0lSSThhSTJDVFNjVFg1eWk1VkV3TnZ0VTR2cHNDTjR6OHFSUmtJZFIyb3VlazQzT2xjbnU0c3lsOWdZbmtPTmdlWjArVWQ0RllMdjM2VDZibS9FZGlSTEFHSFFsb01wdnZESkVIUlhEREhWRWRNdXp6VHNUdzA5NmJZRmdROXd5cEQvN1Nad1M5djVrNEorZ05jZzhrb2sybEhEczUwOVBkazNYelFtWmdnME1IVmNOS05yS1BUL0dFRG1GTnhSMWhLRk1UMXM4TUpzWjlXTWlLblhQeHhrUnpaQUZrOFkvaHRGZXczYzZEVzEiLCJtYWMiOiI4NWY1ZWVkOTY4M2ZkNzRlZGJlNzYzNjI0NzEwYjI1OTBhYzFjMjdlMTNmMzE4YWU2MmZlYWI0NmJjZjhiNjFiIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:39:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZrRWtaVG5zMzRCRHpBcnZNVnBLOVE9PSIsInZhbHVlIjoiQW1Sc1pmeVFHUnBlanBlbm52cmJ2dWV3L2VOZDZOdGJGbzBMOEU3N21hVGE5WUs0QkpnLzJrNGRYdjJEVDB3dUhiRTFXM0g2NGZGTng2MHVQakRVZ0JKOU1MRXN0dmlsOVVTa2RmYmxBSG5KbHdBcWo3S01YdFY2Nk9FZ2d6clV3NURQYVdpdmZVcit0dnhIR3NNbzZCZzdRT205NGJsbm0zSTQrR0dWQXdSRmxucUxaRnMwZ3IycVRDdG9DMkR3aURQSHpwY21jRUdjdEZWWnVjU0UzdGpWUkg3YTdIQ2RZUG1HMXNDZDFsSTYyV3dJT0N4RkIwV0VrYXEvYzZseHJYUjRyN2M5RG1VSE1yc0hRMGhEU1VDVTUwVENjRDl6Sk54L0xHczBqUWdGak9mcHNEOFhSblVsMkdBRnE0bVBtc09PZjgxcXhTY3dDeFpWNitwS0NKcmFoNWxVcGdUUEFGcTdZRkNiU1ZlSURSZkJKOXN0Wi9XNlhtcDVTSVQ5ZDFLVURCVlVQZGU0TFJsdnNCSlJLOTBlUC9PTlE5cWdmajN5aVZQeW90Q3k1alh4VndyejI4QzF4R29ta0l2Mml2eHJjTW90TGl6K1dpYVdVRmtXTjQ3OTRXTjBhUkR6NFk3WldwNFI0cVJMYWI3SEZORVJydFZQVEhtZ3c0aEkiLCJtYWMiOiIwNjBmZTUwZGNhNjlhZmJlZTgwOGZhMzlmNjRjMzljMDkxOWJjZjEwZWQ5ZDQzNGQ0YmRjZmE0YmI3ZTgwZDk3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:39:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlZ4dmpZcTRqQW54anIzVit0WGREbXc9PSIsInZhbHVlIjoicHRtMFNkckljdU00enRuWm13SmpWckJoMEZTb2YvcFVTbXplSGpBZ3dNWUptd0tsektXSndVTzl2ZkR6RnRxUnpzMThhTExzaHd4NHhCeFZnenRaSHIwMmFrR0FOVHFFL2J3bnlQVHJoT3ZaNUpDRzl0cjg1Sms3UlQxUm1VaDZUUmovaFU4a3FFRTZiZUZhT3Btdnc1eExwMndYL240WUZQck1PNVhGQWpXZHkxNnh0U2V5Tm1rTHE5azBITXptZXJqOXcvVnFUbi9DNWJLMkFkM0JteUkxbXI4UEh2QTNSM0hFU0w1Q0t5cGN1dUNYUnJjSzR1Um9KY0pKZ0EvcWZHSTBHdTdtbUJXYnZGdXVMbk1nc0lSSThhSTJDVFNjVFg1eWk1VkV3TnZ0VTR2cHNDTjR6OHFSUmtJZFIyb3VlazQzT2xjbnU0c3lsOWdZbmtPTmdlWjArVWQ0RllMdjM2VDZibS9FZGlSTEFHSFFsb01wdnZESkVIUlhEREhWRWRNdXp6VHNUdzA5NmJZRmdROXd5cEQvN1Nad1M5djVrNEorZ05jZzhrb2sybEhEczUwOVBkazNYelFtWmdnME1IVmNOS05yS1BUL0dFRG1GTnhSMWhLRk1UMXM4TUpzWjlXTWlLblhQeHhrUnpaQUZrOFkvaHRGZXczYzZEVzEiLCJtYWMiOiI4NWY1ZWVkOTY4M2ZkNzRlZGJlNzYzNjI0NzEwYjI1OTBhYzFjMjdlMTNmMzE4YWU2MmZlYWI0NmJjZjhiNjFiIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:39:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-298351956\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">veBeTW15SEAKORdDMlAjy1kl3TyPPSnDpGrAter2</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}