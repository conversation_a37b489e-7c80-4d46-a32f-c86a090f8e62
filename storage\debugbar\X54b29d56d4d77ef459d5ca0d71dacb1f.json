{"__meta": {"id": "X54b29d56d4d77ef459d5ca0d71dacb1f", "datetime": "2025-07-29 04:51:08", "utime": **********.173003, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753764667.164046, "end": **********.173034, "duration": 1.0089879035949707, "duration_str": "1.01s", "measures": [{"label": "Booting", "start": 1753764667.164046, "relative_start": 0, "end": **********.080697, "relative_end": **********.080697, "duration": 0.9166510105133057, "duration_str": "917ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.080713, "relative_start": 0.****************, "end": **********.173054, "relative_end": 2.002716064453125e-05, "duration": 0.*****************, "duration_str": "92.34ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2997\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1801 to 1807\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1801\" onclick=\"\">routes/web.php:1801-1807</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GehuHgKBiasCbtepEQ0eluf1D2JfXN3gBikf27Wt", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1870953520 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1870953520\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-257461026 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-257461026\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2096056780 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2096056780\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1590059748 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1590059748\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-167034489 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-167034489\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-921489762 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 04:51:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVZVGEzZXppUzN3M3k2WXkycVczWWc9PSIsInZhbHVlIjoianloMjJGR0ZKTksxekJTMyt0OUJnV0RCWlJHWHc3dnUvbDFpL3lhNkxsMGs3SitNRktsSkFIaVRORWszRlEwQXVmemt2TFZobkFva3E3UGtEbjVtT3hMekc2WWlEMG02ck0wQVAxeWI1a3VjT0ZXN0wwbFA0V0xpd1hKZVRVN3FMenJYMGdPcnR1aGtMVll1dXd3RWpxaDVhYk1TNHA1WnB5Y2tYVkNhN3ZNM0hORFExYURtN3lqU05KYXh3bTBlS1hhTXJoTGNXcHRrNktlRWtIWnduZWRvZHZhRXBrNktTMkF3UUsyMXhKbVFSVUxTb0hENXRyRFVqTmZ5MkJ2bnlyY1R3cUhCQzRxNUFjNjd5NHRPb2F5VU8wRWZLUFYzamhjSlQ1UHAxaGU3aWhRSE1FZkFMcHUyb1A3UmlBOTg1KzBWbW8zRDRtNFIzdDRkdHg1R0hoNWxRYjBNQStSMGgyTk1DczVWMXlUVjZLUzdWK1MraCs3em44L0dvdTM5b0d3dUZhUDRucWFjYzd4am00Q3U4Z0JFNUxqWlBRTnh0U3UrQVRHTWFHYjAyakZWZ1dtVE4vaTlrNTVGbjhIWVdZeG5EbG1STXM0MU5BZkU0R0JJdXZmUWpsUHUrZjhNVjJWWXJGZHVkVy9PRTNEaW5vNTA0RzZLZFZiTFZqS2EiLCJtYWMiOiJlZDI2ODc4ZDk5OTMxZjg2OGZlMWYxMjM2Y2JkMzY2OTg2N2UzYjExYWMxOTRjMTllZDRlMGI1ZTZlZjE2ZmRmIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 06:51:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InZxQUpVamxGQWdsWjI0WUI3VXl4aVE9PSIsInZhbHVlIjoiWVVwdjJBaTk2QmpQRm1RbUZ3M2dNT1NlUm41TW9kc1owUUEwUXprU0FmcDM0YVZCbTU0Z0xTZm8rZGFwOHBBMVV0OFhBcWxrdGJuT3N1M0djczd6Um1oQ2ExWm1pMlI3NUN2OURMVm5OcGE5ZkZDWXo2V2xUcnZiYWJNSFpWdG1FVWRoVmdwUEprYWZ3UXdLOVV4N1ZrMHh6QTVsaEpxalhFRkVpQ3YvbTE5anJiLzA4VzlXcEpmMGM3dGpNeml1N1EyRkJxRXJuTFQ4bW9uRFhiRG5jeHF3VUVKWlR4WDdHdXZIcTdiY1BXbStFSHhFTFRkV2hBdFE4UkhJOWlyMzFKZTBtL1BwR2N2QTRyVkxhdW1XeFRSVVVvSHZZZG9pY3RYcGtjeVZkR2VaSi83OGxzZyttMW9EUkhKYjZFK2hxZHF5WmhiRWpaV3E0U2lCS0tsNS80cFdtYlZKSDZFNllzS3RhaEhBeFM1bW9UTWFzdS9Kd05TWlF1Z2VvTmliOENtUWhYajU4OXlyclVheVJPTXBHNnVJaFFuOUpuOXVQMjlkMzV2S2FqYjZuUFEyUzR6amNFNjJTQWNtWGxwS3U4WGw4TWVIZFUvdGdva3cvM3g0VFh4QUMxTXUweTQzRFg3Uy9kQ1FjRzl1NzIrTDNqQkpSS0s5Wmp0YkUyR2siLCJtYWMiOiI0YmFlMjQ0YmVmNWVhMjZkMmYwZTMwZGZmY2VjNDgyMTA4ZTYwYmFjOTJhZjVhOTM0YzE1OTNiOWUxMmRhZmMxIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 06:51:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVZVGEzZXppUzN3M3k2WXkycVczWWc9PSIsInZhbHVlIjoianloMjJGR0ZKTksxekJTMyt0OUJnV0RCWlJHWHc3dnUvbDFpL3lhNkxsMGs3SitNRktsSkFIaVRORWszRlEwQXVmemt2TFZobkFva3E3UGtEbjVtT3hMekc2WWlEMG02ck0wQVAxeWI1a3VjT0ZXN0wwbFA0V0xpd1hKZVRVN3FMenJYMGdPcnR1aGtMVll1dXd3RWpxaDVhYk1TNHA1WnB5Y2tYVkNhN3ZNM0hORFExYURtN3lqU05KYXh3bTBlS1hhTXJoTGNXcHRrNktlRWtIWnduZWRvZHZhRXBrNktTMkF3UUsyMXhKbVFSVUxTb0hENXRyRFVqTmZ5MkJ2bnlyY1R3cUhCQzRxNUFjNjd5NHRPb2F5VU8wRWZLUFYzamhjSlQ1UHAxaGU3aWhRSE1FZkFMcHUyb1A3UmlBOTg1KzBWbW8zRDRtNFIzdDRkdHg1R0hoNWxRYjBNQStSMGgyTk1DczVWMXlUVjZLUzdWK1MraCs3em44L0dvdTM5b0d3dUZhUDRucWFjYzd4am00Q3U4Z0JFNUxqWlBRTnh0U3UrQVRHTWFHYjAyakZWZ1dtVE4vaTlrNTVGbjhIWVdZeG5EbG1STXM0MU5BZkU0R0JJdXZmUWpsUHUrZjhNVjJWWXJGZHVkVy9PRTNEaW5vNTA0RzZLZFZiTFZqS2EiLCJtYWMiOiJlZDI2ODc4ZDk5OTMxZjg2OGZlMWYxMjM2Y2JkMzY2OTg2N2UzYjExYWMxOTRjMTllZDRlMGI1ZTZlZjE2ZmRmIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 06:51:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InZxQUpVamxGQWdsWjI0WUI3VXl4aVE9PSIsInZhbHVlIjoiWVVwdjJBaTk2QmpQRm1RbUZ3M2dNT1NlUm41TW9kc1owUUEwUXprU0FmcDM0YVZCbTU0Z0xTZm8rZGFwOHBBMVV0OFhBcWxrdGJuT3N1M0djczd6Um1oQ2ExWm1pMlI3NUN2OURMVm5OcGE5ZkZDWXo2V2xUcnZiYWJNSFpWdG1FVWRoVmdwUEprYWZ3UXdLOVV4N1ZrMHh6QTVsaEpxalhFRkVpQ3YvbTE5anJiLzA4VzlXcEpmMGM3dGpNeml1N1EyRkJxRXJuTFQ4bW9uRFhiRG5jeHF3VUVKWlR4WDdHdXZIcTdiY1BXbStFSHhFTFRkV2hBdFE4UkhJOWlyMzFKZTBtL1BwR2N2QTRyVkxhdW1XeFRSVVVvSHZZZG9pY3RYcGtjeVZkR2VaSi83OGxzZyttMW9EUkhKYjZFK2hxZHF5WmhiRWpaV3E0U2lCS0tsNS80cFdtYlZKSDZFNllzS3RhaEhBeFM1bW9UTWFzdS9Kd05TWlF1Z2VvTmliOENtUWhYajU4OXlyclVheVJPTXBHNnVJaFFuOUpuOXVQMjlkMzV2S2FqYjZuUFEyUzR6amNFNjJTQWNtWGxwS3U4WGw4TWVIZFUvdGdva3cvM3g0VFh4QUMxTXUweTQzRFg3Uy9kQ1FjRzl1NzIrTDNqQkpSS0s5Wmp0YkUyR2siLCJtYWMiOiI0YmFlMjQ0YmVmNWVhMjZkMmYwZTMwZGZmY2VjNDgyMTA4ZTYwYmFjOTJhZjVhOTM0YzE1OTNiOWUxMmRhZmMxIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 06:51:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-921489762\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-19604033 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GehuHgKBiasCbtepEQ0eluf1D2JfXN3gBikf27Wt</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-19604033\", {\"maxDepth\":0})</script>\n"}}