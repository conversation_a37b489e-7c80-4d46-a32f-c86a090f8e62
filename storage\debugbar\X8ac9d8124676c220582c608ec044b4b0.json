{"__meta": {"id": "X8ac9d8124676c220582c608ec044b4b0", "datetime": "2025-07-29 05:33:25", "utime": **********.179866, "method": "POST", "uri": "/api/get-pipeline-stages", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[05:33:25] LOG.info: ContactController: Getting stages for pipeline {\n    \"pipeline_id\": \"28\",\n    \"user_id\": 84,\n    \"creator_id\": 84,\n    \"request_data\": {\n        \"_token\": \"pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U\",\n        \"pipeline_id\": \"28\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.077403, "xdebug_link": null, "collector": "log"}, {"message": "[05:33:25] LOG.info: ContactController: Found stages {\n    \"pipeline_id\": \"28\",\n    \"count\": 4,\n    \"stages\": [\n        {\n            \"id\": 117,\n            \"name\": \"Flow\",\n            \"order\": 0\n        },\n        {\n            \"id\": 108,\n            \"name\": \"Draft One\",\n            \"order\": 1\n        },\n        {\n            \"id\": 109,\n            \"name\": \"Send One\",\n            \"order\": 2\n        },\n        {\n            \"id\": 110,\n            \"name\": \"Follow UP One\",\n            \"order\": 3\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.087543, "xdebug_link": null, "collector": "log"}, {"message": "[05:33:25] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 84,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/api\\/get-pipeline-stages\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.174392, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753767204.261122, "end": **********.179918, "duration": 0.9187960624694824, "duration_str": "919ms", "measures": [{"label": "Booting", "start": 1753767204.261122, "relative_start": 0, "end": 1753767204.984917, "relative_end": 1753767204.984917, "duration": 0.7237949371337891, "duration_str": "724ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753767204.984936, "relative_start": 0.7238140106201172, "end": **********.17992, "relative_end": 1.9073486328125e-06, "duration": 0.19498395919799805, "duration_str": "195ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48182560, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/get-pipeline-stages", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@getPipelineStages", "namespace": null, "prefix": "/api", "where": [], "as": "api.get-pipeline-stages", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=143\" onclick=\"\">app/Http/Controllers/ContactController.php:143-198</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.02359, "accumulated_duration_str": "23.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0388062, "duration": 0.01643, "duration_str": "16.43ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 69.648}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0721, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 69.648, "width_percent": 3.646}, {"sql": "select `id`, `name`, `order` from `lead_stages` where `pipeline_id` = '28' and `created_by` = 84 order by `order` asc, `id` asc", "type": "query", "params": [], "bindings": ["28", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 168}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.078275, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:168", "source": "app/Http/Controllers/ContactController.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=168", "ajax": false, "filename": "ContactController.php", "line": "168"}, "connection": "omx_sass_systam_db", "start_percent": 73.294, "width_percent": 3.222}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.098795, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "omx_sass_systam_db", "start_percent": 76.515, "width_percent": 4.451}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (84) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1080081, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 80.967, "width_percent": 3.518}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (84) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.112412, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 84.485, "width_percent": 4.281}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.119121, "duration": 0.00265, "duration_str": "2.65ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 88.766, "width_percent": 11.234}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 543, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 550, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/api/get-pipeline-stages", "status_code": "<pre class=sf-dump id=sf-dump-37805787 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-37805787\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-815747185 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-815747185\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-593926086 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">28</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-593926086\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1005118848 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">62</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IlVIYnhvZ3FFU2xJSmFNUlpYOW5McXc9PSIsInZhbHVlIjoiU1JEZkZ2dWVhSWFOR3hYZWJDeCtYWWhPd2w4eTV3eTNCcUxVYzRoZGIxNmYwMTRoZGRyUXIweFlrUzdQQXhjTGxGbXRuK2NtMjhlK0FNV2tMR2JKR1pCbEpuZmNmcXhJMmloUFcyQTl0YW00MnBFaXZFUitzTG5kVC95M3BLOCt2Vmk1MVRWTmZOVG9ZTk0wbHlNdldkSURDUmFDekNLRnVKWDdZcGpmODJpdXR2QTc2Qnpua1RGTmM2Z2hpakRoM1hWaGtDQysrZFBMcUg2WFBpMThaUlo5clB1cXpscTlzSVdHOW0weHp4TERZbDR5SHczWlFvclM5YkVFc0QzdmRBNVhNTzRlem1ERm1tYXFWYTh6RWcza0NwL09QR0JjYnd6QzJYVjcwcEIrYm1rUHVCcVdlTC9IMzhKNC81U25pMVl1NGt4SHV0M0x0SDFQSWJqQ0cxL1B1R0lseEdmM3RySUVGMmUydjB5elpnV3RBdkptNG1zaHBpOXBkUHNKSU0rajlqdUFxZTkwTWVNYTNiT0pJejQ0M3dlUTY3RVI1Y3Vjc2pLaDBKUlJ0ai9QM2hxNERoQll2SUx6NVlDSzlRbWt4MWY2NFkvSnFxQzBndDd4ZVkybVJKTVlieXhwSTNtZVlQZ1JGb1pHYnpoUE1MU3lCTU8yeVd4S1diMkgiLCJtYWMiOiI1YzdlMzE3YTNmOTQwMjI2OWM3ZGUyNTE3YjZmNDM0ZTZiY2MxODNiMjc3NzczNGI3ODNiYzMyN2NmNGRjOWYzIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InNJQTIzeGplRUFzN2xGWTdsczY4TGc9PSIsInZhbHVlIjoiUzZTbktESkdqWUJBYjhOZ3FFTkNvQVlyY2hpaWNOZUlnSDJ0RjhWWEJWRUtzeWY1Sk1iQkVHd0tOMS9PV0VzWXI2QmdRWUQ0NlNDalVkczhBVGtUTHZYdVhlbStDU3B5RkEvaE80Z3k4QmpES01ScEVWN2E1VXdUT016ZDB3Nnp6RGwreHk4eHdrTThrZWZkRmUweHd3V09FRFpvbFl6U0pYQTVseDVNN2lVR1g2b0h3UTVKZGxBVndRSDJPdFRUK3pDRHVQdFU4SElOOE1WSUxiSTRPMGM0aEhUWENYN0NkV1VaMERyS0Q2SjV0K05GWkorNHdNWDZmQ1hRYmh1Rk0yVENSZWJ3MFFOWVRWSHVweDlMUE9VcWJ0ZWFaSEFxRUdubXp6QXFmUlJoR3lOZzh2QTkxWTBIM0lleW1rdzd0dFFScWFXanVEQVY5RUJkNmZtdnVzSzNmNTBzQ051azdNKzRUbnE0Zm5yMDFJVlJNRWJFeWVrV2dqZElRcytCcFU0cWZlc1hkTDcrNzM4RFBtUEErdlNmbUdFUG9EMVYyblhvbjVSS3A1c3dETExNbFZ4RmZQTHZGekV0OTFsRzJlK1ZURkdPWnFtQUdPcFJUa0xlbC9VOTZ4VUViU0pVWktKbld6TlJST0s1dDlGelNJT2h2cGprbEt3RnhKbjciLCJtYWMiOiJjYzUxYzRjZTc3OWVlYjMyYzI5YzZiYjAxMzAwZmU3YjYzYjZhZTk0NmEwMmVlNDI2ZmQ4YzQzNmQ1NmZmNWE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1005118848\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1447474500 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1447474500\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1700997246 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:33:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZRaVljYzZYNEN1eUsrcWRwS08zOEE9PSIsInZhbHVlIjoiU3JXRkRETkI4dXB1V2ErQnFMSWRLemR6YU1CSThCc0J3QzJ0NlRwNW1YRnBNTTkzN1pueVoyZ2Q1b09KM2VhUmNKeTdadUlsZUd5ZnlCWkxXZkdlc2dNSFQxSmJIODdhSElqT0hIb3RxdFoyM0VianU0a01waExsQjBrTnJhbnp2U1lOYUJYOS8vT2prenBtMzlyTjFMUnZUSHdaMUp0cS85VDYyOE1UdzNJWWt2bThPRUpudzZ4NkRTMDQyVlh0K0NtSUlmaVEyZkdiMThGejBNOCtMT0xDWmhYUDYvMnAwOUM2MDdLT0w3VjY5SmFGeFRDU0F6QlNvR0Z5NlRRNStGWmdiZ2lDaXRGSDloeHpiRzY0ZU5rTG9xNE11cWJHQWF4T3p4aUY2b24wUFI1ODYwT1pRWWJvdUdBeis0QzlTVEhZV016c05DczVzaEV2bk9sRTQ1eU44R1oxWS9INmFoUzN5bHcwV1gwMkVzaEgwVXJiVnR1WmkvTERBN3pyMlZoSXdjcFg0bk1xUEl4ajhOMHVEcEcwSmwybERsQ00yNlpFQTZMcW9peGErNE1BWHk3RDF0N0xLWU1id1drVjR2ZmJISHd3T3E4NnU1eFAvbmxZT2c2YmlkcG9sdGFQVVRwVmJnMFR6RjByV1JlLzFWSW5lZGlTcStpUVM3VnIiLCJtYWMiOiI4NTdlOWM2NGIyMzBiM2ZkMTQyYWU5NzY1MjA3MzExMjczMzZiMzM1OTU3YzJhMzZmYmFhNmEzYjQ2ZDhiMGUzIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:33:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InA1ci9NcSttTi9GYmUrdHR2Tk5mRnc9PSIsInZhbHVlIjoiSjMybFlUTzlEOVdXVkFOUFhxOTlLNkh0REk0YlZFTjRUTVVHK2FWYjBZVHJ6Smx4TkhNcUlNNGtMSFV3TTNrUXhKTjJHcC9hY2tWdk1ITWJVL2xxZklabzIvc2ZIR3B3endlU1pDaDlIaGhYelAwcjV4NDhZbjJERmYyYTc3aTJQQ3c5cVNTUFR2dVhXOHlVdGkzeXVBd0Vhays5bVNXVXBNUHgrNzBna1c5NFhaSExHbSswaDBueWQ5QnFsa0hYK0F3TXRuK3pMVXA4SWJFUUdHYjlJUHFmUDhwTzgwOVVUUm9JSzFhZ0pQNlFXVHlmbXJaTXhKbXphYXdQSWZxSFpXaFY2WnlhUEtTL3ByNVN5MzJ3YmxqNjVaSGhMUFZnVkd0blY3OS9oTHN6NGYzNGJ3SlFZT0FjK0NwVGZtZHVsT1hRekJuUW5kVjlkWUh1K2tVNkEzVzlZQlRFanVWeS96Zkc3ZGtaanNvNnBYbHRBK3NVcHhVNGNjbGgyMndoK0pqKzBIaVVPSTBUcS9rdS9iM0pOd3JtNTIvL3dVejhnQTlJMU96QTlUQWo5Um92M1JSdXJ5eEpUVGpYVjIzTkkwOUZRekFmMXVoYk0xV1Q0RDdRaDVqenJESlhwWHF2eStoRzZkbWdKMHNkLzErS1U0aGhBdmp0dHNueDBIQmQiLCJtYWMiOiIyN2M0MDg2Y2RlOWIwNmFiMjhlNTkwZTQwNDEwMjM0ZGJiODAwNTNjNjE0Zjg4ZTBmMjFjZjdjMzJhMGVkOGE0IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:33:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZRaVljYzZYNEN1eUsrcWRwS08zOEE9PSIsInZhbHVlIjoiU3JXRkRETkI4dXB1V2ErQnFMSWRLemR6YU1CSThCc0J3QzJ0NlRwNW1YRnBNTTkzN1pueVoyZ2Q1b09KM2VhUmNKeTdadUlsZUd5ZnlCWkxXZkdlc2dNSFQxSmJIODdhSElqT0hIb3RxdFoyM0VianU0a01waExsQjBrTnJhbnp2U1lOYUJYOS8vT2prenBtMzlyTjFMUnZUSHdaMUp0cS85VDYyOE1UdzNJWWt2bThPRUpudzZ4NkRTMDQyVlh0K0NtSUlmaVEyZkdiMThGejBNOCtMT0xDWmhYUDYvMnAwOUM2MDdLT0w3VjY5SmFGeFRDU0F6QlNvR0Z5NlRRNStGWmdiZ2lDaXRGSDloeHpiRzY0ZU5rTG9xNE11cWJHQWF4T3p4aUY2b24wUFI1ODYwT1pRWWJvdUdBeis0QzlTVEhZV016c05DczVzaEV2bk9sRTQ1eU44R1oxWS9INmFoUzN5bHcwV1gwMkVzaEgwVXJiVnR1WmkvTERBN3pyMlZoSXdjcFg0bk1xUEl4ajhOMHVEcEcwSmwybERsQ00yNlpFQTZMcW9peGErNE1BWHk3RDF0N0xLWU1id1drVjR2ZmJISHd3T3E4NnU1eFAvbmxZT2c2YmlkcG9sdGFQVVRwVmJnMFR6RjByV1JlLzFWSW5lZGlTcStpUVM3VnIiLCJtYWMiOiI4NTdlOWM2NGIyMzBiM2ZkMTQyYWU5NzY1MjA3MzExMjczMzZiMzM1OTU3YzJhMzZmYmFhNmEzYjQ2ZDhiMGUzIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:33:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InA1ci9NcSttTi9GYmUrdHR2Tk5mRnc9PSIsInZhbHVlIjoiSjMybFlUTzlEOVdXVkFOUFhxOTlLNkh0REk0YlZFTjRUTVVHK2FWYjBZVHJ6Smx4TkhNcUlNNGtMSFV3TTNrUXhKTjJHcC9hY2tWdk1ITWJVL2xxZklabzIvc2ZIR3B3endlU1pDaDlIaGhYelAwcjV4NDhZbjJERmYyYTc3aTJQQ3c5cVNTUFR2dVhXOHlVdGkzeXVBd0Vhays5bVNXVXBNUHgrNzBna1c5NFhaSExHbSswaDBueWQ5QnFsa0hYK0F3TXRuK3pMVXA4SWJFUUdHYjlJUHFmUDhwTzgwOVVUUm9JSzFhZ0pQNlFXVHlmbXJaTXhKbXphYXdQSWZxSFpXaFY2WnlhUEtTL3ByNVN5MzJ3YmxqNjVaSGhMUFZnVkd0blY3OS9oTHN6NGYzNGJ3SlFZT0FjK0NwVGZtZHVsT1hRekJuUW5kVjlkWUh1K2tVNkEzVzlZQlRFanVWeS96Zkc3ZGtaanNvNnBYbHRBK3NVcHhVNGNjbGgyMndoK0pqKzBIaVVPSTBUcS9rdS9iM0pOd3JtNTIvL3dVejhnQTlJMU96QTlUQWo5Um92M1JSdXJ5eEpUVGpYVjIzTkkwOUZRekFmMXVoYk0xV1Q0RDdRaDVqenJESlhwWHF2eStoRzZkbWdKMHNkLzErS1U0aGhBdmp0dHNueDBIQmQiLCJtYWMiOiIyN2M0MDg2Y2RlOWIwNmFiMjhlNTkwZTQwNDEwMjM0ZGJiODAwNTNjNjE0Zjg4ZTBmMjFjZjdjMzJhMGVkOGE0IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:33:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1700997246\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-919809487 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-919809487\", {\"maxDepth\":0})</script>\n"}}