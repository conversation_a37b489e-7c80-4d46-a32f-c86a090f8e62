{"__meta": {"id": "X39f4b69f55296d2cd904cb03a5d0559b", "datetime": "2025-07-29 05:33:14", "utime": **********.642277, "method": "PUT", "uri": "/api/contacts/12/pipeline", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:33:14] LOG.info: ContactController: Updating contact pipeline {\n    \"contact_id\": \"12\",\n    \"request_data\": {\n        \"_token\": \"pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U\",\n        \"pipeline_id\": \"28\",\n        \"stage_id\": \"110\",\n        \"notes\": null\n    },\n    \"user_id\": 84\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.569088, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753767193.433226, "end": **********.642358, "duration": 1.2091319561004639, "duration_str": "1.21s", "measures": [{"label": "Booting", "start": 1753767193.433226, "relative_start": 0, "end": **********.459232, "relative_end": **********.459232, "duration": 1.0260059833526611, "duration_str": "1.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.459257, "relative_start": 1.0260307788848877, "end": **********.642362, "relative_end": 4.0531158447265625e-06, "duration": 0.1831052303314209, "duration_str": "183ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46338320, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT api/contacts/{id}/pipeline", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@updateContactPipeline", "namespace": null, "prefix": "/api", "where": [], "as": "api.update-contact-pipeline", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=203\" onclick=\"\">app/Http/Controllers/ContactController.php:203-287</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00988, "accumulated_duration_str": "9.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5342028, "duration": 0.005860000000000001, "duration_str": "5.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 59.312}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5603008, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 59.312, "width_percent": 11.134}, {"sql": "select count(*) as aggregate from `pipelines` where `id` = '28'", "type": "query", "params": [], "bindings": ["28"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.594944, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "omx_sass_systam_db", "start_percent": 70.445, "width_percent": 6.781}, {"sql": "select count(*) as aggregate from `lead_stages` where `id` = '110'", "type": "query", "params": [], "bindings": ["110"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.599039, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "omx_sass_systam_db", "start_percent": 77.227, "width_percent": 5.364}, {"sql": "select * from `leads` where `id` = '12' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["12", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 229}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.607937, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:229", "source": "app/Http/Controllers/ContactController.php:229", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=229", "ajax": false, "filename": "ContactController.php", "line": "229"}, "connection": "omx_sass_systam_db", "start_percent": 82.591, "width_percent": 9.919}, {"sql": "select * from `deals` where `id` = '12' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["12", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.614263, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:236", "source": "app/Http/Controllers/ContactController.php:236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=236", "ajax": false, "filename": "ContactController.php", "line": "236"}, "connection": "omx_sass_systam_db", "start_percent": 92.51, "width_percent": 7.49}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/api/contacts/12/pipeline", "status_code": "<pre class=sf-dump id=sf-dump-579343469 data-indent-pad=\"  \"><span class=sf-dump-num>404</span>\n</pre><script>Sfdump(\"sf-dump-579343469\", {\"maxDepth\":0})</script>\n", "status_text": "Not Found", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-203123991 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-203123991\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1299137953 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">28</span>\"\n  \"<span class=sf-dump-key>stage_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">110</span>\"\n  \"<span class=sf-dump-key>notes</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1299137953\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1184550838 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">82</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjVHbGJvMzZqd0ZVV0pVb0tSYzlLSmc9PSIsInZhbHVlIjoiWHRQdmlKSU4xSjhWdjVEcU8rYXA5OXpsUGwrMnlCS2ZXa2UxOVpXNzRSR1FiOC9scTJxVkRpblVXNmFUajhnRU5wWFljVzh0OHhvR1JBczEzMnlzQ0RIcHRUQVVDY0wrYVBGOFdxMnJEQ1dqeCt5dE9VUU1zdGM0Y2hiQnhLblJ5eXpaaW5COFltRVUvS3pFZUlYSHl3aWpyaGJZb284RWFuRWxlcTFHNFh1Z0NrWFpna0hranhGRmZpRG96a0traWdaa2pIVEp3QklJczVjUm1vQkZ2S0hIV0d1RTF1bU1IUWgvUWwwcGloTWdCUzZBMGJSbFdvSlFUelQ2bWt2enFqR3I2UXlyV0VBb3pUT3Jqak1iNUxPSFVYb0RvQklxYTVCaTV1dURTK0pHQWQ1OWxsQ0sydEx6RlJlY09ockl4bEo2WXB3TUhNdlU4Z2JycFFmSXJpdEJZcHhmSFMzV0hvNUpvOWdLcEZYUEU1T0o2azE2MzAwalRram8wb0JEYWNiZVVvSHZpcFdTNU1MZStuNW51RVlnRHJRY2w4U2dYTnpoU3lkVnJiaWtXT2xXRWRwM2dHb2dYTEhDbFZnNnE2LzY0NFM0YUhxUUV2QnZkK3NOMVNaSTY0YmdiZ01RNlZ6VnFTT2dCQUZDR0ZsWGRGYm5IQjJ3aXVOQnBnb1kiLCJtYWMiOiJkMTNhODU0YmM5M2E1OTU1YzYzMjljYTFkOWIyNTQ0ZTNhODVjMWJkMTE1NjkwNDJmMGY5ODFiYTcwMDNmNDI4IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InRRY3NHdTVzT05CSGt6VnVyTXF6RXc9PSIsInZhbHVlIjoid3dVZjRHNUVQUmtRd0FZSkJ3NHZucExxcXhURjFOT1I3ZVpZdk9seDdHZ3JxSmxkUEUyVGt4TjN3dkFXM0hqR013N3NQYUFjd0I0R2hFSGJpMmJmcmFTYkkwWEdVc1RCdW9ySXZiazRKbWN2V0RQQjJsMmRBeDNxZzVDa3R2K2wyb3VWQzhwUDdMNDB6WXRoU0ExYm1RWFNDN3NHQjBlTzY5SDc0cGVxaStqVDFyYWZkZmFYUmc2TzZROERnREd5d2lNQWt3YWhOV2tDOE5XNlVXOTBNREhYSG1KV0NJNnRkWjZBL0JaRjdycHMrUXM2aFppZWtNVlhjdHR1Nkh5dlJYSnBSZ1A4Q1hYZURmejJmUXZRMVBVMnJVZVd4Tk5mOHp2OXdvQUo1Rm5pQlo4WHoxMmszc3VKalA3cVV2bjJsdWk5R3ViZUkwQ096ajhFbkdMUDRsWW1HZU8wcndyT3BRS1BIbDJmM2YyVkVOaVdXODBicWxLR0RSSG0raGhYZzlBZGFOd2dJZ3NNL3Ava25rTHNmVkY4QXczWjBTbkd1UVF2NEIvNGVZNmpNWE1vSTdnTTA3THh5dXpISHkyWmNZVURsNTFDZE5yTTVCQ1RSV3FPMVROemtKT1VLY09iMjNRdUhQVXNKUjgrSFllVEV0NUxRNngwZDRra0U1bEUiLCJtYWMiOiIxYTc3ZWZhOTVlMzIwMzQ5OWUwMmM1MTk1N2JiNzk1YThjNzI2MDVlMzU0YmZiOTZjZGU5ZWJkZGRmYzk4MzQ5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1184550838\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-192136906 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-192136906\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1875616256 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:33:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVIYnhvZ3FFU2xJSmFNUlpYOW5McXc9PSIsInZhbHVlIjoiU1JEZkZ2dWVhSWFOR3hYZWJDeCtYWWhPd2w4eTV3eTNCcUxVYzRoZGIxNmYwMTRoZGRyUXIweFlrUzdQQXhjTGxGbXRuK2NtMjhlK0FNV2tMR2JKR1pCbEpuZmNmcXhJMmloUFcyQTl0YW00MnBFaXZFUitzTG5kVC95M3BLOCt2Vmk1MVRWTmZOVG9ZTk0wbHlNdldkSURDUmFDekNLRnVKWDdZcGpmODJpdXR2QTc2Qnpua1RGTmM2Z2hpakRoM1hWaGtDQysrZFBMcUg2WFBpMThaUlo5clB1cXpscTlzSVdHOW0weHp4TERZbDR5SHczWlFvclM5YkVFc0QzdmRBNVhNTzRlem1ERm1tYXFWYTh6RWcza0NwL09QR0JjYnd6QzJYVjcwcEIrYm1rUHVCcVdlTC9IMzhKNC81U25pMVl1NGt4SHV0M0x0SDFQSWJqQ0cxL1B1R0lseEdmM3RySUVGMmUydjB5elpnV3RBdkptNG1zaHBpOXBkUHNKSU0rajlqdUFxZTkwTWVNYTNiT0pJejQ0M3dlUTY3RVI1Y3Vjc2pLaDBKUlJ0ai9QM2hxNERoQll2SUx6NVlDSzlRbWt4MWY2NFkvSnFxQzBndDd4ZVkybVJKTVlieXhwSTNtZVlQZ1JGb1pHYnpoUE1MU3lCTU8yeVd4S1diMkgiLCJtYWMiOiI1YzdlMzE3YTNmOTQwMjI2OWM3ZGUyNTE3YjZmNDM0ZTZiY2MxODNiMjc3NzczNGI3ODNiYzMyN2NmNGRjOWYzIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:33:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InNJQTIzeGplRUFzN2xGWTdsczY4TGc9PSIsInZhbHVlIjoiUzZTbktESkdqWUJBYjhOZ3FFTkNvQVlyY2hpaWNOZUlnSDJ0RjhWWEJWRUtzeWY1Sk1iQkVHd0tOMS9PV0VzWXI2QmdRWUQ0NlNDalVkczhBVGtUTHZYdVhlbStDU3B5RkEvaE80Z3k4QmpES01ScEVWN2E1VXdUT016ZDB3Nnp6RGwreHk4eHdrTThrZWZkRmUweHd3V09FRFpvbFl6U0pYQTVseDVNN2lVR1g2b0h3UTVKZGxBVndRSDJPdFRUK3pDRHVQdFU4SElOOE1WSUxiSTRPMGM0aEhUWENYN0NkV1VaMERyS0Q2SjV0K05GWkorNHdNWDZmQ1hRYmh1Rk0yVENSZWJ3MFFOWVRWSHVweDlMUE9VcWJ0ZWFaSEFxRUdubXp6QXFmUlJoR3lOZzh2QTkxWTBIM0lleW1rdzd0dFFScWFXanVEQVY5RUJkNmZtdnVzSzNmNTBzQ051azdNKzRUbnE0Zm5yMDFJVlJNRWJFeWVrV2dqZElRcytCcFU0cWZlc1hkTDcrNzM4RFBtUEErdlNmbUdFUG9EMVYyblhvbjVSS3A1c3dETExNbFZ4RmZQTHZGekV0OTFsRzJlK1ZURkdPWnFtQUdPcFJUa0xlbC9VOTZ4VUViU0pVWktKbld6TlJST0s1dDlGelNJT2h2cGprbEt3RnhKbjciLCJtYWMiOiJjYzUxYzRjZTc3OWVlYjMyYzI5YzZiYjAxMzAwZmU3YjYzYjZhZTk0NmEwMmVlNDI2ZmQ4YzQzNmQ1NmZmNWE2IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:33:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVIYnhvZ3FFU2xJSmFNUlpYOW5McXc9PSIsInZhbHVlIjoiU1JEZkZ2dWVhSWFOR3hYZWJDeCtYWWhPd2w4eTV3eTNCcUxVYzRoZGIxNmYwMTRoZGRyUXIweFlrUzdQQXhjTGxGbXRuK2NtMjhlK0FNV2tMR2JKR1pCbEpuZmNmcXhJMmloUFcyQTl0YW00MnBFaXZFUitzTG5kVC95M3BLOCt2Vmk1MVRWTmZOVG9ZTk0wbHlNdldkSURDUmFDekNLRnVKWDdZcGpmODJpdXR2QTc2Qnpua1RGTmM2Z2hpakRoM1hWaGtDQysrZFBMcUg2WFBpMThaUlo5clB1cXpscTlzSVdHOW0weHp4TERZbDR5SHczWlFvclM5YkVFc0QzdmRBNVhNTzRlem1ERm1tYXFWYTh6RWcza0NwL09QR0JjYnd6QzJYVjcwcEIrYm1rUHVCcVdlTC9IMzhKNC81U25pMVl1NGt4SHV0M0x0SDFQSWJqQ0cxL1B1R0lseEdmM3RySUVGMmUydjB5elpnV3RBdkptNG1zaHBpOXBkUHNKSU0rajlqdUFxZTkwTWVNYTNiT0pJejQ0M3dlUTY3RVI1Y3Vjc2pLaDBKUlJ0ai9QM2hxNERoQll2SUx6NVlDSzlRbWt4MWY2NFkvSnFxQzBndDd4ZVkybVJKTVlieXhwSTNtZVlQZ1JGb1pHYnpoUE1MU3lCTU8yeVd4S1diMkgiLCJtYWMiOiI1YzdlMzE3YTNmOTQwMjI2OWM3ZGUyNTE3YjZmNDM0ZTZiY2MxODNiMjc3NzczNGI3ODNiYzMyN2NmNGRjOWYzIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:33:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InNJQTIzeGplRUFzN2xGWTdsczY4TGc9PSIsInZhbHVlIjoiUzZTbktESkdqWUJBYjhOZ3FFTkNvQVlyY2hpaWNOZUlnSDJ0RjhWWEJWRUtzeWY1Sk1iQkVHd0tOMS9PV0VzWXI2QmdRWUQ0NlNDalVkczhBVGtUTHZYdVhlbStDU3B5RkEvaE80Z3k4QmpES01ScEVWN2E1VXdUT016ZDB3Nnp6RGwreHk4eHdrTThrZWZkRmUweHd3V09FRFpvbFl6U0pYQTVseDVNN2lVR1g2b0h3UTVKZGxBVndRSDJPdFRUK3pDRHVQdFU4SElOOE1WSUxiSTRPMGM0aEhUWENYN0NkV1VaMERyS0Q2SjV0K05GWkorNHdNWDZmQ1hRYmh1Rk0yVENSZWJ3MFFOWVRWSHVweDlMUE9VcWJ0ZWFaSEFxRUdubXp6QXFmUlJoR3lOZzh2QTkxWTBIM0lleW1rdzd0dFFScWFXanVEQVY5RUJkNmZtdnVzSzNmNTBzQ051azdNKzRUbnE0Zm5yMDFJVlJNRWJFeWVrV2dqZElRcytCcFU0cWZlc1hkTDcrNzM4RFBtUEErdlNmbUdFUG9EMVYyblhvbjVSS3A1c3dETExNbFZ4RmZQTHZGekV0OTFsRzJlK1ZURkdPWnFtQUdPcFJUa0xlbC9VOTZ4VUViU0pVWktKbld6TlJST0s1dDlGelNJT2h2cGprbEt3RnhKbjciLCJtYWMiOiJjYzUxYzRjZTc3OWVlYjMyYzI5YzZiYjAxMzAwZmU3YjYzYjZhZTk0NmEwMmVlNDI2ZmQ4YzQzNmQ1NmZmNWE2IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:33:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1875616256\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1077231883 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1077231883\", {\"maxDepth\":0})</script>\n"}}