{"__meta": {"id": "X37be0fe99708db6764f7638c06ea3fc4", "datetime": "2025-07-29 05:30:17", "utime": **********.068787, "method": "GET", "uri": "/api/leads/14", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753767016.165562, "end": **********.068809, "duration": 0.9032471179962158, "duration_str": "903ms", "measures": [{"label": "Booting", "start": 1753767016.165562, "relative_start": 0, "end": 1753767016.96241, "relative_end": 1753767016.96241, "duration": 0.7968480587005615, "duration_str": "797ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753767016.96244, "relative_start": 0.7968780994415283, "end": **********.068813, "relative_end": 4.0531158447265625e-06, "duration": 0.10637307167053223, "duration_str": "106ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46166736, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/leads/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@getLeadForPreview", "namespace": null, "prefix": "", "where": [], "as": "api.leads.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=440\" onclick=\"\">app/Http/Controllers/ContactController.php:440-466</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.01602, "accumulated_duration_str": "16.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0075312, "duration": 0.01276, "duration_str": "12.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 79.65}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0353, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 79.65, "width_percent": 6.305}, {"sql": "select * from `leads` where `id` = '14' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["14", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 446}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.04094, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:446", "source": "app/Http/Controllers/ContactController.php:446", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=446", "ajax": false, "filename": "ContactController.php", "line": "446"}, "connection": "omx_sass_systam_db", "start_percent": 85.955, "width_percent": 5.306}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` in (120)", "type": "query", "params": [], "bindings": ["120"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 446}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.049094, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:446", "source": "app/Http/Controllers/ContactController.php:446", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=446", "ajax": false, "filename": "ContactController.php", "line": "446"}, "connection": "omx_sass_systam_db", "start_percent": 91.261, "width_percent": 4.245}, {"sql": "select * from `pipelines` where `pipelines`.`id` in (30)", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 446}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.052725, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:446", "source": "app/Http/Controllers/ContactController.php:446", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=446", "ajax": false, "filename": "ContactController.php", "line": "446"}, "connection": "omx_sass_systam_db", "start_percent": 95.506, "width_percent": 4.494}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/api/leads/14", "status_code": "<pre class=sf-dump id=sf-dump-619272893 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-619272893\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1731941759 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1731941759\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-513260870 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-513260870\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1558346162 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IjhBWmlsNUJvOWJQL0krQkZaUEt2M3c9PSIsInZhbHVlIjoiYzlNNUpzdTRaR2lWY09ocHM0Nnl6VHduQ29KRWNoRHNuRGkrZFpScHVGT3AzTVFqbk1vVEZJaGtVWmlpeHQ2QnB4aHZWUW0yUGpneXhHWE5oYTZGNklhTVc2NlVicDF3UW1qbjY0cnZVM0JoY2dhYnN5bDZINjlqZjRoMnhDMytWSlMvRFJCcnpXbjNTK1lSVHNHTEFsQ3BoNWhTM3FmWDh2YWoxVDBqc3VrVDBpNGV3eUdKOGtIVHg0UzdvWkhzWC9NWTdGc2ZJZ05GSURqd3NCTkRiRWdDN21MbWY4VHdYRVlNWVYydVFuYklxS2pIVTZTeUV6dmZzRkV3ZSt4TWgxbXNlVzFOSzc1c05hS3YyNXhNV2NWb1lJQm1XTGNmbE0rNWVaQThQQXNsbGgyK2dSWWdGMU1WUkY1aGlLZ2dWREsyeDhYL24rSnM2OFBHT1ovakNWQkl4K1JjMlBSVWo1U0R2TzZNN0NhMzR3bUNQdlhnMUhSVkIvOWhRTTJXcktLay92dlRhdTRvOEkvK1NuNVR6eG9hcTF2T3VrVldJOWE0RDB3ZGVwYVlJNXJkUlpFdEZWTjFIdTNiY2tGNlJLRUVtL1NpSG52dEhDcldrTCt5MjgvNlM4VTlCRkJoMFdMZ3UvNDY5d2ZHZFhBanVNWUFTTm9BbjlZM2hZUU8iLCJtYWMiOiJkNzU0OTAwN2JjZGEzNTViM2MwYjdmOTZhMTQ3ODYyYjkyNjQ2MGNmZTk1YzQ5ZmE5YTYxZjliNTUzZTZhYzIwIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkNvYUdHQ2RJM1lxU05MNU16M1lLNVE9PSIsInZhbHVlIjoibkVQU1A4OVdEaktnbDRFY01XcEU1RXBHRUtPVkpLMjY2TXQ5SGx4Uyt1Z0FXZFhVL3F2aXkyV1FVUTRGQnV0RW1ndjd1eEcyZEhyNFB6MDZNYTFBMTEyRTd0QTg5bGVjb0lySnEydm8wL1RXanNrUitreWZzZTQ4UGpWR3lkVmMyMVFJZENPMU1iaFFiQ1B2NGk4UzBVUERuUjZpeVVPYUFLc3VDSlBWS0pHOUJtRjJ1VmZKc1N6RGV4YTk2NDFFSlMxaHdUNDQyY3NRNE9oYmtUTTBKWFVvNHFiK2N1N0IyRE9SWitHNkN2TXhYOEJsSGg2TkVDTElzK2RlL0NtYzczSUN3dGpEZUZmbG5BVHNVSDlvVU5uRHR0UVFZUVJFZ0RyVlBYbXhKVk5SRUVUVUowNFd4RVNqbTBTRUUwYmR5MUU5QWNVQmgzTlhLNEdPUmhMaVJGNW5yQm9zOFpQR29FeXhIN04vbTBud2FWcDdpQzJ3NnFaMEhiVDlBSUl1S0ZqMzFuWlZJVlJSaW5jek9yYkNCbitaK0FwSDlBa0tOcUtWejEzcDk1UnQ4czhubVkxMkZwbHE2b1VHNDF1dFI0SmdZUGNiamwybFdQWWsxYXRNbGpUeU5iRm41dGQwQmlwNncyclpHOWg0eXJqVzNsY1psV1Q0Uk9xVTN2ZHIiLCJtYWMiOiJlYWJiZDlmZDgxZWQyYmU1YTFhNjhlNDFmZTVhMDRkZjBkNmQ1YWY3MDg5MDQ1ZDkzMjg2MjM4MjI1NWRiOWEzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1558346162\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1907900569 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1907900569\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-787510537 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:30:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFvZWgxSDYreHdpOWlLck01UU5hWnc9PSIsInZhbHVlIjoiaWZWWTRRV3NvMWN3NHJpcXQ4M2lGNFh2aHpabTUrYWJtcXNPcmlvWTVxTTBSMnB2YzdsTTJHcDFtZ1dYSU1KRVgrbVlVc1Q4d0x2dk8zU0UzWkY1Z1FoMERMQVlBWkFocUpURlVlaDFaY2Q2YktZd3kyM253Q0lRc0haQVJWNFNRakZxUno1d1JPN0V3MHMwNVo1VHk4OHF6N3h1bW1MMXd0NjVteUphMHpjaEJPazhZV0VXWGlPZGVzc2lRdUlaaGN4UklEV0JlNHZuQ2krMEFQZDFQaERWenNwZ0J2ajcvWVl1MTdhY0o1N2c0eGVGbFZTK1dCczNZNXROYktMdWtQZlhIVlRHaW5LTjRkQmJxbzkweUdHcVZYQ2V4bzlWWFREanQ0L2xGSTFpeC94RUxQSVc1M0ppcWJmb3VkTHV6K0ZIS0F3UHZwbXhneitSNHVDKy9RZW5ValdCaU4wMVdRNnhZL3lWVlBPMVV5QUlOWGtKNHNmZXNvYUpXMXQ5anVrRXkzNGRGNHYxZmp2ZzVTMEJjdXlBUSsrQzdweHVNbVkrVXM2SlZvSXJtNFBLTnNycEJCMERFeGIyejk0WURCZDZZS0tTRWlMa3QwVm81cDJFSjZlYjU1cW83QXFNTTBqN3ZWTk1KZDNnbHUxa0JqN29Lam5PcWcweGpnUFoiLCJtYWMiOiJhMGJiMzU2NzcwNzdjOGVhODg1ZDY5ZjczMWE0ZmYxZjRkN2JjNDI1MWViMDVhY2I2Yzk4ZmY2MjczNzBiNjQyIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:30:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImMvdUJNWE1qVnRMNUtaUkgzQ2dUOXc9PSIsInZhbHVlIjoid3I2eE1sZ29sa2tUL0ZZU2xzQXYySGxnNlc2LzdUZEZlRWRaK1ZZbGtRcXRWeVBKUSs4SDUrM1NVL25taGlWaENsSzEwRm5MVEV5Rk5TSGtGUytSNC9WVEdkSlVZL0VIUkd5MFdjbmZHZ1RuUHFGNFcrNFJFYjFvdFZkbTU1R210RlFtbFcvQm8xenkzT1FpR3lFamFxL3NZRlBWS3lkL2xzRmZybHNwZUZwSFZ5bzl3ZnFqVXIyUlNHeVNTL3F3SWtWSkJEQVFSOHpkZ0hkWXpxY1VlS29QREVNeWdJWHRkbkhSN0xxNWNXcGxmL1JCcnp2OTZnUEtoRzAvcnBhRExGb3RsM2RJTXF1RnkyaGxLVnNMc2dvZWU4VXg1c3MrV1VNTjgvV2NrTGRLc0lQWUZ0M2ZRanEyaUE1eW8zTXB1TlNjRXIyWTMrU2RjNlo4ZmVPeU9URS9CNU94K1N5Q080N1lzejB3VFlRTnJJMWdRdlRiQ3lmOTIwR2k1Wk5ZTGRWNEVtWEpjNzZWai9CT1YwNTlPVzVVZ2thNVNmZHZGODQ4Wkp4U20xOHFQRWdCS29TelVUWGRNenlyVWg4Zm5JcjFVQnZqcTNwQTdxc0VvOFViYWxqT1dzZG13SWEzNWp4Uk0zbEwwV0lscHZkcEI3Zk96Um9kWWo4ZWcwcFQiLCJtYWMiOiJmODZhMzQwMjAxYTJmNmZhYWFlMDQyMzE3MGRjYjI2Mjk5NWZjZmFjY2NhODUxYjVkNzU4ZDkyMGFiMGQzNjMyIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:30:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFvZWgxSDYreHdpOWlLck01UU5hWnc9PSIsInZhbHVlIjoiaWZWWTRRV3NvMWN3NHJpcXQ4M2lGNFh2aHpabTUrYWJtcXNPcmlvWTVxTTBSMnB2YzdsTTJHcDFtZ1dYSU1KRVgrbVlVc1Q4d0x2dk8zU0UzWkY1Z1FoMERMQVlBWkFocUpURlVlaDFaY2Q2YktZd3kyM253Q0lRc0haQVJWNFNRakZxUno1d1JPN0V3MHMwNVo1VHk4OHF6N3h1bW1MMXd0NjVteUphMHpjaEJPazhZV0VXWGlPZGVzc2lRdUlaaGN4UklEV0JlNHZuQ2krMEFQZDFQaERWenNwZ0J2ajcvWVl1MTdhY0o1N2c0eGVGbFZTK1dCczNZNXROYktMdWtQZlhIVlRHaW5LTjRkQmJxbzkweUdHcVZYQ2V4bzlWWFREanQ0L2xGSTFpeC94RUxQSVc1M0ppcWJmb3VkTHV6K0ZIS0F3UHZwbXhneitSNHVDKy9RZW5ValdCaU4wMVdRNnhZL3lWVlBPMVV5QUlOWGtKNHNmZXNvYUpXMXQ5anVrRXkzNGRGNHYxZmp2ZzVTMEJjdXlBUSsrQzdweHVNbVkrVXM2SlZvSXJtNFBLTnNycEJCMERFeGIyejk0WURCZDZZS0tTRWlMa3QwVm81cDJFSjZlYjU1cW83QXFNTTBqN3ZWTk1KZDNnbHUxa0JqN29Lam5PcWcweGpnUFoiLCJtYWMiOiJhMGJiMzU2NzcwNzdjOGVhODg1ZDY5ZjczMWE0ZmYxZjRkN2JjNDI1MWViMDVhY2I2Yzk4ZmY2MjczNzBiNjQyIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:30:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImMvdUJNWE1qVnRMNUtaUkgzQ2dUOXc9PSIsInZhbHVlIjoid3I2eE1sZ29sa2tUL0ZZU2xzQXYySGxnNlc2LzdUZEZlRWRaK1ZZbGtRcXRWeVBKUSs4SDUrM1NVL25taGlWaENsSzEwRm5MVEV5Rk5TSGtGUytSNC9WVEdkSlVZL0VIUkd5MFdjbmZHZ1RuUHFGNFcrNFJFYjFvdFZkbTU1R210RlFtbFcvQm8xenkzT1FpR3lFamFxL3NZRlBWS3lkL2xzRmZybHNwZUZwSFZ5bzl3ZnFqVXIyUlNHeVNTL3F3SWtWSkJEQVFSOHpkZ0hkWXpxY1VlS29QREVNeWdJWHRkbkhSN0xxNWNXcGxmL1JCcnp2OTZnUEtoRzAvcnBhRExGb3RsM2RJTXF1RnkyaGxLVnNMc2dvZWU4VXg1c3MrV1VNTjgvV2NrTGRLc0lQWUZ0M2ZRanEyaUE1eW8zTXB1TlNjRXIyWTMrU2RjNlo4ZmVPeU9URS9CNU94K1N5Q080N1lzejB3VFlRTnJJMWdRdlRiQ3lmOTIwR2k1Wk5ZTGRWNEVtWEpjNzZWai9CT1YwNTlPVzVVZ2thNVNmZHZGODQ4Wkp4U20xOHFQRWdCS29TelVUWGRNenlyVWg4Zm5JcjFVQnZqcTNwQTdxc0VvOFViYWxqT1dzZG13SWEzNWp4Uk0zbEwwV0lscHZkcEI3Zk96Um9kWWo4ZWcwcFQiLCJtYWMiOiJmODZhMzQwMjAxYTJmNmZhYWFlMDQyMzE3MGRjYjI2Mjk5NWZjZmFjY2NhODUxYjVkNzU4ZDkyMGFiMGQzNjMyIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:30:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-787510537\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-124198786 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-124198786\", {\"maxDepth\":0})</script>\n"}}