{"__meta": {"id": "X087aac76f4247e96c3f524102f65bd47", "datetime": "2025-07-29 05:07:39", "utime": **********.396722, "method": "GET", "uri": "/leads/17/labels", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753765658.242985, "end": **********.396743, "duration": 1.1537580490112305, "duration_str": "1.15s", "measures": [{"label": "Booting", "start": 1753765658.242985, "relative_start": 0, "end": **********.006254, "relative_end": **********.006254, "duration": 0.7632689476013184, "duration_str": "763ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.006289, "relative_start": 0.7633039951324463, "end": **********.396745, "relative_end": 1.9073486328125e-06, "duration": 0.390455961227417, "duration_str": "390ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 55010600, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x leads.labels", "param_count": null, "params": [], "start": **********.383965, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\omx-new-saas\\resources\\views/leads/labels.blade.phpleads.labels", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fresources%2Fviews%2Fleads%2Flabels.blade.php&line=1", "ajax": false, "filename": "labels.blade.php", "line": "?"}, "render_count": 1, "name_original": "leads.labels"}]}, "route": {"uri": "GET leads/{id}/labels", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\LeadController@labels", "namespace": null, "prefix": "", "where": [], "as": "leads.labels", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=1195\" onclick=\"\">app/Http/Controllers/LeadController.php:1195-1224</a>"}, "queries": {"nb_statements": 12, "nb_failed_statements": 0, "accumulated_duration": 0.02997, "accumulated_duration_str": "29.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0725088, "duration": 0.0106, "duration_str": "10.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 35.369}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.095455, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 35.369, "width_percent": 3.203}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (84) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 1197}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.106561, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 38.572, "width_percent": 2.903}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (84) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 1197}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.11147, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 41.475, "width_percent": 2.736}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 1197}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1167421, "duration": 0.00311, "duration_str": "3.11ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 44.211, "width_percent": 10.377}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.1521158, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "omx_sass_systam_db", "start_percent": 54.588, "width_percent": 7.875}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.177814, "duration": 0.00745, "duration_str": "7.45ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "omx_sass_systam_db", "start_percent": 62.462, "width_percent": 24.858}, {"sql": "select * from `leads` where `leads`.`id` = '17' limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 1199}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.343724, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:1199", "source": "app/Http/Controllers/LeadController.php:1199", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=1199", "ajax": false, "filename": "LeadController.php", "line": "1199"}, "connection": "omx_sass_systam_db", "start_percent": 87.321, "width_percent": 2.769}, {"sql": "select * from `labels` where `pipeline_id` = 30 and `created_by` = 84", "type": "query", "params": [], "bindings": ["30", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 1202}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.347928, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:1202", "source": "app/Http/Controllers/LeadController.php:1202", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=1202", "ajax": false, "filename": "LeadController.php", "line": "1202"}, "connection": "omx_sass_systam_db", "start_percent": 90.09, "width_percent": 2.102}, {"sql": "select * from `labels` where `id` in ('94')", "type": "query", "params": [], "bindings": ["94"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 54}, {"index": 21, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 138}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 1203}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.353039, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Lead.php:54", "source": "app/Models/Lead.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=54", "ajax": false, "filename": "Lead.php", "line": "54"}, "connection": "omx_sass_systam_db", "start_percent": 92.192, "width_percent": 2.736}, {"sql": "select * from `labels` where `id` in ('94')", "type": "query", "params": [], "bindings": ["94"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 54}, {"index": 21, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 140}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 1203}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.356198, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Lead.php:54", "source": "app/Models/Lead.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=54", "ajax": false, "filename": "Lead.php", "line": "54"}, "connection": "omx_sass_systam_db", "start_percent": 94.928, "width_percent": 2.369}, {"sql": "select * from `labels` where `id` in ('[{\\\"id\\\":94', '\\\"name\\\":\\\"red\\\"', '\\\"color\\\":\\\"danger\\\"', '\\\"pipeline_id\\\":30', '\\\"created_by\\\":84', '\\\"created_at\\\":\\\"2025-07-29T05:06:10.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-29T05:06:10.000000Z\\\"}]')", "type": "query", "params": [], "bindings": ["[{&quot;id&quot;:94", "&quot;name&quot;:&quot;red&quot;", "&quot;color&quot;:&quot;danger&quot;", "&quot;pipeline_id&quot;:30", "&quot;created_by&quot;:84", "&quot;created_at&quot;:&quot;2025-07-29T05:06:10.000000Z&quot;", "&quot;updated_at&quot;:&quot;2025-07-29T05:06:10.000000Z&quot;}]"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 140}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 1203}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3625271, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Lead.php:140", "source": "app/Models/Lead.php:140", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=140", "ajax": false, "filename": "Lead.php", "line": "140"}, "connection": "omx_sass_systam_db", "start_percent": 97.297, "width_percent": 2.703}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 1594, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 1176, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Label": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLabel.php&line=1", "ajax": false, "filename": "Label.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2775, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit lead, result => true, user => 84, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-172901461 data-indent-pad=\"  \"><span class=sf-dump-note>edit lead</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit lead</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>84</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-172901461\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.342178, "xdebug_link": null}]}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/leads/17/labels", "status_code": "<pre class=sf-dump id=sf-dump-2127871321 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2127871321\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-640956761 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-640956761\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1380077089 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1380077089\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-735727097 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/leads/17</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IlVBcldCcEFTS1A5TjlZM2xtTDUrQVE9PSIsInZhbHVlIjoiMzR4R1pMYlpYc21KK3B5djZ4WEpNcTUzdlNHSG51RmNROUlxTE9DVFAyYVU1SGtxVHRMV3dSUWdIdTczSnNqQytBWTlUeWRRTDVqb1lqMEs5YS9kL3Y0cDRaMC9iNTkxUFErTUdIaDRQbTd4VGdUMWJUUUpBOEd1VjlJRmswcm5TMjZNeHQyRmZwdFpMekZMdWxXU0lFMFpYajAzM1IySU5iS2c3QUplSTh0Vkl4eWZUQ3B1cWpxcmhEZnlyYVNDVW9KZ1Q2OXhCbjdnblY1czlINTkzOGhkOWJZRGZyVC93cUpwYi9xMURwS01kZWt5VE4zbERxVmF0YlZpWnJNSGoxM3dVdVpuSVBmQmNQN0xjVjhuOC9nNUpFcnFweG1nYk81N2twZGh2a2xqeldJU051d0twSzB1OWtuUHFReW5zS01LcllSTjcralViTlJpSFhEMEx5SnNGWVlOZkFFTjliTElxclE0dWJHTjB0Z3A3WGJrN2h1Q2dRT1RaR3l0U2Znd2JzOTZEeFE3NTdjVzFjODF4dGNoVHZURUVUY3BhTXZTN3orUEdPcThrMVV5QldRak5reldUT2xmYkEwTzlNZ3V3YlZmMldGOHRJVlhkODNzZmxPSExrL3N1TS8rNWhFY3JHNVZvQlZDM2lRV2VqNjJGcGo0emNRYjM0Sk8iLCJtYWMiOiI1Yjc5NzNkMWFiZWUwZGM3YTY5MjJmZDE4ZjliZGQ4MzZkOTA0YzgwZDA0NDhjN2M5ODBjMDA1ODg0MjUzNjY2IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IjZYK2dtSEtiSk9TWWhvZmMvRWhMR0E9PSIsInZhbHVlIjoibUV0NVhkd2Q1Nzh6em5uVnpnWGpqMmVSY1g3VVpWaFFBSm92bnoxWENIenU2UTZRWkZSbXMzcTM4bkxNby9QQ0I4ejdXRmZib3ltWnhXaFNUUUt2b1FxdGlRNjdPV2RRUWVhUmRBM2Y3MVhoNXpCTVYzc0NHNXhyWEd2UmorYlhpeFFzWkVRMGtraXZJVk9nZ1VmN2JPTDFRenFkTVdIS21tOFptRmRnQmppZ0twRFJuNkRHNUdhWm02R2xzN2pUNEJhcEdwdGFMRmJ0aFBxa2x2anZmVjdOTnBBVXR5SjdnYlREOGRTN0x1ZjhzRjJnMktnK204WnY5R0RFNzFhV2x6cHg0Mk1NVk5MUVczeWsyM2tQTkJHVVdXNjBsaE1reFd6dTB4M1RnZnFYbnNRK1kzdmJIL1lvUEJxVVdPSjJUczNEdzdSZld0d3VCWmNNclBudnljNHVwT1pMRUUxMCtCaFFyVXBLODVrL2dxYVNEVWR2V3IydFZPazBTV29kZEM4RlpyaStrU0NBSklZUVg0cGlCNWx4eGNKRDlPT2ZUby93NEpON0YvSUU2NnJqaWg5YXJ2SVpXNXlXSU1CYngra1pNZ2YxeTRWcVNsb0VsSStsek51bVBHY3EzQVZpdVNwQlJwSlhDYUhIbDhXZVl0NFFtd3pjRzhMN2c4UVMiLCJtYWMiOiI3MWM1MjlkY2RmOThiMjc2MGQ4OTI3MWVmYTI2ODQ4MGZhNTg4MzM2M2M3NTQ1ZmQ2ODA2ZDkxNTI1ZTkyZWEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-735727097\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1933937352 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1933937352\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-912548275 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:07:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJFdHFrRDRvU2JKcDBHT3M1R1hidnc9PSIsInZhbHVlIjoiNXBxZWZVSGswbzBacDB3Nkp5K2xPYjV2L0REZTdVL2YyREMxc2duNVIxTW1ram13UWM0eEdaZ3NMV1R3cytZMnZYVUVuSS9jNFY4WWVZRmorNzFTdmNQcUN5ZFdvRUNHaldTbkYzc1BzZTg3ZnBwaWdZVFBQckh6OWRnQy83RFhpSFdwQ3ZaamFmZDVmM1pwK1BzQWVsMW1oaW9sT09qU0RycmZWOTVWN0JnQnpyQlpqT2IrMUswUEl4SkdaVFZyZHlPeUJRMTByTXhGcXNMbldDeWdPSnU0cWphSmpra3lPSWJBdmNtc05yakQxdWNOQm1hTEhzc1c1LzJYOWMrYUJESytXcFN6RkcyclpPT3hNRi9XaVhkTXVUelZvNTFwU0VUZCt1YW0wY1N1M1NsdnlKMzlBL29nVmNMOUFvcEh1TWhVTjRlVVEycjJKK2dOWWVRQ3JWK1ZWRGFDMXVHYUNoY2pIS0VHeVl6Z1lBZHZZQ3IwcEUxZnZGMlRpWlRVRXJMSWJodmVMcFlRbW9xZm4ycytIM0hsUWtyT0l2MXZ6d0paOVN5V2ZWWXB4bzlFM1daWVBYdWtjWUExdEFJRGVlckUzV3IwcHVDS1F4OThTdTBuRElqU1lVdy9tQW94d3ZBVTRKTzhRdDFPR3NIYXBoT3pQSTZVOWpxZSt5QzciLCJtYWMiOiI3MTc5ZTcxZmY2ZDQ0NWIzYThhYWFjNzhjOGVlMzEwYjYzOTVhZmRkMzA0ZmQ4NTM1NDFmY2Y2ZDZiYmExMGI4IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:07:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjUraVFvVkRwWmlFNDVtdEE5T1EzdXc9PSIsInZhbHVlIjoibjA5Tkp4VW5UR2RjV2dRTlVEWm1QVUJSRmV2SWw5WnlNSXVvRTFieTRUU2M0d1R4WjhiMFVuak9NV0djLzdZMjJmTTFXTmtoUkgwTFYxVDlMTStSaUFsMjI4TDdQNjVrc0o0UTJObzU3MHVFNEFXaDd0NWxwTlR5Rlk5TFVlZDJNZTlJSk13RnNvWllGdDZOS1U3emdoYVFqYnFITkUxL0J2Y1dtNWF1Nm5sbTdhQmVZZlEyMmFsK2tMTkVKSEpVNVlsVEpOcnhVZGF4NmFocmNpeEtNUEdyK0loNWtjb2ZiRTRveE5VdW1sRW9aREthSG1aRjJGNkJDQS9VbGI1bk9IZE5lbU4rRm45NURocnpXck5QRDlNWFpPNnBCR1M4RC9sTXhkbzQxSDd4L21DK0xqYS9DVzRjLzhBRndEeG0rSi83S0VzODdjUytiVXNrblgvWmV3WndvQnpVRWdkMDd2RGVyS083Y1E0eGJJVEVsMjdzWTdqcGdYdDR0S1AxdXliS1ZiY1FxMTA5ZUMra0M4NkZzaDhTbFFXcTRSMFZNd0M3T2hVaU5FakMxMWU1R3o0QlBpZzlsQU9wdkYxd0dRU0M3dnFEbWlDTlN5eUZJeUVIVDRrMmtsSkRkWGJhY2VUWDRLWkV5N3pseDl2OG1CYno3bnJhb1VJYzNnczYiLCJtYWMiOiJmNzkwYThkY2RkOTFjODY4Y2JmZjNmNjdjMDIwNDdlOTgzZmEwYWNmM2E1ZmViNDU0YjBiNWNmOTgzNmRjZmM3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:07:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJFdHFrRDRvU2JKcDBHT3M1R1hidnc9PSIsInZhbHVlIjoiNXBxZWZVSGswbzBacDB3Nkp5K2xPYjV2L0REZTdVL2YyREMxc2duNVIxTW1ram13UWM0eEdaZ3NMV1R3cytZMnZYVUVuSS9jNFY4WWVZRmorNzFTdmNQcUN5ZFdvRUNHaldTbkYzc1BzZTg3ZnBwaWdZVFBQckh6OWRnQy83RFhpSFdwQ3ZaamFmZDVmM1pwK1BzQWVsMW1oaW9sT09qU0RycmZWOTVWN0JnQnpyQlpqT2IrMUswUEl4SkdaVFZyZHlPeUJRMTByTXhGcXNMbldDeWdPSnU0cWphSmpra3lPSWJBdmNtc05yakQxdWNOQm1hTEhzc1c1LzJYOWMrYUJESytXcFN6RkcyclpPT3hNRi9XaVhkTXVUelZvNTFwU0VUZCt1YW0wY1N1M1NsdnlKMzlBL29nVmNMOUFvcEh1TWhVTjRlVVEycjJKK2dOWWVRQ3JWK1ZWRGFDMXVHYUNoY2pIS0VHeVl6Z1lBZHZZQ3IwcEUxZnZGMlRpWlRVRXJMSWJodmVMcFlRbW9xZm4ycytIM0hsUWtyT0l2MXZ6d0paOVN5V2ZWWXB4bzlFM1daWVBYdWtjWUExdEFJRGVlckUzV3IwcHVDS1F4OThTdTBuRElqU1lVdy9tQW94d3ZBVTRKTzhRdDFPR3NIYXBoT3pQSTZVOWpxZSt5QzciLCJtYWMiOiI3MTc5ZTcxZmY2ZDQ0NWIzYThhYWFjNzhjOGVlMzEwYjYzOTVhZmRkMzA0ZmQ4NTM1NDFmY2Y2ZDZiYmExMGI4IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:07:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjUraVFvVkRwWmlFNDVtdEE5T1EzdXc9PSIsInZhbHVlIjoibjA5Tkp4VW5UR2RjV2dRTlVEWm1QVUJSRmV2SWw5WnlNSXVvRTFieTRUU2M0d1R4WjhiMFVuak9NV0djLzdZMjJmTTFXTmtoUkgwTFYxVDlMTStSaUFsMjI4TDdQNjVrc0o0UTJObzU3MHVFNEFXaDd0NWxwTlR5Rlk5TFVlZDJNZTlJSk13RnNvWllGdDZOS1U3emdoYVFqYnFITkUxL0J2Y1dtNWF1Nm5sbTdhQmVZZlEyMmFsK2tMTkVKSEpVNVlsVEpOcnhVZGF4NmFocmNpeEtNUEdyK0loNWtjb2ZiRTRveE5VdW1sRW9aREthSG1aRjJGNkJDQS9VbGI1bk9IZE5lbU4rRm45NURocnpXck5QRDlNWFpPNnBCR1M4RC9sTXhkbzQxSDd4L21DK0xqYS9DVzRjLzhBRndEeG0rSi83S0VzODdjUytiVXNrblgvWmV3WndvQnpVRWdkMDd2RGVyS083Y1E0eGJJVEVsMjdzWTdqcGdYdDR0S1AxdXliS1ZiY1FxMTA5ZUMra0M4NkZzaDhTbFFXcTRSMFZNd0M3T2hVaU5FakMxMWU1R3o0QlBpZzlsQU9wdkYxd0dRU0M3dnFEbWlDTlN5eUZJeUVIVDRrMmtsSkRkWGJhY2VUWDRLWkV5N3pseDl2OG1CYno3bnJhb1VJYzNnczYiLCJtYWMiOiJmNzkwYThkY2RkOTFjODY4Y2JmZjNmNjdjMDIwNDdlOTgzZmEwYWNmM2E1ZmViNDU0YjBiNWNmOTgzNmRjZmM3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:07:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-912548275\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-116212262 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-116212262\", {\"maxDepth\":0})</script>\n"}}