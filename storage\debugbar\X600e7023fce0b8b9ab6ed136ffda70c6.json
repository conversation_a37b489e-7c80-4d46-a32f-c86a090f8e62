{"__meta": {"id": "X600e7023fce0b8b9ab6ed136ffda70c6", "datetime": "2025-07-29 05:02:45", "utime": **********.997625, "method": "POST", "uri": "/leads", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 9, "messages": [{"message": "[05:02:40] LOG.info: Lead creation started {\n    \"user_id\": 84,\n    \"request_data\": {\n        \"_token\": \"pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U\",\n        \"name\": \"<PERSON><PERSON>\",\n        \"email\": \"al<PERSON><PERSON><PERSON>@gmail.com\",\n        \"phone\": \"8965478956\",\n        \"date_of_birth\": \"2004-05-29\",\n        \"contact_type\": \"Lead\",\n        \"tags\": null,\n        \"postal_code\": \"734429\",\n        \"city\": \"Siliguri\",\n        \"state\": \"West Bengal\",\n        \"country\": \"India\",\n        \"business_name\": \"Smart Internz\",\n        \"business_gst\": \"Smart Internz\",\n        \"business_state\": \"West Bengal\",\n        \"business_postal_code\": \"85888500\",\n        \"business_address\": \"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429\\r\\nnaxalbari\",\n        \"user_id\": \"84\",\n        \"subject\": \"New Contact\",\n        \"pipeline_id\": null,\n        \"stage_id\": null,\n        \"dnd_settings\": \"{\\\"all\\\":false,\\\"emails\\\":false,\\\"whatsapp\\\":false,\\\"sms\\\":false,\\\"calls\\\":false}\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.816847, "xdebug_link": null, "collector": "log"}, {"message": "[05:02:41] LOG.info: Creating lead with data {\n    \"pipeline_id\": 30,\n    \"stage_id\": 118,\n    \"user_id\": \"84\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.549323, "xdebug_link": null, "collector": "log"}, {"message": "[05:02:41] LOG.info: Lead saved successfully {\n    \"lead_id\": 18\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.560521, "xdebug_link": null, "collector": "log"}, {"message": "[05:02:41] LOG.info: UserLead relationships created successfully", "message_html": null, "is_string": false, "label": "info", "time": **********.602152, "xdebug_link": null, "collector": "log"}, {"message": "[05:02:41] LOG.warning: Error building webhook data for related models {\n    \"entity_class\": \"App\\\\Models\\\\Lead\",\n    \"error\": \"Undefined array key \\\"labels\\\"\"\n}", "message_html": null, "is_string": false, "label": "warning", "time": **********.939427, "xdebug_link": null, "collector": "log"}, {"message": "[05:02:41] LOG.info: Starting webhook dispatch for action: crm.lead_created {\n    \"timestamp\": \"2025-07-29T05:02:41.943922Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"crm.lead_created\",\n    \"user_id\": 84,\n    \"entity_type\": \"Lead\",\n    \"entity_id\": 18,\n    \"status\": \"dispatching\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.983821, "xdebug_link": null, "collector": "log"}, {"message": "[05:02:45] LOG.error: <PERSON><PERSON><PERSON> failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2040 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {\n    \"timestamp\": \"2025-07-29T05:02:45.961962Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"crm.lead_created\",\n    \"module_name\": \"OMX FLOW\",\n    \"webhook_url\": \"http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n    \"status\": \"failed\",\n    \"status_code\": null,\n    \"response_time_ms\": 3971,\n    \"user_id\": 84,\n    \"entity_id\": 18,\n    \"entity_type\": \"Lead\",\n    \"request_payload\": {\n        \"action\": \"crm.lead_created\",\n        \"timestamp\": \"2025-07-29T05:02:41.991019Z\",\n        \"data\": {\n            \"name\": \"<PERSON><PERSON>\",\n            \"email\": \"alik<PERSON><PERSON>@gmail.com\",\n            \"phone\": \"8965478956\",\n            \"subject\": \"New Contact\",\n            \"user_id\": \"84\",\n            \"pipeline_id\": 30,\n            \"stage_id\": 118,\n            \"created_by\": 84,\n            \"date\": \"2025-07-29\",\n            \"date_of_birth\": \"2004-05-29\",\n            \"type\": null,\n            \"status\": \"active\",\n            \"opportunity_info\": null,\n            \"opportunity_description\": null,\n            \"opportunity_source\": null,\n            \"lead_value\": null,\n            \"next_follow_up_date\": null,\n            \"contact_type\": \"Lead\",\n            \"tags\": null,\n            \"postal_code\": \"734429\",\n            \"city\": \"Siliguri\",\n            \"state\": \"West Bengal\",\n            \"country\": \"India\",\n            \"business_name\": \"Smart Internz\",\n            \"business_gst\": \"Smart Internz\",\n            \"business_state\": \"West Bengal\",\n            \"business_postal_code\": \"85888500\",\n            \"business_address\": \"GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429\\r\\nnaxalbari\",\n            \"dnd_settings\": \"{\\\"all\\\":false,\\\"emails\\\":false,\\\"whatsapp\\\":false,\\\"sms\\\":false,\\\"calls\\\":false}\",\n            \"updated_at\": \"2025-07-29T05:02:41.000000Z\",\n            \"created_at\": \"2025-07-29T05:02:41.000000Z\",\n            \"id\": 18,\n            \"stage\": {\n                \"id\": 118,\n                \"name\": \"New Leads\",\n                \"pipeline_id\": 30,\n                \"created_by\": 84,\n                \"order\": 1,\n                \"created_at\": \"2025-07-28T11:09:42.000000Z\",\n                \"updated_at\": \"2025-07-28T11:09:42.000000Z\"\n            },\n            \"pipeline\": {\n                \"id\": 30,\n                \"name\": \"OMX Digital AI\",\n                \"created_by\": 84,\n                \"is_deleted\": 0,\n                \"created_at\": \"2025-07-28T11:09:42.000000Z\",\n                \"updated_at\": \"2025-07-28T11:09:42.000000Z\"\n            },\n            \"triggered_by\": {\n                \"user_id\": 84,\n                \"email\": \"<EMAIL>\",\n                \"name\": \"parichay  Bot flow\",\n                \"type\": \"company\"\n            }\n        },\n        \"user_id\": 84,\n        \"source\": {\n            \"system\": \"krishna\",\n            \"version\": \"1.0\",\n            \"url\": \"http:\\/\\/localhost:8000\"\n        }\n    },\n    \"error_message\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2040 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n    \"response_body\": null\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.962694, "xdebug_link": null, "collector": "log"}, {"message": "[05:02:45] LOG.warning: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {\n    \"timestamp\": \"2025-07-29T05:02:45.963240Z\",\n    \"source\": \"crm_webhook_system\",\n    \"action\": \"crm.lead_created\",\n    \"user_id\": 84,\n    \"status\": \"completed\",\n    \"total_modules\": 1,\n    \"successful_modules\": 0,\n    \"failed_modules\": 1,\n    \"modules\": [\n        \"OMX FLOW\"\n    ],\n    \"results\": {\n        \"OMX FLOW\": {\n            \"success\": false,\n            \"error\": \"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2040 ms: Couldn't connect to server (see https:\\/\\/curl.haxx.se\\/libcurl\\/c\\/libcurl-errors.html) for http:\\/\\/127.0.0.1:2000\\/external-crm\\/webhook\",\n            \"integration\": \"OMX FLOW\"\n        }\n    }\n}", "message_html": null, "is_string": false, "label": "warning", "time": **********.963752, "xdebug_link": null, "collector": "log"}, {"message": "[05:02:45] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 84,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/leads\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.991586, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753765359.703264, "end": **********.997664, "duration": 6.294399976730347, "duration_str": "6.29s", "measures": [{"label": "Booting", "start": 1753765359.703264, "relative_start": 0, "end": **********.705831, "relative_end": **********.705831, "duration": 1.0025670528411865, "duration_str": "1s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.705853, "relative_start": 1.0025889873504639, "end": **********.997666, "relative_end": 1.9073486328125e-06, "duration": 5.291812896728516, "duration_str": "5.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 56926832, "peak_usage_str": "54MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST leads", "middleware": "web, verified, auth, XSS", "as": "leads.store", "controller": "App\\Http\\Controllers\\LeadController@store", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=257\" onclick=\"\">app/Http/Controllers/LeadController.php:257-614</a>"}, "queries": {"nb_statements": 20, "nb_failed_statements": 0, "accumulated_duration": 0.042140000000000004, "accumulated_duration_str": "42.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.786283, "duration": 0.0054800000000000005, "duration_str": "5.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 13.004}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8111339, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 13.004, "width_percent": 2.231}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (84) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 267}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.827543, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 15.235, "width_percent": 2.397}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (84) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 267}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.833908, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 17.632, "width_percent": 3.275}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 267}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.843572, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 20.907, "width_percent": 8.448}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.891372, "duration": 0.00259, "duration_str": "2.59ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "omx_sass_systam_db", "start_percent": 29.355, "width_percent": 6.146}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 285}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 322}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 199}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 428}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}], "start": **********.9268038, "duration": 0.0076100000000000004, "duration_str": "7.61ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:285", "source": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php:285", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=285", "ajax": false, "filename": "PermissionRegistrar.php", "line": "285"}, "connection": "omx_sass_systam_db", "start_percent": 35.501, "width_percent": 18.059}, {"sql": "select * from `pipelines` where `created_by` = 84 and `id` = 30 limit 1", "type": "query", "params": [], "bindings": ["84", "30"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 358}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5373511, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:358", "source": "app/Http/Controllers/LeadController.php:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=358", "ajax": false, "filename": "LeadController.php", "line": "358"}, "connection": "omx_sass_systam_db", "start_percent": 53.56, "width_percent": 2.302}, {"sql": "select * from `lead_stages` where `pipeline_id` = 30 limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 370}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.544014, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:370", "source": "app/Http/Controllers/LeadController.php:370", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=370", "ajax": false, "filename": "LeadController.php", "line": "370"}, "connection": "omx_sass_systam_db", "start_percent": 55.861, "width_percent": 1.756}, {"sql": "insert into `leads` (`name`, `email`, `phone`, `subject`, `user_id`, `pipeline_id`, `stage_id`, `created_by`, `date`, `date_of_birth`, `type`, `status`, `opportunity_info`, `opportunity_description`, `opportunity_source`, `lead_value`, `next_follow_up_date`, `contact_type`, `tags`, `postal_code`, `city`, `state`, `country`, `business_name`, `business_gst`, `business_state`, `business_postal_code`, `business_address`, `dnd_settings`, `updated_at`, `created_at`) values ('<PERSON><PERSON> Sarkar', 'al<PERSON><PERSON><PERSON>@gmail.com', '8965478956', 'New Contact', '84', 30, 118, 84, '2025-07-29', '2004-05-29', '', 'active', '', '', '', '', '', 'Lead', '', '734429', 'Siliguri', 'West Bengal', 'India', 'Smart Internz', 'Smart Internz', 'West Bengal', '85888500', 'GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429\\r\\nnaxalbari', '{\\\"all\\\":false,\\\"emails\\\":false,\\\"whatsapp\\\":false,\\\"sms\\\":false,\\\"calls\\\":false}', '2025-07-29 05:02:41', '2025-07-29 05:02:41')", "type": "query", "params": [], "bindings": ["<PERSON><PERSON>", "alik<PERSON><PERSON>@gmail.com", "8965478956", "New Contact", "84", "30", "118", "84", "2025-07-29", "2004-05-29", "", "active", "", "", "", "", "", "Lead", "", "734429", "Siliguri", "West Bengal", "India", "Smart Internz", "Smart Internz", "West Bengal", "85888500", "GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429\r\nnaxalbari", "{&quot;all&quot;:false,&quot;emails&quot;:false,&quot;whatsapp&quot;:false,&quot;sms&quot;:false,&quot;calls&quot;:false}", "2025-07-29 05:02:41", "2025-07-29 05:02:41"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 456}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.551635, "duration": 0.00328, "duration_str": "3.28ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:456", "source": "app/Http/Controllers/LeadController.php:456", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=456", "ajax": false, "filename": "LeadController.php", "line": "456"}, "connection": "omx_sass_systam_db", "start_percent": 57.617, "width_percent": 7.784}, {"sql": "insert into `user_leads` (`user_id`, `lead_id`, `updated_at`, `created_at`) values ('84', 18, '2025-07-29 05:02:41', '2025-07-29 05:02:41')", "type": "query", "params": [], "bindings": ["84", "18", "2025-07-29 05:02:41", "2025-07-29 05:02:41"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 481}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.595596, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:481", "source": "app/Http/Controllers/LeadController.php:481", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=481", "ajax": false, "filename": "LeadController.php", "line": "481"}, "connection": "omx_sass_systam_db", "start_percent": 65.401, "width_percent": 8.614}, {"sql": "select * from `users` where `users`.`id` = '84' limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 510}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.602716, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "LeadController.php:510", "source": "app/Http/Controllers/LeadController.php:510", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=510", "ajax": false, "filename": "LeadController.php", "line": "510"}, "connection": "omx_sass_systam_db", "start_percent": 74.015, "width_percent": 2.563}, {"sql": "select * from `users` where `users`.`id` = '84' limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 523}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.608127, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:523", "source": "app/Http/Controllers/LeadController.php:523", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=523", "ajax": false, "filename": "LeadController.php", "line": "523"}, "connection": "omx_sass_systam_db", "start_percent": 76.578, "width_percent": 1.898}, {"sql": "select * from `email_templates` where `name` LIKE 'lead_assigned' limit 1", "type": "query", "params": [], "bindings": ["lead_assigned"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 2418}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 531}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6129801, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:2418", "source": "app/Models/Utility.php:2418", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=2418", "ajax": false, "filename": "Utility.php", "line": "2418"}, "connection": "omx_sass_systam_db", "start_percent": 78.477, "width_percent": 1.566}, {"sql": "select * from `user_email_templates` where `template_id` = 4 and `user_id` = 84 limit 1", "type": "query", "params": [], "bindings": ["4", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 2422}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 531}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.6182232, "duration": 0.00466, "duration_str": "4.66ms", "memory": 0, "memory_str": null, "filename": "Utility.php:2422", "source": "app/Models/Utility.php:2422", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=2422", "ajax": false, "filename": "Utility.php", "line": "2422"}, "connection": "omx_sass_systam_db", "start_percent": 80.043, "width_percent": 11.058}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` = 118 and `lead_stages`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["118"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 358}, {"index": 22, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 23, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 556}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9284818, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "CrmWebhookDispatcher.php:358", "source": "app/Services/CrmWebhookDispatcher.php:358", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FCrmWebhookDispatcher.php&line=358", "ajax": false, "filename": "CrmWebhookDispatcher.php", "line": "358"}, "connection": "omx_sass_systam_db", "start_percent": 91.101, "width_percent": 1.424}, {"sql": "select * from `pipelines` where `pipelines`.`id` = 30 and `pipelines`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 365}, {"index": 22, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 23, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 556}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9329739, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "CrmWebhookDispatcher.php:365", "source": "app/Services/CrmWebhookDispatcher.php:365", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FCrmWebhookDispatcher.php&line=365", "ajax": false, "filename": "CrmWebhookDispatcher.php", "line": "365"}, "connection": "omx_sass_systam_db", "start_percent": 92.525, "width_percent": 1.353}, {"sql": "select * from `users` where `users`.`id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 411}, {"index": 21, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 59}, {"index": 22, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 556}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9398851, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "CrmWebhookDispatcher.php:411", "source": "app/Services/CrmWebhookDispatcher.php:411", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FCrmWebhookDispatcher.php&line=411", "ajax": false, "filename": "CrmWebhookDispatcher.php", "line": "411"}, "connection": "omx_sass_systam_db", "start_percent": 93.878, "width_percent": 2.302}, {"sql": "select * from `module_integrations` where `enabled` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/ModuleWebhookService.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\ModuleWebhookService.php", "line": 61}, {"index": 16, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 69}, {"index": 17, "namespace": null, "name": "app/Services/CrmWebhookDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Services\\CrmWebhookDispatcher.php", "line": 95}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 556}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.985482, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ModuleWebhookService.php:61", "source": "app/Services/ModuleWebhookService.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FServices%2FModuleWebhookService.php&line=61", "ajax": false, "filename": "ModuleWebhookService.php", "line": "61"}, "connection": "omx_sass_systam_db", "start_percent": 96.179, "width_percent": 1.78}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.982448, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "omx_sass_systam_db", "start_percent": 97.959, "width_percent": 2.041}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 1594, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 1176, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\EmailTemplate": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FEmailTemplate.php&line=1", "ajax": false, "filename": "EmailTemplate.php", "line": "?"}}, "App\\Models\\ModuleIntegration": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FModuleIntegration.php&line=1", "ajax": false, "filename": "ModuleIntegration.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}}, "count": 2781, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create lead, result => true, user => 84, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1651289276 data-indent-pad=\"  \"><span class=sf-dump-note>create lead</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create lead</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>84</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1651289276\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.14936, "xdebug_link": null}]}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/leads", "status_code": "<pre class=sf-dump id=sf-dump-657637713 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-657637713\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1888583878 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1888583878\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-576038441 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\"><PERSON><PERSON> Sarkar</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"20 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"10 characters\">8965478956</span>\"\n  \"<span class=sf-dump-key>date_of_birth</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2004-05-29</span>\"\n  \"<span class=sf-dump-key>contact_type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Lead</span>\"\n  \"<span class=sf-dump-key>tags</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>postal_code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">734429</span>\"\n  \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Siliguri</span>\"\n  \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"11 characters\">West Bengal</span>\"\n  \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"5 characters\">India</span>\"\n  \"<span class=sf-dump-key>business_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Smart Internz</span>\"\n  \"<span class=sf-dump-key>business_gst</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Smart Internz</span>\"\n  \"<span class=sf-dump-key>business_state</span>\" => \"<span class=sf-dump-str title=\"11 characters\">West Bengal</span>\"\n  \"<span class=sf-dump-key>business_postal_code</span>\" => \"<span class=sf-dump-str title=\"8 characters\">85888500</span>\"\n  \"<span class=sf-dump-key>business_address</span>\" => \"\"\"\n    <span class=sf-dump-str title=\"74 characters\">GANDAGOL JOTE, PANITANKI,DULAL JOTE,KHARIBARI,GARJEELING,734429<span class=\"sf-dump-default sf-dump-ns\">\\r\\n</span></span>\n    <span class=sf-dump-str title=\"74 characters\">naxalbari</span>\n    \"\"\"\n  \"<span class=sf-dump-key>user_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">84</span>\"\n  \"<span class=sf-dump-key>subject</span>\" => \"<span class=sf-dump-str title=\"11 characters\">New Contact</span>\"\n  \"<span class=sf-dump-key>pipeline_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>stage_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>dnd_settings</span>\" => \"<span class=sf-dump-str title=\"71 characters\">{&quot;all&quot;:false,&quot;emails&quot;:false,&quot;whatsapp&quot;:false,&quot;sms&quot;:false,&quot;calls&quot;:false}</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-576038441\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1611648635 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2396</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryg6rOwIGH4aqcBNsw</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6InNuTkhZcVhVRG9SMXpRbFVseWErYUE9PSIsInZhbHVlIjoiS0hsV1ZtTEdXZTUrRVMyM2hBY0kyRzFkNDJoV2hyY3dkVDEwZVZCbmdRRkhZTmdwTVpIS2VaTHorNElzdVB4aFFocTdyQzQ4QzVrSUVUdXNqMXJVSnc2OUpKRDE1T1lEWmlYcmdaZ0crM2NHQmVHSTNiUDFSY016RDNCZDQ2T2NkaFkyWHFFVGlkb1FzTFNwTEhGUXJkcC9ZbVlYaUNQNk43Z3FsQUxUSEJSa3NHejJNMnJqUDltSkhUcUdDbUFvcHV5YnJlUWo3L0sycml2OW5YbFFVcHhVaXpBUW1WNHllTzk5V0RGQkU5RGJTZWVUQnpma3pVaHBySHcwMzQ1UkxvWHVVYy82UTZoOUY1VSt0dVdzTTRWd0E1eHZRY0p0b0JGM25NVEUrdUU0a3dVQ0d4bHloN1VlUjBMcHQ0MlZlK1Z1c1BCYjl3VkhGaW1YUkJ5aFpJTHh1K3R2UkZYVFVaeE9GSkJVdlZHSmVGd25HdWZ1RkYzSFIyU1ZMTVA0K1JXcVhBVG0vRGtFY2gvSzRuRllEMDZHMlJ5dXZzc2pIbG9tV1RIcDFsZGFRWVFHY1YxQk0vY2NTd0xid1UvblJYWFRsOFRuM0tXOGNkbElIVVduaHIyTVB0a2xGYUFlT3N5bzdlN1FCMWovMXArdTk4TnM1VjVTOXRpTTN1bUsiLCJtYWMiOiIwYWI2NzhmZWE1MmE4YmIzNjE5MjI2ZWE3Mzg2YjU0NTVjMmU2NWRlYTUwMzYzNDNhYTc1MjRjNTRiYjJjOGExIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkZIdGw0UXdpT1JVTjNKZFJ2TmxPbVE9PSIsInZhbHVlIjoiQjhpNXcyV0pqQVRzbEFvL3NjUFpuRVRhcmszUFNhdXlKemV6aGM1c2ZrUW1TR0dvQ2s4bHFJSVE2cldPTEYvRUJFTW8rZmdYWTRGanJMTENuajdxbWxaNW9uSkhyVUhGcVdQYVlYZ0hLaldFdllhUjAvTEI0WE5xdlhQdFpYTXp2Yk1yVzN4bDZ6Ny9EbDYvT2M5TUJ3Nzd6WXFFQ2EvRTBBeC9KZXpGaDVKTWZiSGtxcllybmtaS1g5amU2eHdGOTd6V2EyUHRicE5PdlJqM2EybDlWK285b213WEFJV3RoNzFGdjFqNSs1WGY2WWZrOGwrYVp2REFQWThHZi9QcEpXRlNhMDJzUG0rbnFxVmc1bUZsY0c0OC82ZTRacG9sSDkzMHF1Y3RTL1k4SmtTZTBDNjJQRHB1Mm1YVFlSQmtucHh2Zms0eVk2eDNOaFFlVVBpbVEvenhrc0FjMGxKTmxCaVEvNi9OL3ZlV3lhdmNzOENxdDJBTTlVWCt2Zzhjd2pVTTRuV2M4VUJqb2hyQnUzRG94NWNqUm8vNXZaZCtEUDBlN0F0VHdIeno2RzYwaHNJOUUwZGVmVW84Z005ZmYxOSsrM0x6R3Z1dUdQbEpVbW1DalBFQStiWFE4TkxjRE1WSG13cGlWSXdWVGdwVVhZTExVRWFOVzIzMytScDciLCJtYWMiOiJhNjdjMTNhNjRkNWZkZTAwMjQ5MDJkNmQ0YjA4MDRmMDFhYWI5OGMyZGFkM2UxNGY2Mjg4YzRkNWQzMTk2Yzg2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1611648635\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-36419806 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-36419806\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1928145387 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:02:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFtMms5Y3BaaHdJaFpBTmlBNUlGWXc9PSIsInZhbHVlIjoiUzBQMXFaKzNlaHRrWWUxMUZUZWY4MGFCblJyVDNsYmtpRVllODExcXJpOHZpSXltWkZsK2NOTFkrUk9rTS9YL1puMHpnM3JjajNBbjFEVHBjNyt6MGlDS0tSYjdwNlIweGlBaWFvWXJpWHU1T3JYS3EwZS9kSUJmbEFvZU9FbVdiRmt3V0lrbUpJZlRiOStnSjFjWnh4VjBNc252eUxKWTVMS2FNUGtqTkh0TW5UNHNqcEN2aHJRdVYvMUJGa0xMOFZ5SmV1cytqc0pIVnE5eFdnRHowd1dWb3Y1WUZ1M2h5UGpJYmcybktRakh4T2R4YTRXR1Z1MG5TMTVDRS95VlJJdGJYSTNNTGllcUtjM2l5Umt6c1BFcitOcEZwOUZoVDZoSlR1TUw4RXVKV0hKTTVpTy9FM2RVS3dPWXVTSEp0SzB1ZWxjcS9zU0NUTXZHSm5Jb0VxR2U3ZXJaNW5yK0RHdTBzVGttT3RDbTlGKzNHYUJ1NTF2d09qSEdHaXV2UlZmbkNnZmZSTDhCSmlEdHA4c29oQys3aUZJMUJnSThZNGJtejRUMEJWblluZ2ZHRTI5c25jUTZ6REhkdHd3M3J3a3g1T1p2cmhDelZWUDFrdzZ0YkhySFJ0dUM1UzE3R3B2WVVFSkV2ZDd4azdRRC90UTR3N1ZtM0MxMVZwM1ciLCJtYWMiOiI4YzFjYzhmNThjNDYzNWVlNGEwMzUxZWIyNTNhZjUzYzgyY2IyYjQ2ZWU4ODAxNWUyODZhMWMwMzU3MTljYzcyIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:02:45 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjloQXlZREEvdzBKK0JMY05sbnZ1Q2c9PSIsInZhbHVlIjoiOVZURXRzTzlBaURFeUJSWHF2aUZYMW82c2RuMEUwQmlmcyszSjFnbjN2dXBXSFdtcFRVRHB3QkR4V2x1SzhPT3ltOXdEUGxFMDV1ODA1b1BvSnRNKzhLOHRLQWowcmlPbXpDeDZEYk0xNGcwSUZDL1F2YTJnazl2TzBmaFZhRnhWM09kUXpPUzB2MTNZaWppUUZ6SWFaWml5YXoxb1FjaEYya2RvaUhDUTMzZ1A4bGZ1QTZ3RVRwbTg3N0VJRi9najlhTTVJd0JDRENlUUd0WlljbWVOWW5LUUpKYjNGWk5ZY2RzcHpBeUhPMGU0ODU5N2NVN0NwNUVBUFpoSTNBV0xsN29aZE1hVFJ2STNHeWMrNy9VSDZEL2VURTFUbWxOdzFYWDFJY05sdFAxZmhzQ01LdFQwR0o1MERZa055TnFhbnllNUFFcUxJa255T1NvUVdpNld1UWczYWZRK3gvVkNMYk0rakg2bTBzdGFuZHV6MTdtK3d5TmlPNS9FTEFVaWM0aXZuTlNJWjB5Y3lOVXQ5R0JrbC9LK0xKaFhCNXZCaFV2Y2UvOWkwYWo4b1AzekUvUTk4NCtrdkYwUnFROGMyVnZlTkFJaTczNHN0TVd4dzFpTElsOWpMYS82QjM0ZXJUNmdjMjViK0JHV1FieHB2Sm1FV0xNeGpiM3NaY1EiLCJtYWMiOiIwMThhZWNmMmE4NWNmNmQ3YTE4NjVlOTI1YzZmZjk0MGQ4ZmVjNDlmNDY5NDlmY2E4NmQ5MTdjNzhmNWQ5YzllIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:02:45 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFtMms5Y3BaaHdJaFpBTmlBNUlGWXc9PSIsInZhbHVlIjoiUzBQMXFaKzNlaHRrWWUxMUZUZWY4MGFCblJyVDNsYmtpRVllODExcXJpOHZpSXltWkZsK2NOTFkrUk9rTS9YL1puMHpnM3JjajNBbjFEVHBjNyt6MGlDS0tSYjdwNlIweGlBaWFvWXJpWHU1T3JYS3EwZS9kSUJmbEFvZU9FbVdiRmt3V0lrbUpJZlRiOStnSjFjWnh4VjBNc252eUxKWTVMS2FNUGtqTkh0TW5UNHNqcEN2aHJRdVYvMUJGa0xMOFZ5SmV1cytqc0pIVnE5eFdnRHowd1dWb3Y1WUZ1M2h5UGpJYmcybktRakh4T2R4YTRXR1Z1MG5TMTVDRS95VlJJdGJYSTNNTGllcUtjM2l5Umt6c1BFcitOcEZwOUZoVDZoSlR1TUw4RXVKV0hKTTVpTy9FM2RVS3dPWXVTSEp0SzB1ZWxjcS9zU0NUTXZHSm5Jb0VxR2U3ZXJaNW5yK0RHdTBzVGttT3RDbTlGKzNHYUJ1NTF2d09qSEdHaXV2UlZmbkNnZmZSTDhCSmlEdHA4c29oQys3aUZJMUJnSThZNGJtejRUMEJWblluZ2ZHRTI5c25jUTZ6REhkdHd3M3J3a3g1T1p2cmhDelZWUDFrdzZ0YkhySFJ0dUM1UzE3R3B2WVVFSkV2ZDd4azdRRC90UTR3N1ZtM0MxMVZwM1ciLCJtYWMiOiI4YzFjYzhmNThjNDYzNWVlNGEwMzUxZWIyNTNhZjUzYzgyY2IyYjQ2ZWU4ODAxNWUyODZhMWMwMzU3MTljYzcyIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:02:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjloQXlZREEvdzBKK0JMY05sbnZ1Q2c9PSIsInZhbHVlIjoiOVZURXRzTzlBaURFeUJSWHF2aUZYMW82c2RuMEUwQmlmcyszSjFnbjN2dXBXSFdtcFRVRHB3QkR4V2x1SzhPT3ltOXdEUGxFMDV1ODA1b1BvSnRNKzhLOHRLQWowcmlPbXpDeDZEYk0xNGcwSUZDL1F2YTJnazl2TzBmaFZhRnhWM09kUXpPUzB2MTNZaWppUUZ6SWFaWml5YXoxb1FjaEYya2RvaUhDUTMzZ1A4bGZ1QTZ3RVRwbTg3N0VJRi9najlhTTVJd0JDRENlUUd0WlljbWVOWW5LUUpKYjNGWk5ZY2RzcHpBeUhPMGU0ODU5N2NVN0NwNUVBUFpoSTNBV0xsN29aZE1hVFJ2STNHeWMrNy9VSDZEL2VURTFUbWxOdzFYWDFJY05sdFAxZmhzQ01LdFQwR0o1MERZa055TnFhbnllNUFFcUxJa255T1NvUVdpNld1UWczYWZRK3gvVkNMYk0rakg2bTBzdGFuZHV6MTdtK3d5TmlPNS9FTEFVaWM0aXZuTlNJWjB5Y3lOVXQ5R0JrbC9LK0xKaFhCNXZCaFV2Y2UvOWkwYWo4b1AzekUvUTk4NCtrdkYwUnFROGMyVnZlTkFJaTczNHN0TVd4dzFpTElsOWpMYS82QjM0ZXJUNmdjMjViK0JHV1FieHB2Sm1FV0xNeGpiM3NaY1EiLCJtYWMiOiIwMThhZWNmMmE4NWNmNmQ3YTE4NjVlOTI1YzZmZjk0MGQ4ZmVjNDlmNDY5NDlmY2E4NmQ5MTdjNzhmNWQ5YzllIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:02:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1928145387\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1052093435 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1052093435\", {\"maxDepth\":0})</script>\n"}}