{"__meta": {"id": "Xd81335d1645d6a6eaf3016b6c06d279a", "datetime": "2025-07-29 05:42:38", "utime": **********.489459, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753767757.348331, "end": **********.489495, "duration": 1.1411640644073486, "duration_str": "1.14s", "measures": [{"label": "Booting", "start": 1753767757.348331, "relative_start": 0, "end": **********.39217, "relative_end": **********.39217, "duration": 1.0438389778137207, "duration_str": "1.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.392198, "relative_start": 1.****************, "end": **********.489499, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "97.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "0CP29LNexIR4mxmaSATYnFD6o9I7urnvLKSE7mMp", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1183246873 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1183246873\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1274282371 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1274282371\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2146399532 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2146399532\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1442903846 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1442903846\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1166138224 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1166138224\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-828090354 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:42:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1zWFFvUm9BZEF5QTFnc3hXQXk2WEE9PSIsInZhbHVlIjoiWXhiMGMvNkQvMUxhajQxVmtKeGV6dnV4NGZNQTgrU1RtaEpMRzlxWFZSTlp1RlJUbGZ6Mk1ZS1I1Q2lvVEcyakNrSk9TUkV4bEx5NGt3dEE4bVBqZUYySWFVbThUbUVjUTV5bG9FcGdvdHZmZkJQSlZWcThHaGhVWlV5eURiRG9jNWdwcEJUUmFCdW9vWUlLSmtNdEVIdFpVK0pCWXcwa21HR3Roejg5YXoza1BJNHY3TnNhM0N0ZVhjbHNZRTBLazNvRmVNUjJNVHZaQXhFQzc0QjdBN1VOZ0p3eTFmemNLUW1USVcyOTFvbms5VjYvdlVSaFVETnBFNEMxQlZzR0xHTGhrMHFGU0YvL3ZuVlE1cFpLVHdSUTViZVJrZXpvZnFRZWVLZHNhVTVSOXpKVy9nU1BJbjk5eWhtUTlPd0NYZlJpUFBuQXA2cFVKVjZldXZ1RDJJb0hSSGt0bnZkQ0NITEFkL1IyNWFMRndJWk53SXRISUJHV1orMm5aNkhtRHFZbEZmRXRBVHVLclF6dWV0bktzZHorSWNwbDJZVkh4ZmtDZ2k2VnFhdGFaOWFTb3A4anVKWFdPalIyd1I3ZFF1bjgwaWZsSkFFdUgxOEVnaTNGT0xZeUZIaThJT2tSdDF5b2FnaDl6MUlIRlgzWmJ5RkNBdzVSVk5MbUx3aWUiLCJtYWMiOiJiOTI5YzIwNGM2ZjIwNzY0OWI4ZDVjNTU0YTQzNDk3NzQ3NzQxMjZjOGJiNzllOGVjNDI4ZjUzYWRhMWU0NDE2IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:42:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InpvYmtzdXB3SzZzWWVZa0cwL0FPVXc9PSIsInZhbHVlIjoiOGkxUXEvem02ajJoQkFQc0QxcExaZ2owUkhSVUNheklCalNINHVoVkwwNWxCb0k2WjgxUlNDc0RWaHZwd3E2aHM3VmRoMy9oMWpUM0VuWXZaKzAxeVVCbFBwd3g2TjJaa0hnL3JKdzFsV2NZZEg5bGFDcFhUWVE2bnIzRVVJMUJwTG9neTBhL2lqcHBPV3YwWWNUMVpMMThGRXlYcTJtOWVDY29PUlRtdXZiMkFmM3pQek4zc21walRnbTlTczhCekd0b3kyRU9FSGNEL3RuaTgxeHpPTWgyNVcvRU1pMzY0TThsUldyVlltU0F1UG9JcElKOGxOZk5jWHd5RUxOMnQ5Tzd0R1lpVjVWN21LSlQ5ZzhHV0hxZThpeWpmeU14Z0lzOTFCUlFweEtBeTdWeXlkRk0xUk9FWENrdU5OS1krNXBMakhJQlo5NmFmaVVwQjE4Qmo1VEhPeldoV2ZKdkpwc1AwMkJCeDJ2cmNEOG5rRDFXTWpJMDljRGZSOGNQSittbE5ZZE1sZ1VkOHRiM3Ztcmlud3JvN2lCY2NWUXNaZTVTRTA3M2xZMHVwRUFvUGV6ekFrWTBzdkVGRmRrN3RsSzR4RFV4MmRwZTJlODlaOXl1bE4vK08zc1VaZXJQSyt1VlZLV2s3QVRMaEhCLzRnZjRpNFROckx2Y3o4L3oiLCJtYWMiOiJlNzhhNDhhNDZmMGY3ZDVkYTZjMDAxMWIyN2VmZDZiZjhjY2Y3ZDg1YTBmZjc3NTBmMTgzM2VjY2YwNDYzOWRjIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:42:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1zWFFvUm9BZEF5QTFnc3hXQXk2WEE9PSIsInZhbHVlIjoiWXhiMGMvNkQvMUxhajQxVmtKeGV6dnV4NGZNQTgrU1RtaEpMRzlxWFZSTlp1RlJUbGZ6Mk1ZS1I1Q2lvVEcyakNrSk9TUkV4bEx5NGt3dEE4bVBqZUYySWFVbThUbUVjUTV5bG9FcGdvdHZmZkJQSlZWcThHaGhVWlV5eURiRG9jNWdwcEJUUmFCdW9vWUlLSmtNdEVIdFpVK0pCWXcwa21HR3Roejg5YXoza1BJNHY3TnNhM0N0ZVhjbHNZRTBLazNvRmVNUjJNVHZaQXhFQzc0QjdBN1VOZ0p3eTFmemNLUW1USVcyOTFvbms5VjYvdlVSaFVETnBFNEMxQlZzR0xHTGhrMHFGU0YvL3ZuVlE1cFpLVHdSUTViZVJrZXpvZnFRZWVLZHNhVTVSOXpKVy9nU1BJbjk5eWhtUTlPd0NYZlJpUFBuQXA2cFVKVjZldXZ1RDJJb0hSSGt0bnZkQ0NITEFkL1IyNWFMRndJWk53SXRISUJHV1orMm5aNkhtRHFZbEZmRXRBVHVLclF6dWV0bktzZHorSWNwbDJZVkh4ZmtDZ2k2VnFhdGFaOWFTb3A4anVKWFdPalIyd1I3ZFF1bjgwaWZsSkFFdUgxOEVnaTNGT0xZeUZIaThJT2tSdDF5b2FnaDl6MUlIRlgzWmJ5RkNBdzVSVk5MbUx3aWUiLCJtYWMiOiJiOTI5YzIwNGM2ZjIwNzY0OWI4ZDVjNTU0YTQzNDk3NzQ3NzQxMjZjOGJiNzllOGVjNDI4ZjUzYWRhMWU0NDE2IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:42:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InpvYmtzdXB3SzZzWWVZa0cwL0FPVXc9PSIsInZhbHVlIjoiOGkxUXEvem02ajJoQkFQc0QxcExaZ2owUkhSVUNheklCalNINHVoVkwwNWxCb0k2WjgxUlNDc0RWaHZwd3E2aHM3VmRoMy9oMWpUM0VuWXZaKzAxeVVCbFBwd3g2TjJaa0hnL3JKdzFsV2NZZEg5bGFDcFhUWVE2bnIzRVVJMUJwTG9neTBhL2lqcHBPV3YwWWNUMVpMMThGRXlYcTJtOWVDY29PUlRtdXZiMkFmM3pQek4zc21walRnbTlTczhCekd0b3kyRU9FSGNEL3RuaTgxeHpPTWgyNVcvRU1pMzY0TThsUldyVlltU0F1UG9JcElKOGxOZk5jWHd5RUxOMnQ5Tzd0R1lpVjVWN21LSlQ5ZzhHV0hxZThpeWpmeU14Z0lzOTFCUlFweEtBeTdWeXlkRk0xUk9FWENrdU5OS1krNXBMakhJQlo5NmFmaVVwQjE4Qmo1VEhPeldoV2ZKdkpwc1AwMkJCeDJ2cmNEOG5rRDFXTWpJMDljRGZSOGNQSittbE5ZZE1sZ1VkOHRiM3Ztcmlud3JvN2lCY2NWUXNaZTVTRTA3M2xZMHVwRUFvUGV6ekFrWTBzdkVGRmRrN3RsSzR4RFV4MmRwZTJlODlaOXl1bE4vK08zc1VaZXJQSyt1VlZLV2s3QVRMaEhCLzRnZjRpNFROckx2Y3o4L3oiLCJtYWMiOiJlNzhhNDhhNDZmMGY3ZDVkYTZjMDAxMWIyN2VmZDZiZjhjY2Y3ZDg1YTBmZjc3NTBmMTgzM2VjY2YwNDYzOWRjIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:42:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-828090354\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-544605747 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0CP29LNexIR4mxmaSATYnFD6o9I7urnvLKSE7mMp</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-544605747\", {\"maxDepth\":0})</script>\n"}}