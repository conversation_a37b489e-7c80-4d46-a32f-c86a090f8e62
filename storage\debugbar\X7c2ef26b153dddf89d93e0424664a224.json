{"__meta": {"id": "X7c2ef26b153dddf89d93e0424664a224", "datetime": "2025-07-29 05:33:29", "utime": **********.379591, "method": "PUT", "uri": "/api/contacts/14/pipeline", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[05:33:29] LOG.info: ContactController: Updating contact pipeline {\n    \"contact_id\": \"14\",\n    \"request_data\": {\n        \"_token\": \"pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U\",\n        \"pipeline_id\": \"28\",\n        \"stage_id\": \"109\",\n        \"notes\": null\n    },\n    \"user_id\": 84\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.322976, "xdebug_link": null, "collector": "log"}, {"message": "[05:33:29] LOG.info: Contact pipeline updated successfully {\n    \"contact_id\": \"14\",\n    \"contact_name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"contact_type\": \"Lead\",\n    \"pipeline_name\": \"AI\",\n    \"stage_name\": \"Send One\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.369598, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753767208.445051, "end": **********.379622, "duration": 0.9345710277557373, "duration_str": "935ms", "measures": [{"label": "Booting", "start": 1753767208.445051, "relative_start": 0, "end": **********.224132, "relative_end": **********.224132, "duration": 0.7790811061859131, "duration_str": "779ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.22415, "relative_start": 0.7790989875793457, "end": **********.379625, "relative_end": 3.0994415283203125e-06, "duration": 0.15547513961791992, "duration_str": "155ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46379304, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT api/contacts/{id}/pipeline", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@updateContactPipeline", "namespace": null, "prefix": "/api", "where": [], "as": "api.update-contact-pipeline", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=203\" onclick=\"\">app/Http/Controllers/ContactController.php:203-287</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.02857, "accumulated_duration_str": "28.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2871418, "duration": 0.01551, "duration_str": "15.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 54.288}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.318474, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 54.288, "width_percent": 2.8}, {"sql": "select count(*) as aggregate from `pipelines` where `id` = '28'", "type": "query", "params": [], "bindings": ["28"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.332611, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "omx_sass_systam_db", "start_percent": 57.088, "width_percent": 4.97}, {"sql": "select count(*) as aggregate from `lead_stages` where `id` = '109'", "type": "query", "params": [], "bindings": ["109"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 938}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 909}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 664}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}], "start": **********.337782, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "omx_sass_systam_db", "start_percent": 62.058, "width_percent": 1.89}, {"sql": "select * from `leads` where `id` = '14' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["14", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 229}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3423629, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:229", "source": "app/Http/Controllers/ContactController.php:229", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=229", "ajax": false, "filename": "ContactController.php", "line": "229"}, "connection": "omx_sass_systam_db", "start_percent": 63.948, "width_percent": 2.485}, {"sql": "update `leads` set `pipeline_id` = '28', `stage_id` = '109', `leads`.`updated_at` = '2025-07-29 05:33:29' where `id` = 14", "type": "query", "params": [], "bindings": ["28", "109", "2025-07-29 05:33:29", "14"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.346433, "duration": 0.007940000000000001, "duration_str": "7.94ms", "memory": 0, "memory_str": null, "filename": "ContactController.php:260", "source": "app/Http/Controllers/ContactController.php:260", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=260", "ajax": false, "filename": "ContactController.php", "line": "260"}, "connection": "omx_sass_systam_db", "start_percent": 66.433, "width_percent": 27.791}, {"sql": "select * from `pipelines` where `pipelines`.`id` = '28' limit 1", "type": "query", "params": [], "bindings": ["28"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 263}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.358806, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:263", "source": "app/Http/Controllers/ContactController.php:263", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=263", "ajax": false, "filename": "ContactController.php", "line": "263"}, "connection": "omx_sass_systam_db", "start_percent": 94.225, "width_percent": 2.555}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` = '109' limit 1", "type": "query", "params": [], "bindings": ["109"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 264}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3624039, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:264", "source": "app/Http/Controllers/ContactController.php:264", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=264", "ajax": false, "filename": "ContactController.php", "line": "264"}, "connection": "omx_sass_systam_db", "start_percent": 96.78, "width_percent": 3.22}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/api/contacts/14/pipeline", "status_code": "<pre class=sf-dump id=sf-dump-514812851 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-514812851\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-675224962 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-675224962\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-682370807 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">28</span>\"\n  \"<span class=sf-dump-key>stage_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">109</span>\"\n  \"<span class=sf-dump-key>notes</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-682370807\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1375928753 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">82</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImZRaVljYzZYNEN1eUsrcWRwS08zOEE9PSIsInZhbHVlIjoiU3JXRkRETkI4dXB1V2ErQnFMSWRLemR6YU1CSThCc0J3QzJ0NlRwNW1YRnBNTTkzN1pueVoyZ2Q1b09KM2VhUmNKeTdadUlsZUd5ZnlCWkxXZkdlc2dNSFQxSmJIODdhSElqT0hIb3RxdFoyM0VianU0a01waExsQjBrTnJhbnp2U1lOYUJYOS8vT2prenBtMzlyTjFMUnZUSHdaMUp0cS85VDYyOE1UdzNJWWt2bThPRUpudzZ4NkRTMDQyVlh0K0NtSUlmaVEyZkdiMThGejBNOCtMT0xDWmhYUDYvMnAwOUM2MDdLT0w3VjY5SmFGeFRDU0F6QlNvR0Z5NlRRNStGWmdiZ2lDaXRGSDloeHpiRzY0ZU5rTG9xNE11cWJHQWF4T3p4aUY2b24wUFI1ODYwT1pRWWJvdUdBeis0QzlTVEhZV016c05DczVzaEV2bk9sRTQ1eU44R1oxWS9INmFoUzN5bHcwV1gwMkVzaEgwVXJiVnR1WmkvTERBN3pyMlZoSXdjcFg0bk1xUEl4ajhOMHVEcEcwSmwybERsQ00yNlpFQTZMcW9peGErNE1BWHk3RDF0N0xLWU1id1drVjR2ZmJISHd3T3E4NnU1eFAvbmxZT2c2YmlkcG9sdGFQVVRwVmJnMFR6RjByV1JlLzFWSW5lZGlTcStpUVM3VnIiLCJtYWMiOiI4NTdlOWM2NGIyMzBiM2ZkMTQyYWU5NzY1MjA3MzExMjczMzZiMzM1OTU3YzJhMzZmYmFhNmEzYjQ2ZDhiMGUzIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InA1ci9NcSttTi9GYmUrdHR2Tk5mRnc9PSIsInZhbHVlIjoiSjMybFlUTzlEOVdXVkFOUFhxOTlLNkh0REk0YlZFTjRUTVVHK2FWYjBZVHJ6Smx4TkhNcUlNNGtMSFV3TTNrUXhKTjJHcC9hY2tWdk1ITWJVL2xxZklabzIvc2ZIR3B3endlU1pDaDlIaGhYelAwcjV4NDhZbjJERmYyYTc3aTJQQ3c5cVNTUFR2dVhXOHlVdGkzeXVBd0Vhays5bVNXVXBNUHgrNzBna1c5NFhaSExHbSswaDBueWQ5QnFsa0hYK0F3TXRuK3pMVXA4SWJFUUdHYjlJUHFmUDhwTzgwOVVUUm9JSzFhZ0pQNlFXVHlmbXJaTXhKbXphYXdQSWZxSFpXaFY2WnlhUEtTL3ByNVN5MzJ3YmxqNjVaSGhMUFZnVkd0blY3OS9oTHN6NGYzNGJ3SlFZT0FjK0NwVGZtZHVsT1hRekJuUW5kVjlkWUh1K2tVNkEzVzlZQlRFanVWeS96Zkc3ZGtaanNvNnBYbHRBK3NVcHhVNGNjbGgyMndoK0pqKzBIaVVPSTBUcS9rdS9iM0pOd3JtNTIvL3dVejhnQTlJMU96QTlUQWo5Um92M1JSdXJ5eEpUVGpYVjIzTkkwOUZRekFmMXVoYk0xV1Q0RDdRaDVqenJESlhwWHF2eStoRzZkbWdKMHNkLzErS1U0aGhBdmp0dHNueDBIQmQiLCJtYWMiOiIyN2M0MDg2Y2RlOWIwNmFiMjhlNTkwZTQwNDEwMjM0ZGJiODAwNTNjNjE0Zjg4ZTBmMjFjZjdjMzJhMGVkOGE0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1375928753\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1954768286 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1954768286\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:33:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZ1RGZrcG05d0pySnBqUVRKUCtHdWc9PSIsInZhbHVlIjoia1NXL1JpTFJHOTJmSFNXZ2Z6bk9HUkluSlJleCs4U3V1cEpkVm82TDVRV1RVTTdlVTBEWWpwanhxQ3A3dnlkM0hXZ0doUzMweDFtQjU1NFY4M1MySWs0eWFuVHI0STdYQkhhLzd5b2xLTFgyV09EV21UN2gwYmVFSUlqd1A5VTl1QXVwZTBsT1RrQTNLL1dPUGdDWUd3cE5URWwzT2REZHhIbXNraDUyS3o5ZEkrQS9BSFA5VTBHRDBOMEZCeEhmUks1VzArSEg4WFNqMW9IRE5FWkpxVDRQbmxld1VaU1U0MlEwRGM0KzJrOTlpQ09nd01IVnlCRTQxb1lWQm1hem1DZnNVMHlUTVUwYW9MZG5tcjNEUzRTazgrbFFFMmt5eTA5aHBSdCtpYndVVkR5ZEdxZ2RIVm1nazdta216VjMxMzFReXdLOWZOOS9YVW5Zb1lNam9QOWhFYldDbk1CdFRoNTJ0MWNLbnBQbzZSSXRGdnErQWRyczN2VWdSb3BtTFpQQlVZYzJEb1pyYzdmU0ZqWU9QdVE4UTZwSW5yWjZnTWh3RS93S1IvZVkxdjlFcGpOcUprcytvUGFWb0k4R0FUbmtYU3YrL08zQTZIWndWa0t0TDJIM3dtR05OQ3FsMEl6RGhvVThDd3dXVFcyU3BCM2VUQ1ROVGRldFhjN2EiLCJtYWMiOiI3MTIwYjlkOTk0NDEyMmY1NjVmYWYyMDY2ODVmNTYwMmY4YjMxNjA0OWVlZjc5ZjliMjk3YjFlNzM0MTZlZDg5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:33:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkFnNXJpOUNiS3N6ZmU5WXk5eVBJaEE9PSIsInZhbHVlIjoiV2R2T1dDRlVvYnNKYWZITSsyenI1T1BiaUZQSFJzNEcycys3Ukp6b1FkMmFhUG1Ca0FUdnAzOVhwcTB6dXU3ZEdwbDNTNWtEN0tLMVk5WDhCYk9WRUlabEh6RloxY3oyTXVsbFo1YlVrbVdKQ2ViMDJhSml1SmxRWEpsdXBqTjdidVRJTXNENloyWUE4cmNtcTN6YUpVbmszcWRNV3JKZTBoRVA1QlZtaWMrQVY4Rmx5Mkt2UGNhS1Zja0kwbmt0WGdqK2h0TlAzWnM5WjVEdWcxaDUyK2hmSm5xeDlRd1NnUU5wcW12RXBSOW1UNUhxcUF5NGNoQ3JydmxWM1VIOGo1WUZTSjFGV1d2eXk4SlNMb3l5b1ZNcThXUHZzMHp0MUlIV2l3RDZvSnEvMkJyQnRqQU13OVl5RXp6eE8vMTRQdHlvRThvSXBkRERJcTVKbzIrVmZ1K1NPQSswNjNWeEZRZnRPc2ZmQTcyYmMwS0FEWk5kQ0NoeTlObXhlZlRnQXlKQ01IY3VVUkQ3S2JlRkcxWjhlbk9QclJQNFIrWmZ5ZDAxM1VpRFRHVnFaSDFieGhGSDgvNm00Q09pK1dTWmtxa3dKV3JjNklGbkxCVTlKMzF4a1hndWJNQWhLa1c2OWhsVDlaQjlEOXpua21QWFBFRktlSHZPeVIyaXlmcUUiLCJtYWMiOiI0NGEyMTI2MWMxNTg1MDBkZTdkZmNiMmU5OTVlYzFkYzMwZmRkZjBmZWZhMjY0MTYzZjU5ODMzYTk4M2NkODA2IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:33:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZ1RGZrcG05d0pySnBqUVRKUCtHdWc9PSIsInZhbHVlIjoia1NXL1JpTFJHOTJmSFNXZ2Z6bk9HUkluSlJleCs4U3V1cEpkVm82TDVRV1RVTTdlVTBEWWpwanhxQ3A3dnlkM0hXZ0doUzMweDFtQjU1NFY4M1MySWs0eWFuVHI0STdYQkhhLzd5b2xLTFgyV09EV21UN2gwYmVFSUlqd1A5VTl1QXVwZTBsT1RrQTNLL1dPUGdDWUd3cE5URWwzT2REZHhIbXNraDUyS3o5ZEkrQS9BSFA5VTBHRDBOMEZCeEhmUks1VzArSEg4WFNqMW9IRE5FWkpxVDRQbmxld1VaU1U0MlEwRGM0KzJrOTlpQ09nd01IVnlCRTQxb1lWQm1hem1DZnNVMHlUTVUwYW9MZG5tcjNEUzRTazgrbFFFMmt5eTA5aHBSdCtpYndVVkR5ZEdxZ2RIVm1nazdta216VjMxMzFReXdLOWZOOS9YVW5Zb1lNam9QOWhFYldDbk1CdFRoNTJ0MWNLbnBQbzZSSXRGdnErQWRyczN2VWdSb3BtTFpQQlVZYzJEb1pyYzdmU0ZqWU9QdVE4UTZwSW5yWjZnTWh3RS93S1IvZVkxdjlFcGpOcUprcytvUGFWb0k4R0FUbmtYU3YrL08zQTZIWndWa0t0TDJIM3dtR05OQ3FsMEl6RGhvVThDd3dXVFcyU3BCM2VUQ1ROVGRldFhjN2EiLCJtYWMiOiI3MTIwYjlkOTk0NDEyMmY1NjVmYWYyMDY2ODVmNTYwMmY4YjMxNjA0OWVlZjc5ZjliMjk3YjFlNzM0MTZlZDg5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:33:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkFnNXJpOUNiS3N6ZmU5WXk5eVBJaEE9PSIsInZhbHVlIjoiV2R2T1dDRlVvYnNKYWZITSsyenI1T1BiaUZQSFJzNEcycys3Ukp6b1FkMmFhUG1Ca0FUdnAzOVhwcTB6dXU3ZEdwbDNTNWtEN0tLMVk5WDhCYk9WRUlabEh6RloxY3oyTXVsbFo1YlVrbVdKQ2ViMDJhSml1SmxRWEpsdXBqTjdidVRJTXNENloyWUE4cmNtcTN6YUpVbmszcWRNV3JKZTBoRVA1QlZtaWMrQVY4Rmx5Mkt2UGNhS1Zja0kwbmt0WGdqK2h0TlAzWnM5WjVEdWcxaDUyK2hmSm5xeDlRd1NnUU5wcW12RXBSOW1UNUhxcUF5NGNoQ3JydmxWM1VIOGo1WUZTSjFGV1d2eXk4SlNMb3l5b1ZNcThXUHZzMHp0MUlIV2l3RDZvSnEvMkJyQnRqQU13OVl5RXp6eE8vMTRQdHlvRThvSXBkRERJcTVKbzIrVmZ1K1NPQSswNjNWeEZRZnRPc2ZmQTcyYmMwS0FEWk5kQ0NoeTlObXhlZlRnQXlKQ01IY3VVUkQ3S2JlRkcxWjhlbk9QclJQNFIrWmZ5ZDAxM1VpRFRHVnFaSDFieGhGSDgvNm00Q09pK1dTWmtxa3dKV3JjNklGbkxCVTlKMzF4a1hndWJNQWhLa1c2OWhsVDlaQjlEOXpua21QWFBFRktlSHZPeVIyaXlmcUUiLCJtYWMiOiI0NGEyMTI2MWMxNTg1MDBkZTdkZmNiMmU5OTVlYzFkYzMwZmRkZjBmZWZhMjY0MTYzZjU5ODMzYTk4M2NkODA2IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:33:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-143462639 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-143462639\", {\"maxDepth\":0})</script>\n"}}