{"__meta": {"id": "Xd751879b665d968f827bca45f241651d", "datetime": "2025-07-29 04:52:46", "utime": **********.397445, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753764765.330127, "end": **********.397703, "duration": 1.0675759315490723, "duration_str": "1.07s", "measures": [{"label": "Booting", "start": 1753764765.330127, "relative_start": 0, "end": **********.302635, "relative_end": **********.302635, "duration": 0.9725079536437988, "duration_str": "973ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.302653, "relative_start": 0.****************, "end": **********.397728, "relative_end": 2.5033950805664062e-05, "duration": 0.*****************, "duration_str": "95.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2997\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1801 to 1807\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1801\" onclick=\"\">routes/web.php:1801-1807</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2deoCJdVF4jHJ7lOTZcwIx1q68gmB1URtqhTzQnQ", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1169371054 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1169371054\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1502911831 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1502911831\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2089008648 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2089008648\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-77075185 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-77075185\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-692188523 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-692188523\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2030921514 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 04:52:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJvMDZYVFdsVUQvNlBDc0M3R3hUK0E9PSIsInZhbHVlIjoiVzFuNWtQNHlQb3VlcWZBdlY2YzB3S20vekZWc09jR2I5QXVicE9pMkhkT3YrZ1RiQVkrYTB6cWRWM3JhaUhmbnZrUHhtK0VkUksrN2RseEE4WTlERktobGtzS2pvd3V0M2tQQTZMOUdLZm5taVFUcWc1NTFuN0EyVzRUdVN5ekFHUFQ1cm5pcWczeDQvQ2J1UDQvamdqQjl4K084YmpEY1NjQ2lqeXlQQXh1L3NSbllqdlQ1Y3lBQ1ZkKzVnS3pOeEZ1cWMwTFFWVzk4TlRBckdJeFY4QThYVmtWSnl2K2YrbnFiKzhZaHl6RzlML2xxMHVIUlY1V3V1LzQyUDBqeEFJaHpyM2NLUHI3bVJibW1GNGZuU2Q0T0pNdDVGUEx5Um56UjBrYWVxYWM3VjZlZ1RneFFFSytOTmpic05FL0tKVVRXbkxUVHZSem9qbDV4Ym5IVjFjL0JzL1MvYTltd1VudzdZSFNqSXkrVDhiTlBzMGpSUXpEcHNuM2tqUWtLUThXNkdaZ0hWWlJGaVBpajYzTm1sNmJmNzVTbWowcHFtQWRNVUphVXJGSm13SnJvdmw1dFBEU21UaHlIUnlSbE9EV3pSamhiNGZVUU1UcytzRHdieDE1SUpTVTM4Y09HOFpjZks3cXpiVnVnR0gwNWZKZTRvTFFRTGZSaWNqbWsiLCJtYWMiOiI2MDg5YjA5NDNhNTEyNzc1NmY0MThiZDU1MTNkMWM5MzkxYjUwNjA1YjRhMTg4YTNjZjQ3NjZlYTM0YTM4OTVmIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 06:52:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ikd6Mk5rWWN5OHh4cEMxbWNyeDVlRFE9PSIsInZhbHVlIjoiNldVQ0tnb0hEMGJ0K1EzOThqUG94cDNITE0wVmhRQnQ0bk5vZDVsYUVrVGgyZmU3UStOanUzamhVc1JGZ25VQW1seC9yTHVLS1g5RWtsb1dSN0t0SW1CakxYaUtiZDVZQ1NTV3M5TkZ1WERuMXFES3l2d3dFaGViRFp0MG96ejdWaVZUZU9jbW53TUkwSi9iZXcwbnk3MUZKbFlhTTNUYm8rSzZoQmNuc1p5eEozbmlwMmxqQk5KYWhlTUtmUUhxUm9OOXFQOGliWkFpOFQ5RVgvMkJsUXdZTy9mZmFZWFc5elZlVkRzUXdQdUdVRHZuVUR6bEdERFpubDdhSC81OHJ3a2pSTysxWEN5cWtDZjdob0NuUSs5cnNqN1VWWThkSFU1ZTlKZFJBWTN0RDJ6VUJ3Nm50aUVqdVdqM01Cb2JEMWlMRWtsSlMxenBQZ3Q0RXk1RkxFOHRWSjNENW5ScHh2aDVqd2lRZVNua0R5eWRwcmMzcnRDN1dTMEVPdTdsWVNRd2VQOWkzcFpmS0RyaHM1eHV0Nld1bm5VTGZzcTZJS0xhU3lEczRXV0Iza2R1cXZGaWRsc3VtNDl2Ti9UcjRXTE1wNm1icXkxL3QyVGR3MGwzQ0Z3VVRRUXZKUlluMUhYaVhyL2Z2NFNJa2dtUXFEMStiT0VkU1VsMHdHUFAiLCJtYWMiOiIwMjQ0NTZkMDhjMjIzM2ZhZTNhZmNmYmJiN2IyNmEyMjEzNDU3ODk4YjVlY2QxNzNkODQzZjk5NmI4MWQ3MmM5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 06:52:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJvMDZYVFdsVUQvNlBDc0M3R3hUK0E9PSIsInZhbHVlIjoiVzFuNWtQNHlQb3VlcWZBdlY2YzB3S20vekZWc09jR2I5QXVicE9pMkhkT3YrZ1RiQVkrYTB6cWRWM3JhaUhmbnZrUHhtK0VkUksrN2RseEE4WTlERktobGtzS2pvd3V0M2tQQTZMOUdLZm5taVFUcWc1NTFuN0EyVzRUdVN5ekFHUFQ1cm5pcWczeDQvQ2J1UDQvamdqQjl4K084YmpEY1NjQ2lqeXlQQXh1L3NSbllqdlQ1Y3lBQ1ZkKzVnS3pOeEZ1cWMwTFFWVzk4TlRBckdJeFY4QThYVmtWSnl2K2YrbnFiKzhZaHl6RzlML2xxMHVIUlY1V3V1LzQyUDBqeEFJaHpyM2NLUHI3bVJibW1GNGZuU2Q0T0pNdDVGUEx5Um56UjBrYWVxYWM3VjZlZ1RneFFFSytOTmpic05FL0tKVVRXbkxUVHZSem9qbDV4Ym5IVjFjL0JzL1MvYTltd1VudzdZSFNqSXkrVDhiTlBzMGpSUXpEcHNuM2tqUWtLUThXNkdaZ0hWWlJGaVBpajYzTm1sNmJmNzVTbWowcHFtQWRNVUphVXJGSm13SnJvdmw1dFBEU21UaHlIUnlSbE9EV3pSamhiNGZVUU1UcytzRHdieDE1SUpTVTM4Y09HOFpjZks3cXpiVnVnR0gwNWZKZTRvTFFRTGZSaWNqbWsiLCJtYWMiOiI2MDg5YjA5NDNhNTEyNzc1NmY0MThiZDU1MTNkMWM5MzkxYjUwNjA1YjRhMTg4YTNjZjQ3NjZlYTM0YTM4OTVmIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 06:52:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ikd6Mk5rWWN5OHh4cEMxbWNyeDVlRFE9PSIsInZhbHVlIjoiNldVQ0tnb0hEMGJ0K1EzOThqUG94cDNITE0wVmhRQnQ0bk5vZDVsYUVrVGgyZmU3UStOanUzamhVc1JGZ25VQW1seC9yTHVLS1g5RWtsb1dSN0t0SW1CakxYaUtiZDVZQ1NTV3M5TkZ1WERuMXFES3l2d3dFaGViRFp0MG96ejdWaVZUZU9jbW53TUkwSi9iZXcwbnk3MUZKbFlhTTNUYm8rSzZoQmNuc1p5eEozbmlwMmxqQk5KYWhlTUtmUUhxUm9OOXFQOGliWkFpOFQ5RVgvMkJsUXdZTy9mZmFZWFc5elZlVkRzUXdQdUdVRHZuVUR6bEdERFpubDdhSC81OHJ3a2pSTysxWEN5cWtDZjdob0NuUSs5cnNqN1VWWThkSFU1ZTlKZFJBWTN0RDJ6VUJ3Nm50aUVqdVdqM01Cb2JEMWlMRWtsSlMxenBQZ3Q0RXk1RkxFOHRWSjNENW5ScHh2aDVqd2lRZVNua0R5eWRwcmMzcnRDN1dTMEVPdTdsWVNRd2VQOWkzcFpmS0RyaHM1eHV0Nld1bm5VTGZzcTZJS0xhU3lEczRXV0Iza2R1cXZGaWRsc3VtNDl2Ti9UcjRXTE1wNm1icXkxL3QyVGR3MGwzQ0Z3VVRRUXZKUlluMUhYaVhyL2Z2NFNJa2dtUXFEMStiT0VkU1VsMHdHUFAiLCJtYWMiOiIwMjQ0NTZkMDhjMjIzM2ZhZTNhZmNmYmJiN2IyNmEyMjEzNDU3ODk4YjVlY2QxNzNkODQzZjk5NmI4MWQ3MmM5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 06:52:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2030921514\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-575792633 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2deoCJdVF4jHJ7lOTZcwIx1q68gmB1URtqhTzQnQ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-575792633\", {\"maxDepth\":0})</script>\n"}}