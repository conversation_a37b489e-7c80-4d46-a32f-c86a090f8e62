{"__meta": {"id": "X98390dee22cde3337afbf72faf5190b4", "datetime": "2025-07-29 04:58:29", "utime": **********.222407, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753765108.108606, "end": **********.222433, "duration": 1.1138269901275635, "duration_str": "1.11s", "measures": [{"label": "Booting", "start": 1753765108.108606, "relative_start": 0, "end": **********.139679, "relative_end": **********.139679, "duration": 1.0310728549957275, "duration_str": "1.03s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.139709, "relative_start": 1.****************, "end": **********.222436, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "82.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2997\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1801 to 1807\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1801\" onclick=\"\">routes/web.php:1801-1807</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GDUodjrMWKwUr2Akms6D2wD6pEYEbeaAnzKl12lu", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-484127153 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-484127153\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1633794983 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1633794983\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2132499876 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2132499876\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1691517445 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1691517445\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-947589279 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-947589279\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-397819979 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 04:58:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBmVk9rVDgvMzdCV3NwRFNOUWFaQXc9PSIsInZhbHVlIjoia0N0aUNOeW1GYmI1VVdjNHpsdUkxTFVZL2xIZC9tSE5oZ1pRVGRHYkF0MFlOdDhlbnVqcWZHTkg3M3hmcWJBd1ZIVE9EdTVKK3RET2JLM25VUE53Q2xWaFpDQkYyYVJvUzM2OWRSdzZWWnF6WXd2eFEzczUrc3B6dlJRWUw0N0s3dlJjbmh0OC81eTJHU2FQeVBSWThCcWhBSExrbFpKcXN1by9kZjJocjllaW1teWhmWWQ4Y3BOampIeWZwNTY0ZVQ1NWpEVCtrQ21GY2hKa0paU3pVVkpHdjAvSlFnRW0xWEpHdnZYV2FtQnQ3TS96OC9qbXY1eVo5MUdQTWtCbGdPY2pyYlc1akZrSmlpbzNmeUU0SUVHRHFsMXNrSytVR3RJem1mU2k0Y3FQd0hiNytrcklVWGxybllEcnZKSXV3dkN1SE9OV2pNTS9WbEJaOUFqRUF0UTlURmgyMlo2YnZ6ZzlJNm4yT0YxRDFOTVF5a21UVlpuaVJGTXhVMW4rVzFFSnNpVGlRSGNPeVVRZXVrM2JKZmtGTUxMZjlObXRwNjFORW9GWHJDSkJNSGxsK2tTQWZtUmw5c3N2NmVlWnUrWHNBZGdLSVdCOFNUK0U4RCtxWlRGK1p2eWFnWTNvbW9PR05nenlxdFdPTEtFaUtDTTR2eTFzQUtyYnlnd3AiLCJtYWMiOiIwZGYyYmQwYTBjMGQ0MmU0MjI3ZGVlZTZhNWQ4MDFjNDU3NWQ4MGUwZTc3MDQ4YWE3YTdlNGJjYWI5NjZiY2I0IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 06:58:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlFwZHhySEQ4emdleW1mZjdBOXN6SGc9PSIsInZhbHVlIjoiSTNWWVJoNFBkZm5yT083ZTZyaWFaTDlPaG5zLzN5UUlwM0VtZ3hHS2kwZzJMNXYzdzlabnNyREtlN0FlRFppcTFMZXIwZHRyQ2JPK2lLN2d6YkNYbmhudzUxWE53Y29OdHNxMkFzL21jUXlwVlpjOUt0aTF2MlVtdmpreXlpQXBVQVRvWXpnVktHNGVKVVFwWnlITlRJbUd3U1lFYkxIKzlEVi8wYkZma0ZYZ2pZMzRoUEdtbkVGeTNsQTJCVXlDZ2o2aFlFNjJkWk84Q0JuZ2pqcjc3MC90YzJ0M1ZPYWpFS0FGZitiV1psZU5JUkUvUnRGUHRWU2htZnA5UDBFSzh5MVAzMmlwRTdYQ1VOcm05d0c1bWdqY1NxemZqV0h4VzVEdmxvZ0VBaFFoeGVsOFpiWFY3eGNCU0pLMkQwYmZFVU83cUZ4L2FEUEJpU2F5YUl1cDRXR1Q2Yi9iN3Q1MkpzdnNDSi9udHlTUHNDQkxreTRVU3J4VTlic05oYmF2bmdMbmlzYWMwS2h0eTJUOU1kUzlLdWpKSTVXWmxRYnkvNGRIY3IrRkJCd1d5VkhUZDBBM0lwaTR5dUtrUVdJYXl4STlkbm55TGg0M3JLOXJBdmFWOUhDeFVhYlYzcTlDQmxLUVhHTk1sYzlaQ2p3eHBlWXFnK3V0bWNDQWVDMCsiLCJtYWMiOiJjNTA2OWNiZTdmYWVmYTRjNWNmN2YwODlmMjFhYjQ5ZTU1YTBjYTVmMjA0ZDI4ZmMwYmU3ZjE4ZDNkNWI3M2UxIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 06:58:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBmVk9rVDgvMzdCV3NwRFNOUWFaQXc9PSIsInZhbHVlIjoia0N0aUNOeW1GYmI1VVdjNHpsdUkxTFVZL2xIZC9tSE5oZ1pRVGRHYkF0MFlOdDhlbnVqcWZHTkg3M3hmcWJBd1ZIVE9EdTVKK3RET2JLM25VUE53Q2xWaFpDQkYyYVJvUzM2OWRSdzZWWnF6WXd2eFEzczUrc3B6dlJRWUw0N0s3dlJjbmh0OC81eTJHU2FQeVBSWThCcWhBSExrbFpKcXN1by9kZjJocjllaW1teWhmWWQ4Y3BOampIeWZwNTY0ZVQ1NWpEVCtrQ21GY2hKa0paU3pVVkpHdjAvSlFnRW0xWEpHdnZYV2FtQnQ3TS96OC9qbXY1eVo5MUdQTWtCbGdPY2pyYlc1akZrSmlpbzNmeUU0SUVHRHFsMXNrSytVR3RJem1mU2k0Y3FQd0hiNytrcklVWGxybllEcnZKSXV3dkN1SE9OV2pNTS9WbEJaOUFqRUF0UTlURmgyMlo2YnZ6ZzlJNm4yT0YxRDFOTVF5a21UVlpuaVJGTXhVMW4rVzFFSnNpVGlRSGNPeVVRZXVrM2JKZmtGTUxMZjlObXRwNjFORW9GWHJDSkJNSGxsK2tTQWZtUmw5c3N2NmVlWnUrWHNBZGdLSVdCOFNUK0U4RCtxWlRGK1p2eWFnWTNvbW9PR05nenlxdFdPTEtFaUtDTTR2eTFzQUtyYnlnd3AiLCJtYWMiOiIwZGYyYmQwYTBjMGQ0MmU0MjI3ZGVlZTZhNWQ4MDFjNDU3NWQ4MGUwZTc3MDQ4YWE3YTdlNGJjYWI5NjZiY2I0IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 06:58:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlFwZHhySEQ4emdleW1mZjdBOXN6SGc9PSIsInZhbHVlIjoiSTNWWVJoNFBkZm5yT083ZTZyaWFaTDlPaG5zLzN5UUlwM0VtZ3hHS2kwZzJMNXYzdzlabnNyREtlN0FlRFppcTFMZXIwZHRyQ2JPK2lLN2d6YkNYbmhudzUxWE53Y29OdHNxMkFzL21jUXlwVlpjOUt0aTF2MlVtdmpreXlpQXBVQVRvWXpnVktHNGVKVVFwWnlITlRJbUd3U1lFYkxIKzlEVi8wYkZma0ZYZ2pZMzRoUEdtbkVGeTNsQTJCVXlDZ2o2aFlFNjJkWk84Q0JuZ2pqcjc3MC90YzJ0M1ZPYWpFS0FGZitiV1psZU5JUkUvUnRGUHRWU2htZnA5UDBFSzh5MVAzMmlwRTdYQ1VOcm05d0c1bWdqY1NxemZqV0h4VzVEdmxvZ0VBaFFoeGVsOFpiWFY3eGNCU0pLMkQwYmZFVU83cUZ4L2FEUEJpU2F5YUl1cDRXR1Q2Yi9iN3Q1MkpzdnNDSi9udHlTUHNDQkxreTRVU3J4VTlic05oYmF2bmdMbmlzYWMwS2h0eTJUOU1kUzlLdWpKSTVXWmxRYnkvNGRIY3IrRkJCd1d5VkhUZDBBM0lwaTR5dUtrUVdJYXl4STlkbm55TGg0M3JLOXJBdmFWOUhDeFVhYlYzcTlDQmxLUVhHTk1sYzlaQ2p3eHBlWXFnK3V0bWNDQWVDMCsiLCJtYWMiOiJjNTA2OWNiZTdmYWVmYTRjNWNmN2YwODlmMjFhYjQ5ZTU1YTBjYTVmMjA0ZDI4ZmMwYmU3ZjE4ZDNkNWI3M2UxIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 06:58:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-397819979\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1313536040 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GDUodjrMWKwUr2Akms6D2wD6pEYEbeaAnzKl12lu</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1313536040\", {\"maxDepth\":0})</script>\n"}}