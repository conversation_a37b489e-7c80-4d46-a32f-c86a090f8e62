{"__meta": {"id": "Xd0c050dc63d5e670a38044ccc4f004ff", "datetime": "2025-07-29 05:03:24", "utime": **********.700343, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753765403.483282, "end": **********.700384, "duration": 1.217101812362671, "duration_str": "1.22s", "measures": [{"label": "Booting", "start": 1753765403.483282, "relative_start": 0, "end": **********.605174, "relative_end": **********.605174, "duration": 1.121891975402832, "duration_str": "1.12s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.605199, "relative_start": 1.****************, "end": **********.700387, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "95.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2997\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1801 to 1807\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1801\" onclick=\"\">routes/web.php:1801-1807</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "mJKQvHLvi150EduTB9QI0ZRqyiOzs0ibO2w3aqi6", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-285039265 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-285039265\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1974922836 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1974922836\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1101811555 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1101811555\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1038571572 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1038571572\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1298069152 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1298069152\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1985687118 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:03:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJQSkJzcytWUXA3Qit3VFFHNHl0dFE9PSIsInZhbHVlIjoiT1B2em9Pc1FhUElJaDFJUVJJelR2RVJBMS9aK294K1VENjB4TFIwYTdaOGFWTGh4cVBuM3pHRTNjQ3NvLzBndzc2clBHaHRLME03YlRyQTRPMW9aM0s4eEhXKzdFem5OMVdPRXo0dy84OHBMVFI5eUNuRkthTjE4cWhDK0o0Z0p1YlkrVlB0NWc1Q09MQkJCYXQ1Qnd3eTk3ZnE3M0JDVmtuZUd2ek1ubnREaEJNaTNtMUVkc3Rta2dRRkozblJjZUFadkJXeSs5Zjk4RlMxYVZnMmh6cGNRb1krM2FtRmtFMVFqTFpZa3oyaCtBVUxJZ1B2TU9veVVINVdwYTdqYWlVZHlDeExyTVlwSzZtdmY0VFZjd3ZLRlltS2t4dWtlVFBwaTBRcEpRR0NmdE5TQnI2K0pPRC92UkY1TkplNWpZQVlVcHpoTGROQmxHYzBaUkdhRHE5OHZQbUwwSWo5OS9ad2tiMWNoY25YcWJBcFlGQWo5MDZlZnVPWHgwYytzaEttVE1TZUhoUXpBTnFGSkE2ZjlpdHJrQWIrZWNMcmpLd3ZmclBxMEtZck0yYnRza2cvcUdFZlVqRSs4YWRsT1k1STcyTXpIVVFCU1NnbEk1N3l6cnhkbnZaYjVWN1pVTG5ONG9pTVkzSVlWTVQra2hGMDBpbndPUlhkcjJwdk4iLCJtYWMiOiJmNjk1NDEwZjYyMTliYjdhZGM5YjM1M2Y2YmQyYmRjMDE3MDhkODE1YzAyODM4MzU5YzZiYWQ1YWIzYjE0OWU3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:03:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjloODQ5bHN2cWJJamZWQ2hFek5rZlE9PSIsInZhbHVlIjoiMmVPcjY4TXBwR1RMKzNxSE9IcVpUVmYzRXZ1MWVLL3BYZ2FRNFZ5VWpEUVJqdVg3aDRrb0VUbTBCTFdwY0VBUTl2WW5EVXF4VnAvNXM5QmtXUmVqL2IrRjM4d3pHay9lb2Zja0dtanVaZ2IzTVAxMG5DMUZ3cjVIb0I2eE0xblcraUhZRHJxaVpkY21PdzRTQkpFKzB5RTkwMzBzbGRqOFZsQ2JNSjRNakN4aUQvYndpR056c0pvcUd0SFp1dU41MWJnYXA0TmtrLzNYMXNNS01UUmFMb1ZOZVBnREpTT3RQckEvRE5ZU0ZZVVhjUEFwcllVOEgzQ3gvNG9mcFlWWGJuVCtQdGx0SFhhYVFHOSt4UDVTajVGaWdsU3ZFaWtLL1ZkWFE5REpwN2lRTnlHQ215YjBCNHE5aGkraklQSkVxclZQR2ZpMnBrRUI3RjZoWGpQK2Q5eFRsWVd4Z05DT0ZSTDRBNEpUNCtLMkdIakNuREpoMGg1emlqeVcrRHpGOTh4YXhOMjJoWUJXZlUva3RwOEtzNkhpd2EvZ0kyMU5rQ2dxRFpseklqdnN5OVI4NGhuQTAvbjg1S3kxNTFUWkQxZS9XYkxaYnI2cnBhQVY1c2ZQbTRFZTJGbHQyTzhkaGJ0amxEeEhmRmRSWEcycE5VbEdCRUgyYVU3L2huUmQiLCJtYWMiOiJhOWRiYzRjMmJjOTdlNTViM2YyZGUxMTExY2UxMzMwMzQxMWU2ODRmNGEzYzM3M2ZmMjkwMDJmMjUyYzNkMGQyIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:03:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJQSkJzcytWUXA3Qit3VFFHNHl0dFE9PSIsInZhbHVlIjoiT1B2em9Pc1FhUElJaDFJUVJJelR2RVJBMS9aK294K1VENjB4TFIwYTdaOGFWTGh4cVBuM3pHRTNjQ3NvLzBndzc2clBHaHRLME03YlRyQTRPMW9aM0s4eEhXKzdFem5OMVdPRXo0dy84OHBMVFI5eUNuRkthTjE4cWhDK0o0Z0p1YlkrVlB0NWc1Q09MQkJCYXQ1Qnd3eTk3ZnE3M0JDVmtuZUd2ek1ubnREaEJNaTNtMUVkc3Rta2dRRkozblJjZUFadkJXeSs5Zjk4RlMxYVZnMmh6cGNRb1krM2FtRmtFMVFqTFpZa3oyaCtBVUxJZ1B2TU9veVVINVdwYTdqYWlVZHlDeExyTVlwSzZtdmY0VFZjd3ZLRlltS2t4dWtlVFBwaTBRcEpRR0NmdE5TQnI2K0pPRC92UkY1TkplNWpZQVlVcHpoTGROQmxHYzBaUkdhRHE5OHZQbUwwSWo5OS9ad2tiMWNoY25YcWJBcFlGQWo5MDZlZnVPWHgwYytzaEttVE1TZUhoUXpBTnFGSkE2ZjlpdHJrQWIrZWNMcmpLd3ZmclBxMEtZck0yYnRza2cvcUdFZlVqRSs4YWRsT1k1STcyTXpIVVFCU1NnbEk1N3l6cnhkbnZaYjVWN1pVTG5ONG9pTVkzSVlWTVQra2hGMDBpbndPUlhkcjJwdk4iLCJtYWMiOiJmNjk1NDEwZjYyMTliYjdhZGM5YjM1M2Y2YmQyYmRjMDE3MDhkODE1YzAyODM4MzU5YzZiYWQ1YWIzYjE0OWU3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:03:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjloODQ5bHN2cWJJamZWQ2hFek5rZlE9PSIsInZhbHVlIjoiMmVPcjY4TXBwR1RMKzNxSE9IcVpUVmYzRXZ1MWVLL3BYZ2FRNFZ5VWpEUVJqdVg3aDRrb0VUbTBCTFdwY0VBUTl2WW5EVXF4VnAvNXM5QmtXUmVqL2IrRjM4d3pHay9lb2Zja0dtanVaZ2IzTVAxMG5DMUZ3cjVIb0I2eE0xblcraUhZRHJxaVpkY21PdzRTQkpFKzB5RTkwMzBzbGRqOFZsQ2JNSjRNakN4aUQvYndpR056c0pvcUd0SFp1dU41MWJnYXA0TmtrLzNYMXNNS01UUmFMb1ZOZVBnREpTT3RQckEvRE5ZU0ZZVVhjUEFwcllVOEgzQ3gvNG9mcFlWWGJuVCtQdGx0SFhhYVFHOSt4UDVTajVGaWdsU3ZFaWtLL1ZkWFE5REpwN2lRTnlHQ215YjBCNHE5aGkraklQSkVxclZQR2ZpMnBrRUI3RjZoWGpQK2Q5eFRsWVd4Z05DT0ZSTDRBNEpUNCtLMkdIakNuREpoMGg1emlqeVcrRHpGOTh4YXhOMjJoWUJXZlUva3RwOEtzNkhpd2EvZ0kyMU5rQ2dxRFpseklqdnN5OVI4NGhuQTAvbjg1S3kxNTFUWkQxZS9XYkxaYnI2cnBhQVY1c2ZQbTRFZTJGbHQyTzhkaGJ0amxEeEhmRmRSWEcycE5VbEdCRUgyYVU3L2huUmQiLCJtYWMiOiJhOWRiYzRjMmJjOTdlNTViM2YyZGUxMTExY2UxMzMwMzQxMWU2ODRmNGEzYzM3M2ZmMjkwMDJmMjUyYzNkMGQyIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:03:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985687118\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-861791612 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mJKQvHLvi150EduTB9QI0ZRqyiOzs0ibO2w3aqi6</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-861791612\", {\"maxDepth\":0})</script>\n"}}