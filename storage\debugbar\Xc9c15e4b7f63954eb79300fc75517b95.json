{"__meta": {"id": "Xc9c15e4b7f63954eb79300fc75517b95", "datetime": "2025-07-29 05:06:14", "utime": **********.495853, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753765573.618267, "end": **********.495889, "duration": 0.8776218891143799, "duration_str": "878ms", "measures": [{"label": "Booting", "start": 1753765573.618267, "relative_start": 0, "end": **********.41489, "relative_end": **********.41489, "duration": 0.7966229915618896, "duration_str": "797ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.414907, "relative_start": 0.****************, "end": **********.495893, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "80.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2997\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1801 to 1807\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1801\" onclick=\"\">routes/web.php:1801-1807</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TM673HvKYez8sNAikUP4qxDtFyNb8uY7HITlwuCq", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-966166945 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-966166945\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-647354500 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-647354500\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1806115435 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1806115435\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-85078314 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-85078314\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1904508323 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1904508323\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:06:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9CN29JK3doSVVFUUlaM1Z3WDBiOEE9PSIsInZhbHVlIjoiTWZISCtGV293MGgzS0g1WlIxUkxwL0FqSGl5eEYwNjcyQmpkYmZWTXpxU2xIcjVHOUpXeDNTaGZlamkvdm5tQU5LTFlJUmhRVG94UDdKZGFsYkpNRjJsK0RRVlhBb2tweE9DbzJraWhKVEtYa1haRmRTMzJmZUhrRnkxUGFtVjlGMmxiRFEybmpxSmpqMXNaWm5OUmlycHNkZ1dUL0sydFF6OU9GQllVaDNpK3NMcEwvUWtzeThsbDh3Z2xGM0RWZU43cjYrZ0FYeFYxbzg2ZmJtODN2bjBHczdjaWhKM0FsSnhkRFpuWTZVUVExNUNZdk9oL3gzY1Z2NGFaOW1ocGNNc2thRXVOVnNIR096RVJKckJhRjNQcWhYWnVWRnFldXpRN2hWUjhtWDFlTkJJK1ZzNjZWVmJiQTFjOVlrY1dBanUvUEhNc0dtMWt2OEVvdUR2QlFoODMwVzVSNzB3WXZnUkFYamZKdXE1QnphUXZhOUo3bUtnY0E1L0MwclBaN0Irc1RVTHgvbkdzSGhpSnd5clRUUWRNT1YzRFBJNjU1V1hJbHoraDR2RGRJVWllbWF5RmxJY2hSWC9aQ3lOSXVyU1M4bGtOWG9xQTVxb0Z3YXp6MUl6MjhZSm4wemhUQUM4dWVSNHo3YzlxT1h0U01pS0JwaUEzbTE4TzhPNVMiLCJtYWMiOiIwM2RlNmFkYzM3NzI1NTMzNmY3MmNjODY4NWI5OTk5ZTE5Y2NhMmE5MzY0YzQ1ZjE4OGJlMTlmZTNmZTAyOTY5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:06:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ims0V3VOOURmcHF0L0NuV2dnT0t6anc9PSIsInZhbHVlIjoiMDVSVHBZWGs0R3ZUT1dHNEFaWURjWi92Ly9HQkN2Y3ZxK1NlZm9URU41WldlZ09URXBUQ2J1YWpPeUE0WVJHNmRwT3d3Y2hvMGFJNlkvdjJxS3JVamtacjZVMzFMOWFOSkQ5dzVjQ3pLQkx2bGJWMDNsSk8rWnp1THNZaWwxcWNhS29KZUVoUFBvVFlHS2pRdjVzOVdKQzZKbUoyYndJdGtSZzd2ak8yVGYrcmQ5ODFNREZ5K1VqNjErRWhYbnhRUEd6NU1rMGFKdVZPdWhiRFJPYjYxVWZxenl5RitrZFI3a2xqT3NNWTJvbDBQWVBqaUpZOGhjRVpUbHk0dTlsb1RsTzhTa2czcmZUTDN5VFVkWDh5cnNRSkg4elRkczF3cHFXZFBlWHY0eXp3MmUrVUxGTXg1cS8xcVRQRXd6SkV4V0tmM0hlbXhxSXVLeXpHbkpKRW0xMS9JVGlPbFNzOFhFRHJNUGpNa1BoYjBGeERtZEhsYzljN0ZIQWpJQVRLMm1LaDNwQzl6UVFva25vVWl6QTVwTnZIOEYrV1FLeEVnNnBvZExpdnFqT0I5UlJmYzZ4Q0M3YmRzUDBQNkdUNHV3YWtFaXR1d0l1aWRCb2NVYTdpRHIxbGE4dm5ickhaQVZxQyt5RHA5U2RhL3RXbi8wMnhtMG9kWTQvNzJBQWwiLCJtYWMiOiJjZDEzZGNjZDMwZWRlNmVkODNmNGJlZjVlZjg0YmI3NjdhZjQ1ZDJlMGY1MmNkZDA1M2U3YThmMjA4YmNiOTU3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:06:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9CN29JK3doSVVFUUlaM1Z3WDBiOEE9PSIsInZhbHVlIjoiTWZISCtGV293MGgzS0g1WlIxUkxwL0FqSGl5eEYwNjcyQmpkYmZWTXpxU2xIcjVHOUpXeDNTaGZlamkvdm5tQU5LTFlJUmhRVG94UDdKZGFsYkpNRjJsK0RRVlhBb2tweE9DbzJraWhKVEtYa1haRmRTMzJmZUhrRnkxUGFtVjlGMmxiRFEybmpxSmpqMXNaWm5OUmlycHNkZ1dUL0sydFF6OU9GQllVaDNpK3NMcEwvUWtzeThsbDh3Z2xGM0RWZU43cjYrZ0FYeFYxbzg2ZmJtODN2bjBHczdjaWhKM0FsSnhkRFpuWTZVUVExNUNZdk9oL3gzY1Z2NGFaOW1ocGNNc2thRXVOVnNIR096RVJKckJhRjNQcWhYWnVWRnFldXpRN2hWUjhtWDFlTkJJK1ZzNjZWVmJiQTFjOVlrY1dBanUvUEhNc0dtMWt2OEVvdUR2QlFoODMwVzVSNzB3WXZnUkFYamZKdXE1QnphUXZhOUo3bUtnY0E1L0MwclBaN0Irc1RVTHgvbkdzSGhpSnd5clRUUWRNT1YzRFBJNjU1V1hJbHoraDR2RGRJVWllbWF5RmxJY2hSWC9aQ3lOSXVyU1M4bGtOWG9xQTVxb0Z3YXp6MUl6MjhZSm4wemhUQUM4dWVSNHo3YzlxT1h0U01pS0JwaUEzbTE4TzhPNVMiLCJtYWMiOiIwM2RlNmFkYzM3NzI1NTMzNmY3MmNjODY4NWI5OTk5ZTE5Y2NhMmE5MzY0YzQ1ZjE4OGJlMTlmZTNmZTAyOTY5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:06:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ims0V3VOOURmcHF0L0NuV2dnT0t6anc9PSIsInZhbHVlIjoiMDVSVHBZWGs0R3ZUT1dHNEFaWURjWi92Ly9HQkN2Y3ZxK1NlZm9URU41WldlZ09URXBUQ2J1YWpPeUE0WVJHNmRwT3d3Y2hvMGFJNlkvdjJxS3JVamtacjZVMzFMOWFOSkQ5dzVjQ3pLQkx2bGJWMDNsSk8rWnp1THNZaWwxcWNhS29KZUVoUFBvVFlHS2pRdjVzOVdKQzZKbUoyYndJdGtSZzd2ak8yVGYrcmQ5ODFNREZ5K1VqNjErRWhYbnhRUEd6NU1rMGFKdVZPdWhiRFJPYjYxVWZxenl5RitrZFI3a2xqT3NNWTJvbDBQWVBqaUpZOGhjRVpUbHk0dTlsb1RsTzhTa2czcmZUTDN5VFVkWDh5cnNRSkg4elRkczF3cHFXZFBlWHY0eXp3MmUrVUxGTXg1cS8xcVRQRXd6SkV4V0tmM0hlbXhxSXVLeXpHbkpKRW0xMS9JVGlPbFNzOFhFRHJNUGpNa1BoYjBGeERtZEhsYzljN0ZIQWpJQVRLMm1LaDNwQzl6UVFva25vVWl6QTVwTnZIOEYrV1FLeEVnNnBvZExpdnFqT0I5UlJmYzZ4Q0M3YmRzUDBQNkdUNHV3YWtFaXR1d0l1aWRCb2NVYTdpRHIxbGE4dm5ickhaQVZxQyt5RHA5U2RhL3RXbi8wMnhtMG9kWTQvNzJBQWwiLCJtYWMiOiJjZDEzZGNjZDMwZWRlNmVkODNmNGJlZjVlZjg0YmI3NjdhZjQ1ZDJlMGY1MmNkZDA1M2U3YThmMjA4YmNiOTU3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:06:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TM673HvKYez8sNAikUP4qxDtFyNb8uY7HITlwuCq</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}