{"__meta": {"id": "X5f42543e9688b932e0c5dea91f13ab11", "datetime": "2025-07-29 04:59:10", "utime": 1753765150.0284, "method": "POST", "uri": "/api/get-pipeline-stages", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[04:59:09] LOG.info: ContactController: Getting stages for pipeline {\n    \"pipeline_id\": \"30\",\n    \"user_id\": 84,\n    \"creator_id\": 84,\n    \"request_data\": {\n        \"_token\": \"pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U\",\n        \"pipeline_id\": \"30\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.965324, "xdebug_link": null, "collector": "log"}, {"message": "[04:59:09] LOG.info: ContactController: Found stages {\n    \"pipeline_id\": \"30\",\n    \"count\": 3,\n    \"stages\": [\n        {\n            \"id\": 118,\n            \"name\": \"New Leads\",\n            \"order\": 1\n        },\n        {\n            \"id\": 119,\n            \"name\": \"Follow Up\",\n            \"order\": 2\n        },\n        {\n            \"id\": 120,\n            \"name\": \"Send Flow\",\n            \"order\": 3\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.971945, "xdebug_link": null, "collector": "log"}, {"message": "[04:59:10] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 84,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/api\\/get-pipeline-stages\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": 1753765150.024421, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.218311, "end": 1753765150.028426, "duration": 0.810114860534668, "duration_str": "810ms", "measures": [{"label": "Booting", "start": **********.218311, "relative_start": 0, "end": **********.88685, "relative_end": **********.88685, "duration": 0.6685390472412109, "duration_str": "669ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.886867, "relative_start": 0.6685559749603271, "end": 1753765150.028428, "relative_end": 2.1457672119140625e-06, "duration": 0.14156103134155273, "duration_str": "142ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48169976, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/get-pipeline-stages", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@getPipelineStages", "namespace": null, "prefix": "/api", "where": [], "as": "api.get-pipeline-stages", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=143\" onclick=\"\">app/Http/Controllers/ContactController.php:143-198</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.02019, "accumulated_duration_str": "20.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.936101, "duration": 0.01383, "duration_str": "13.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 68.499}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.961155, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 68.499, "width_percent": 3.566}, {"sql": "select `id`, `name`, `order` from `lead_stages` where `pipeline_id` = '30' and `created_by` = 84 order by `order` asc, `id` asc", "type": "query", "params": [], "bindings": ["30", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 168}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.966114, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:168", "source": "app/Http/Controllers/ContactController.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=168", "ajax": false, "filename": "ContactController.php", "line": "168"}, "connection": "omx_sass_systam_db", "start_percent": 72.065, "width_percent": 3.219}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.979796, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "omx_sass_systam_db", "start_percent": 75.285, "width_percent": 3.318}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (84) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.98755, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 78.603, "width_percent": 4.061}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (84) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.991764, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 82.665, "width_percent": 3.616}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.996391, "duration": 0.00277, "duration_str": "2.77ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 86.28, "width_percent": 13.72}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 543, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 549, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/api/get-pipeline-stages", "status_code": "<pre class=sf-dump id=sf-dump-936924695 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-936924695\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1699667028 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1699667028\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1417441150 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1417441150\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-281254983 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">62</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6InZHUzhaS3puaUdINVUrT0NIdWxvNmc9PSIsInZhbHVlIjoiR01pMnE3N05jeHpFNXdJVWxRUHRZQVFKeERBOHltYlFoanBOZDNReWJ6NzczcXFYQnkrRjEwc2dvMHZuNHlnczFFVHh1V1pJOHBVWEZZWlFiOE9vMldhNGgxYkpBRVVVSklMY2xMcTdmeVRTb0xOR0NSRG5OYWU1VjQ4Rm83VVJsMll0K0tJQ2gvTVNZSVRTaUVXMHkxZnQxNDVnbVJJaStOVmVuOGNjU20rdFZOUnRaQ3J3azFOTEw0KzQyeDhVYzdtTndvMm1YbDNSb0hQcC9DZENEaGJjU3Jta2RQVVRWYWJPYWtoWE1saGl4S3V0N3FCRTdNc09zYWVEOFN3ckVyTUdOS1BMMTJPR1NhcWFxcWg5WnFDQ3NIVFR4L2FqMHBydDhmaUV4WUtIdkkwdkMvQ0JJUUZVRndkdk5LMjd4Nm9yZ0FwYmtTY2dRaXdWejkvOUpKVUFYL3FLcHBxVzQ0U29vSHdKNC9BR0xrYUdhQXRETU1OVnNVL29xSUtFMjBIK0V1Wm8zdTR3L2JGQ1hVaE5nRHcxdFIyT2Z5d2lBbDNURHJFK3E1MHlNREJOOXRyME9rZU5HTTRiS3dFSjZOOTNnYTAxQW9qa21wbHZyWUhTcXFpM05uSnJtVnlpVGhjbVIxR29YSWRpV2NpUHZpU1haQXhpRmpEZnhNV1MiLCJtYWMiOiJmOTI4NjRmMWE2MzM4NzkyYjU5NTk2ZTBhOGZhMWJlNjliMDQ1YTQ1YTFmODE5NDk3MTUxOWQ5Mzg1NDA3NTc1IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6Ilo2b1o2MXVIamlxN0R1WjNwZkZkRlE9PSIsInZhbHVlIjoiUXNMNnY3RW9BMUpKay9YVy9UalhKbmNTSmNyeEkyc05vS21HVDIybUJyNElBWDZWem84V3BsRFJWcVQ2VVdCSkZCaGxRZnlxZWNwRytKanlPd01jeG9iYmZCdDhsQWdmbjZkYUtDVTcrNXYva0lLU2xEdjJnNmVJNlcvZXJZSzZjNVE2Sk9DVFBvdER3eko4RUNMS04xL2RQai8wMS9vV0tKZmxpTmZORGVrNHQwVEx3S0JDSGJDczBFUzZqa1FzSHZ6ZGFYaEsrRXVRSFFBd0cxc0RQVWdGRUhwcEVzcStnSjVzVXdMekh4UXkvZW9ERnlLZWlBd1hoN3RDeWxWbHNmMWJtWG8xcHJZZGNxSUxEMEFtdS9sK21OU2ZDajBRMFJzK0kzN0lPaE56WW9mbXFLMUtzcWo2eUZVUWVYNC9YQ1BWbDUySUxvd3I3eitmd2FhTGZSN3o1TTU0MWZLYm9OeVIrNCtQS3NCdWUzS3Yvcm5sMTdxNW5DK1FLaGJCWEI5RDFDbm9GSEFQektrQm9YSVJvL2ZEeTFhQkdtcEVkdUpSYjE3cVRzL0hVeUFUZkdGZHVZT1psQXl5bVlybzFkeVBGL3BzT0F0Smx1Q29YRFJ0Q2NpVzRrazVSbWtVb0N6TnA4YW81UmlxRDdSK1lsYjlKa1JrTWVtR0RFV3AiLCJtYWMiOiJiZTRmMTIzMTFkNzFjMDQyMzIyNDBmMDk2YTdhMGRjM2UwYTNlYjQ0MzJkOWVmNDcyYzc5ZThlOGFiM2M0ZWNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-281254983\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1639619186 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1639619186\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-368592820 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 04:59:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNuTkhZcVhVRG9SMXpRbFVseWErYUE9PSIsInZhbHVlIjoiS0hsV1ZtTEdXZTUrRVMyM2hBY0kyRzFkNDJoV2hyY3dkVDEwZVZCbmdRRkhZTmdwTVpIS2VaTHorNElzdVB4aFFocTdyQzQ4QzVrSUVUdXNqMXJVSnc2OUpKRDE1T1lEWmlYcmdaZ0crM2NHQmVHSTNiUDFSY016RDNCZDQ2T2NkaFkyWHFFVGlkb1FzTFNwTEhGUXJkcC9ZbVlYaUNQNk43Z3FsQUxUSEJSa3NHejJNMnJqUDltSkhUcUdDbUFvcHV5YnJlUWo3L0sycml2OW5YbFFVcHhVaXpBUW1WNHllTzk5V0RGQkU5RGJTZWVUQnpma3pVaHBySHcwMzQ1UkxvWHVVYy82UTZoOUY1VSt0dVdzTTRWd0E1eHZRY0p0b0JGM25NVEUrdUU0a3dVQ0d4bHloN1VlUjBMcHQ0MlZlK1Z1c1BCYjl3VkhGaW1YUkJ5aFpJTHh1K3R2UkZYVFVaeE9GSkJVdlZHSmVGd25HdWZ1RkYzSFIyU1ZMTVA0K1JXcVhBVG0vRGtFY2gvSzRuRllEMDZHMlJ5dXZzc2pIbG9tV1RIcDFsZGFRWVFHY1YxQk0vY2NTd0xid1UvblJYWFRsOFRuM0tXOGNkbElIVVduaHIyTVB0a2xGYUFlT3N5bzdlN1FCMWovMXArdTk4TnM1VjVTOXRpTTN1bUsiLCJtYWMiOiIwYWI2NzhmZWE1MmE4YmIzNjE5MjI2ZWE3Mzg2YjU0NTVjMmU2NWRlYTUwMzYzNDNhYTc1MjRjNTRiYjJjOGExIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 06:59:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkZIdGw0UXdpT1JVTjNKZFJ2TmxPbVE9PSIsInZhbHVlIjoiQjhpNXcyV0pqQVRzbEFvL3NjUFpuRVRhcmszUFNhdXlKemV6aGM1c2ZrUW1TR0dvQ2s4bHFJSVE2cldPTEYvRUJFTW8rZmdYWTRGanJMTENuajdxbWxaNW9uSkhyVUhGcVdQYVlYZ0hLaldFdllhUjAvTEI0WE5xdlhQdFpYTXp2Yk1yVzN4bDZ6Ny9EbDYvT2M5TUJ3Nzd6WXFFQ2EvRTBBeC9KZXpGaDVKTWZiSGtxcllybmtaS1g5amU2eHdGOTd6V2EyUHRicE5PdlJqM2EybDlWK285b213WEFJV3RoNzFGdjFqNSs1WGY2WWZrOGwrYVp2REFQWThHZi9QcEpXRlNhMDJzUG0rbnFxVmc1bUZsY0c0OC82ZTRacG9sSDkzMHF1Y3RTL1k4SmtTZTBDNjJQRHB1Mm1YVFlSQmtucHh2Zms0eVk2eDNOaFFlVVBpbVEvenhrc0FjMGxKTmxCaVEvNi9OL3ZlV3lhdmNzOENxdDJBTTlVWCt2Zzhjd2pVTTRuV2M4VUJqb2hyQnUzRG94NWNqUm8vNXZaZCtEUDBlN0F0VHdIeno2RzYwaHNJOUUwZGVmVW84Z005ZmYxOSsrM0x6R3Z1dUdQbEpVbW1DalBFQStiWFE4TkxjRE1WSG13cGlWSXdWVGdwVVhZTExVRWFOVzIzMytScDciLCJtYWMiOiJhNjdjMTNhNjRkNWZkZTAwMjQ5MDJkNmQ0YjA4MDRmMDFhYWI5OGMyZGFkM2UxNGY2Mjg4YzRkNWQzMTk2Yzg2IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 06:59:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNuTkhZcVhVRG9SMXpRbFVseWErYUE9PSIsInZhbHVlIjoiS0hsV1ZtTEdXZTUrRVMyM2hBY0kyRzFkNDJoV2hyY3dkVDEwZVZCbmdRRkhZTmdwTVpIS2VaTHorNElzdVB4aFFocTdyQzQ4QzVrSUVUdXNqMXJVSnc2OUpKRDE1T1lEWmlYcmdaZ0crM2NHQmVHSTNiUDFSY016RDNCZDQ2T2NkaFkyWHFFVGlkb1FzTFNwTEhGUXJkcC9ZbVlYaUNQNk43Z3FsQUxUSEJSa3NHejJNMnJqUDltSkhUcUdDbUFvcHV5YnJlUWo3L0sycml2OW5YbFFVcHhVaXpBUW1WNHllTzk5V0RGQkU5RGJTZWVUQnpma3pVaHBySHcwMzQ1UkxvWHVVYy82UTZoOUY1VSt0dVdzTTRWd0E1eHZRY0p0b0JGM25NVEUrdUU0a3dVQ0d4bHloN1VlUjBMcHQ0MlZlK1Z1c1BCYjl3VkhGaW1YUkJ5aFpJTHh1K3R2UkZYVFVaeE9GSkJVdlZHSmVGd25HdWZ1RkYzSFIyU1ZMTVA0K1JXcVhBVG0vRGtFY2gvSzRuRllEMDZHMlJ5dXZzc2pIbG9tV1RIcDFsZGFRWVFHY1YxQk0vY2NTd0xid1UvblJYWFRsOFRuM0tXOGNkbElIVVduaHIyTVB0a2xGYUFlT3N5bzdlN1FCMWovMXArdTk4TnM1VjVTOXRpTTN1bUsiLCJtYWMiOiIwYWI2NzhmZWE1MmE4YmIzNjE5MjI2ZWE3Mzg2YjU0NTVjMmU2NWRlYTUwMzYzNDNhYTc1MjRjNTRiYjJjOGExIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 06:59:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkZIdGw0UXdpT1JVTjNKZFJ2TmxPbVE9PSIsInZhbHVlIjoiQjhpNXcyV0pqQVRzbEFvL3NjUFpuRVRhcmszUFNhdXlKemV6aGM1c2ZrUW1TR0dvQ2s4bHFJSVE2cldPTEYvRUJFTW8rZmdYWTRGanJMTENuajdxbWxaNW9uSkhyVUhGcVdQYVlYZ0hLaldFdllhUjAvTEI0WE5xdlhQdFpYTXp2Yk1yVzN4bDZ6Ny9EbDYvT2M5TUJ3Nzd6WXFFQ2EvRTBBeC9KZXpGaDVKTWZiSGtxcllybmtaS1g5amU2eHdGOTd6V2EyUHRicE5PdlJqM2EybDlWK285b213WEFJV3RoNzFGdjFqNSs1WGY2WWZrOGwrYVp2REFQWThHZi9QcEpXRlNhMDJzUG0rbnFxVmc1bUZsY0c0OC82ZTRacG9sSDkzMHF1Y3RTL1k4SmtTZTBDNjJQRHB1Mm1YVFlSQmtucHh2Zms0eVk2eDNOaFFlVVBpbVEvenhrc0FjMGxKTmxCaVEvNi9OL3ZlV3lhdmNzOENxdDJBTTlVWCt2Zzhjd2pVTTRuV2M4VUJqb2hyQnUzRG94NWNqUm8vNXZaZCtEUDBlN0F0VHdIeno2RzYwaHNJOUUwZGVmVW84Z005ZmYxOSsrM0x6R3Z1dUdQbEpVbW1DalBFQStiWFE4TkxjRE1WSG13cGlWSXdWVGdwVVhZTExVRWFOVzIzMytScDciLCJtYWMiOiJhNjdjMTNhNjRkNWZkZTAwMjQ5MDJkNmQ0YjA4MDRmMDFhYWI5OGMyZGFkM2UxNGY2Mjg4YzRkNWQzMTk2Yzg2IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 06:59:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-368592820\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1507097577 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1507097577\", {\"maxDepth\":0})</script>\n"}}