{"__meta": {"id": "X71ea976711982efe1f5bf597cd835f59", "datetime": "2025-07-29 05:30:24", "utime": **********.301517, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753767023.414151, "end": **********.301554, "duration": 0.8874030113220215, "duration_str": "887ms", "measures": [{"label": "Booting", "start": 1753767023.414151, "relative_start": 0, "end": **********.222867, "relative_end": **********.222867, "duration": 0.8087160587310791, "duration_str": "809ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.222883, "relative_start": 0.****************, "end": **********.301557, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "78.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aqiqi0D7HeEISN2tvNqiPU15gB05NIodw7zUVai0", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1354160653 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1354160653\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1239596856 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1239596856\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1529944520 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1529944520\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-677576832 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-677576832\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1901578196 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1901578196\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-868593225 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:30:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdhK09uaGRNeFNXTzh2RkV1QnlTVnc9PSIsInZhbHVlIjoia2FyZHNnMisxNkNhdlJKRkNPUFhRdFdmRkNDc0FETldMZHEwNnk4QzY1c0xSMkdBZ05WaWQzQnZtRUtkQjZsVnpaVkZONzNTaFJ3MndWMWgwQTJ0a3pOSEJkSnJZTFY5ZGZOeDA1V2gwNmdxUWhzekRxUnVreFNwR2xrNmN0am9LeVEzaFZhczcxdkx0MVpWYjBjTnBicHlzNVYvS0hGS3p0NjY3VWNDMmNsYWQrNHlUU21vQWVWSHJaSit1ejhlK1NxR3d3SThRbjRQTWNmd1BmVy96ZWd0dkxLRUdQemxoUEFpZi9KRkd3UTkyaWpGVmhlWFNTdWc1TEhCWnJ3aHlpWWZBRmJUQkNiRExkV2s0TUsvV3gxWTVGZkZWZTBqbWplS2ZCS3VOQXFuMWxRVGFIU0pjZTN1eVV3SUNzWXRFT0xyRHVLZUxTeFBNczA2bVNsMDFNMGlnQkhPa2NtUE1UNzVkU3ZITmxwM3pEMG1WQTFHdTdLbUhuZm5rQmJ2a1BxMTdaM0U0aHNxNDhOb1JzNGs1VHlHZXpRVEtSUEUzVllvb0I2U0lOSHh4eHh4aWhGMWhJYTdMS2ZXcVFkVTlrQ05OTyt0bU0yTzl4cXAyS0tiQ2dRK1c3UmV2aXFJWU0wQTY1V2tjNDFpUklJOEdtcVpOMGlkVHJxRzNKclMiLCJtYWMiOiIyMTE0MjI5ZmU1ZWZmYjBjM2FjNzExMzg5OGFhNjE1Y2QyOTAxZGE2NGUzNmUwOGI1OTM2YTJlMGYwYTkzNzk0IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:30:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InQ2dm1nSktsamFxTFJpR2gvMUNQN1E9PSIsInZhbHVlIjoiajQvaHFuSjNqQ2pvTEVJeEYyMlBVSW9XdFVMNXZET1ZXbk9FaTJpVnNINnZVanV1b1RaNVNPVjh6N04wRnJUeWhGVXg5VlBoZloxajFHWlpIY2xqZ0hZbnoyaWRkbHZOUENSM3VGdSswaEVvNnpYU0k1RzNjdlRTOThDUXRGdzFzZDFSRTQ5WFRJTVFsd0M2c2dBejFHZVV5YTUxZmZXYUhDcXh5cjZ4VElJaGNHdVcrSm5EZzNhMmhBaUlreVczU0pXM2ZYaHpBWTBlNDFMdkNudjRLdXZTWm5hWlo0eVVpdDM2VURRQVc0QmVWOE84djd3TWZaS2NJS3J4RkRSOEIvU1pwZm93WXY2WUxUTjFNUDRaeVhvVHJNVTRhTWNOSXcreEhyemlsOEFyNnk4RVlBdjM1bDFhZlZ0T3NMWGFCaG9ydDNhUHRJSkFWYmlMUCtSNmJDa1M2a3lzQWhzZ21GWGI2enhuSkpSbW1zTG5WcG4rYUxBQXk1MjExTEduWG12MllXL0wrR2UydkttWXlwenF5U3RndllURjZ5a3Y2K1VidnNCcjZYQ3Qvb2liSjhyNm93a3pKUVhxTnRLcDBMaERHb2xyU3FCMHAwL1h0c09jeWJNVkRyTVZzOVByc3h2a1dOZU1uMFJKWkRiWEZqdmJVTGJlTG9uMitpRVciLCJtYWMiOiJhMjQ5ZmYwY2EwODRmYTc2MDgzMjI4NDllNDc5OTU4MzcyMjIwYzc4YzBmN2VmOGU1MTdmM2U0MWM4MjVjYzgxIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:30:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdhK09uaGRNeFNXTzh2RkV1QnlTVnc9PSIsInZhbHVlIjoia2FyZHNnMisxNkNhdlJKRkNPUFhRdFdmRkNDc0FETldMZHEwNnk4QzY1c0xSMkdBZ05WaWQzQnZtRUtkQjZsVnpaVkZONzNTaFJ3MndWMWgwQTJ0a3pOSEJkSnJZTFY5ZGZOeDA1V2gwNmdxUWhzekRxUnVreFNwR2xrNmN0am9LeVEzaFZhczcxdkx0MVpWYjBjTnBicHlzNVYvS0hGS3p0NjY3VWNDMmNsYWQrNHlUU21vQWVWSHJaSit1ejhlK1NxR3d3SThRbjRQTWNmd1BmVy96ZWd0dkxLRUdQemxoUEFpZi9KRkd3UTkyaWpGVmhlWFNTdWc1TEhCWnJ3aHlpWWZBRmJUQkNiRExkV2s0TUsvV3gxWTVGZkZWZTBqbWplS2ZCS3VOQXFuMWxRVGFIU0pjZTN1eVV3SUNzWXRFT0xyRHVLZUxTeFBNczA2bVNsMDFNMGlnQkhPa2NtUE1UNzVkU3ZITmxwM3pEMG1WQTFHdTdLbUhuZm5rQmJ2a1BxMTdaM0U0aHNxNDhOb1JzNGs1VHlHZXpRVEtSUEUzVllvb0I2U0lOSHh4eHh4aWhGMWhJYTdMS2ZXcVFkVTlrQ05OTyt0bU0yTzl4cXAyS0tiQ2dRK1c3UmV2aXFJWU0wQTY1V2tjNDFpUklJOEdtcVpOMGlkVHJxRzNKclMiLCJtYWMiOiIyMTE0MjI5ZmU1ZWZmYjBjM2FjNzExMzg5OGFhNjE1Y2QyOTAxZGE2NGUzNmUwOGI1OTM2YTJlMGYwYTkzNzk0IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:30:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InQ2dm1nSktsamFxTFJpR2gvMUNQN1E9PSIsInZhbHVlIjoiajQvaHFuSjNqQ2pvTEVJeEYyMlBVSW9XdFVMNXZET1ZXbk9FaTJpVnNINnZVanV1b1RaNVNPVjh6N04wRnJUeWhGVXg5VlBoZloxajFHWlpIY2xqZ0hZbnoyaWRkbHZOUENSM3VGdSswaEVvNnpYU0k1RzNjdlRTOThDUXRGdzFzZDFSRTQ5WFRJTVFsd0M2c2dBejFHZVV5YTUxZmZXYUhDcXh5cjZ4VElJaGNHdVcrSm5EZzNhMmhBaUlreVczU0pXM2ZYaHpBWTBlNDFMdkNudjRLdXZTWm5hWlo0eVVpdDM2VURRQVc0QmVWOE84djd3TWZaS2NJS3J4RkRSOEIvU1pwZm93WXY2WUxUTjFNUDRaeVhvVHJNVTRhTWNOSXcreEhyemlsOEFyNnk4RVlBdjM1bDFhZlZ0T3NMWGFCaG9ydDNhUHRJSkFWYmlMUCtSNmJDa1M2a3lzQWhzZ21GWGI2enhuSkpSbW1zTG5WcG4rYUxBQXk1MjExTEduWG12MllXL0wrR2UydkttWXlwenF5U3RndllURjZ5a3Y2K1VidnNCcjZYQ3Qvb2liSjhyNm93a3pKUVhxTnRLcDBMaERHb2xyU3FCMHAwL1h0c09jeWJNVkRyTVZzOVByc3h2a1dOZU1uMFJKWkRiWEZqdmJVTGJlTG9uMitpRVciLCJtYWMiOiJhMjQ5ZmYwY2EwODRmYTc2MDgzMjI4NDllNDc5OTU4MzcyMjIwYzc4YzBmN2VmOGU1MTdmM2U0MWM4MjVjYzgxIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:30:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-868593225\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-396476432 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aqiqi0D7HeEISN2tvNqiPU15gB05NIodw7zUVai0</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-396476432\", {\"maxDepth\":0})</script>\n"}}