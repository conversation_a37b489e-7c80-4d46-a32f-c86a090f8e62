{"__meta": {"id": "X51368296c61afef3e6e62b0b907afdd5", "datetime": "2025-07-29 05:03:59", "utime": **********.278415, "method": "POST", "uri": "/chats/favorites", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[05:03:59] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 84,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/chats\\/favorites\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.274434, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753765438.214865, "end": **********.27844, "duration": 1.063575029373169, "duration_str": "1.06s", "measures": [{"label": "Booting", "start": 1753765438.214865, "relative_start": 0, "end": **********.076357, "relative_end": **********.076357, "duration": 0.8614919185638428, "duration_str": "861ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.076372, "relative_start": 0.8615069389343262, "end": **********.278442, "relative_end": 1.9073486328125e-06, "duration": 0.20206999778747559, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48260168, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.03146, "accumulated_duration_str": "31.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.133302, "duration": 0.023190000000000002, "duration_str": "23.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 73.713}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 21}], "start": **********.174429, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 73.713, "width_percent": 2.924}, {"sql": "select count(*) as aggregate from `ch_favorites` where `user_id` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 458}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.179732, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:458", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:458", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=458", "ajax": false, "filename": "MessagesController.php", "line": "458"}, "connection": "omx_sass_systam_db", "start_percent": 76.637, "width_percent": 2.289}, {"sql": "select * from `ch_favorites` where `user_id` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/vendor/Chatify/MessagesController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\vendor\\Chatify\\MessagesController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1833868, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "MessagesController.php:459", "source": "app/Http/Controllers/vendor/Chatify/MessagesController.php:459", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=459", "ajax": false, "filename": "MessagesController.php", "line": "459"}, "connection": "omx_sass_systam_db", "start_percent": 78.926, "width_percent": 2.416}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.201818, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "omx_sass_systam_db", "start_percent": 81.341, "width_percent": 3.401}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (84) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.216283, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 84.743, "width_percent": 2.543}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (84) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.222551, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 87.285, "width_percent": 2.765}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.228408, "duration": 0.00313, "duration_str": "3.13ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 90.051, "width_percent": 9.949}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 543, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 546, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1386691588 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1386691588\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-141955299 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-141955299\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1293434774 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1293434774\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1713103851 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Imh6Q2U2RkNuY2NJaVczcTBoc0hBTGc9PSIsInZhbHVlIjoiNHBqQ0JmTlBrN3BxVXFhdk5qSXByQ2xvVGg2TDhLQUh3OFUwOXBqRkJJaHJmTk5zK0NtZ3JESlNzNFo3RnFRbytzWnBBWFM1ekloMDhEalFDSHdVaElHc3ViOCtPODhmd092aXpxeFVvRGIzb2dzZHNxb3doQnp6Q2w1RWxWdENkVitKdFVMNVRrcVR1dExXLzMxeTVQZXBzQUFwY2JPUExiRGVhcTlYaFpDTmJBUXNxRGhSenV4SldydHo5K3grbFF0M1Y5YW5ZNk9mNWpDTUo3cEZtODVhOHdraGxzUkViaGVraVY2Z0lTb1I5M0FZWGVjRjkxQ2kybDl5ZUNhTURXcXBMZUwwQTFCdmphZDJmTXllQXZHNmJVU0ltcFhkd01jMzhGWWtDV2xsbForVTkxcmhpbmxwSmF1ZlpnRVVTSlVmYUJrV3ErQm44OC9JZklQUTFyUGMxT3I4b3N4Z1Erb09ucEVFaWxCOEJnT2JvMTVadnp6UEFUN1hXM21TYnl4VWdhb1Fna0l5b3V6cXNBdGpKUnV1OE02QVpwL2hsTHhnU1haV3Nid2owZVNTSk9OSHFGMHFsSXFDb3RwSUFtUjlyWGVuYXQxRlpobXdiU3dIRWR6YXFhdWVESEFRUEtDdElkK0RaL1p1TTlQM25YTVlzczJsSWEzeU0yVVUiLCJtYWMiOiI2NDA2YmQ3NzJmMzVmMDJhNmU5M2U0OTNhMTU1ODhmMjRkZGNhNzU1Y2NjOWM1Yzg0MWFlNTc2ZWIzMTMwMTJlIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkthMEpidDNlTEd5Zk9iejhsQldjaWc9PSIsInZhbHVlIjoiM1Z4SWNmdkVjYmNYNUxtT1RvQlFoSnRxUnV1ci9qL2V0R3V5ZUozTjhTelY2ckpvaWF3RktZUllQSVJCcUM5aXliUDh0Q1B4Y1RmRnU5SkVBbUtXODZOZ0pNeVRGaC83aUlhSzhnbXFqWWs0aGdLdkxaT0VTWWh3RkN2VktOUURjSUxHWHRtNGtUeTdFbEJSVFo1aHFna1ZuS1RNN0hSaXRnYVJhc29NUnMzRjRtc1Y2UWdrWXk5b2hPSXBFdTRDTWN4aGJTdHBVWTZiTlR6ejJmeWZ1QkZMQjVpMFp0ajl3bWtUVWRsaFV6RHJYNkhsWjlwTDNJUE9keEw5K2I3ZkRiMUJaMXRuci9NNFNjV2xsV2JreG55YVo4dXVFYlNPdjM1NTFFSkNGSkJJcjFSWTAwTGwvYndMUU9GYU8xZWZQK2k1cldsWlZTRlczSjEwVERpa0ErZ1hPalBSVGc2emxwTTJnMXhJNFljUU5TQk5TRCt4WnE5MFZUSXc2T1VYWTNseTZGUGVCQ1UzblBrRzlyMDRVVklLbi9saWM5TlVWVEhMY0NFMWp5a1pST2dYeG5lM0NzQUFUczdYdmtPRWZtZW8rUDFDZXY5N2ZpWFlRUEI3Tk10ekhpYS9ZNlE5NlNLR05XYktwc3plTGgwVHhQMEw2S2xGYW1iK1VrYTAiLCJtYWMiOiI3MGU1NmUwNWJiNjcyZTdjOTZiNzIxMThlMDdlMmI4NWRmZDlmZGQ0YTE4OWY3MjNkODEyYTAzZWVlMDY1MmZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1713103851\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1576671983 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1576671983\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2143187152 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:03:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRGZ2NNOTVXcFRwbDVINUk4L0lFY1E9PSIsInZhbHVlIjoibUxhZ25sMldBVm1SdU5DdXZia0h4RVdFV1NZREhOYmh0NGs0VE5qYkNBdlVsS25mUFJUclZzYjhFbWcvOFMrZkxGcm13WWZSaW9ubW94aFZCallWd21Ed3hlbjk3OVE1cTRBdEgxeVc4WnZ1NzEyd09OSXQzT2NwWXd0OU5DVWZHWWd2TTg3YVZGcENZUFEzRGcrRmdHSVgyWEJCZDdQUGt4b24rVFhIR3FTSy94ZnV0RHB0aUt6SHRmREFIWkMyVUhjVDdYNzBONVlWR3psRFFSQnFISTMxZVdJclEydlBjKzdnYUJuOFNUNmR3WTJHMWhQdnVOcnZEZXdIdnNkOW8wUERXc1VYNmNvZmpMUGtwaGVWQkFYcUpIeVBGSS8rZllMa3I2Z3R1bUxUSWZ6eEJveE5ON3RkTzBtVFR2L0RwQlNFaEM4UjlIcnJTbjZRQmFMc1JXYytMY2dienF4emJ6dHVPMThNN25ya2lTcjRpN09mak9hSmY3b0FQWFFqUGd3SmdxM1VIT2h6WXhHc0JIZ1phWFd2OFlTZWdvOFpHZmxYTkZ0LzZ3cHBRM042K3M2S1FCR2lORG40em0zQjNESmdxVXBycWxScmpPR0tCZU5GZ1FLWHNxZjBTMHAzMllvS0FESDBGSWxSdE5VWmpwVXRBT2gyQktvbWNFQm0iLCJtYWMiOiI0MDlkZTY3OGYwYzUzNWEwNGI1M2YyN2FkZjdiZDFmNTcxMzlhN2FhMzJmN2M0OGM0N2VjNzFjNTMzMzQyMzhiIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:03:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkhUaVdSLzdMeVNIQ2hHYWE2UHJXR3c9PSIsInZhbHVlIjoiUVg2dHh5L2RnYWVveVJBNjNOSjZsZmJCRWx1bXE2cjFxbVZrQ2s5b3l5enpYK1dGekFvWGRZREJlOHRucmxjTXpsa2ZYM0hlekhKTmlXWjNjaVNac1JNaHBaaTZDR0E5T2xrbXF2ZzA1ZGF3OXh1YmxuT1pMNmZOR2RsMlhhK1MrV1hiREhiQWFyclJXOGhKL2JvNmNRZTlHOHVwUVVndDlGUWk1QkRPcFVQaXhmVHJoeGdod0JsR2dDdU80U3F2eG5Hc25EYUtNTjI3RGRFQllnbXJRRGJnV2lqamZNTUhhR0tYNkE4K3JHOXRyV3F4aE01dUQrbUFVUmZ3bUZEZHFCemtzbXh2anRhQllWa1BVb3BqSUh2c0h5YzFuMHRtMG1hS2s3UEwxN2FtaUlDVTJUWE9xeDdEanNrQmhGeXorUFREdUV4UTBLNW8zVkJGT2tGdG9TZXFUZXlaWFJ3LzBMZnFlK1VvRzg1N01SUVZGQU4xenk2MUx6aTBHNjZNTWhkZDRSQ1hJOFlOUUxtSkxYWWlJMzdmZXNicFhYZU0rQ0lCR09LVUNTME0weDdNNkszVXdmamN3MHdOU1dmTEVHYXlqcDN3emRlVGNzcmlsRjNuRXg1R05rYjh0UENDT29qcUtPbEJ0ZU8zamNtRFRSZmdCRE5sd0djZlA0dGIiLCJtYWMiOiJmZWNjYmExMjE1MzRmOTlmNjEyZTkyNzBiZTJjY2Y0MzJiMzQ0ZjAzZjIyYmZiY2I1YTBmOTQyMjgzMWViMDhjIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:03:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRGZ2NNOTVXcFRwbDVINUk4L0lFY1E9PSIsInZhbHVlIjoibUxhZ25sMldBVm1SdU5DdXZia0h4RVdFV1NZREhOYmh0NGs0VE5qYkNBdlVsS25mUFJUclZzYjhFbWcvOFMrZkxGcm13WWZSaW9ubW94aFZCallWd21Ed3hlbjk3OVE1cTRBdEgxeVc4WnZ1NzEyd09OSXQzT2NwWXd0OU5DVWZHWWd2TTg3YVZGcENZUFEzRGcrRmdHSVgyWEJCZDdQUGt4b24rVFhIR3FTSy94ZnV0RHB0aUt6SHRmREFIWkMyVUhjVDdYNzBONVlWR3psRFFSQnFISTMxZVdJclEydlBjKzdnYUJuOFNUNmR3WTJHMWhQdnVOcnZEZXdIdnNkOW8wUERXc1VYNmNvZmpMUGtwaGVWQkFYcUpIeVBGSS8rZllMa3I2Z3R1bUxUSWZ6eEJveE5ON3RkTzBtVFR2L0RwQlNFaEM4UjlIcnJTbjZRQmFMc1JXYytMY2dienF4emJ6dHVPMThNN25ya2lTcjRpN09mak9hSmY3b0FQWFFqUGd3SmdxM1VIT2h6WXhHc0JIZ1phWFd2OFlTZWdvOFpHZmxYTkZ0LzZ3cHBRM042K3M2S1FCR2lORG40em0zQjNESmdxVXBycWxScmpPR0tCZU5GZ1FLWHNxZjBTMHAzMllvS0FESDBGSWxSdE5VWmpwVXRBT2gyQktvbWNFQm0iLCJtYWMiOiI0MDlkZTY3OGYwYzUzNWEwNGI1M2YyN2FkZjdiZDFmNTcxMzlhN2FhMzJmN2M0OGM0N2VjNzFjNTMzMzQyMzhiIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:03:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkhUaVdSLzdMeVNIQ2hHYWE2UHJXR3c9PSIsInZhbHVlIjoiUVg2dHh5L2RnYWVveVJBNjNOSjZsZmJCRWx1bXE2cjFxbVZrQ2s5b3l5enpYK1dGekFvWGRZREJlOHRucmxjTXpsa2ZYM0hlekhKTmlXWjNjaVNac1JNaHBaaTZDR0E5T2xrbXF2ZzA1ZGF3OXh1YmxuT1pMNmZOR2RsMlhhK1MrV1hiREhiQWFyclJXOGhKL2JvNmNRZTlHOHVwUVVndDlGUWk1QkRPcFVQaXhmVHJoeGdod0JsR2dDdU80U3F2eG5Hc25EYUtNTjI3RGRFQllnbXJRRGJnV2lqamZNTUhhR0tYNkE4K3JHOXRyV3F4aE01dUQrbUFVUmZ3bUZEZHFCemtzbXh2anRhQllWa1BVb3BqSUh2c0h5YzFuMHRtMG1hS2s3UEwxN2FtaUlDVTJUWE9xeDdEanNrQmhGeXorUFREdUV4UTBLNW8zVkJGT2tGdG9TZXFUZXlaWFJ3LzBMZnFlK1VvRzg1N01SUVZGQU4xenk2MUx6aTBHNjZNTWhkZDRSQ1hJOFlOUUxtSkxYWWlJMzdmZXNicFhYZU0rQ0lCR09LVUNTME0weDdNNkszVXdmamN3MHdOU1dmTEVHYXlqcDN3emRlVGNzcmlsRjNuRXg1R05rYjh0UENDT29qcUtPbEJ0ZU8zamNtRFRSZmdCRE5sd0djZlA0dGIiLCJtYWMiOiJmZWNjYmExMjE1MzRmOTlmNjEyZTkyNzBiZTJjY2Y0MzJiMzQ0ZjAzZjIyYmZiY2I1YTBmOTQyMjgzMWViMDhjIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:03:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2143187152\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1704164165 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/leads</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1704164165\", {\"maxDepth\":0})</script>\n"}}