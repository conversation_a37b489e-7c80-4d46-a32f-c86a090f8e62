{"__meta": {"id": "Xe24a14544440c7c5f1c270c923c93f32", "datetime": "2025-07-29 05:06:23", "utime": **********.651386, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753765582.74238, "end": **********.651419, "duration": 0.9090390205383301, "duration_str": "909ms", "measures": [{"label": "Booting", "start": 1753765582.74238, "relative_start": 0, "end": **********.553191, "relative_end": **********.553191, "duration": 0.8108110427856445, "duration_str": "811ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.553227, "relative_start": 0.****************, "end": **********.651423, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "98.2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2997\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1801 to 1807\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1801\" onclick=\"\">routes/web.php:1801-1807</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "2mWCCmO5PRVd7B0BAfaLtNgLsz1yrWZcn9cAGMfZ", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1924942677 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1924942677\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1394585791 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1394585791\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-577424136 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-577424136\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2077638235 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2077638235\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-32763820 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-32763820\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2035926701 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:06:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilh1T0Q4RHJkRC80NDdKRlRPUjRVaEE9PSIsInZhbHVlIjoiUkVaK1hSYlhVOGRhV1picGYyTnZ4YTJaWFZ1OXJOa0FoOHhsTEV6b3czY2J4WW5JY0lsMkRlcnltUmFKdmtQVXBFaDhHTUdkOUxTMGdoeFBDZnNWSEtGK3BXTStsaFdvbExIK2pSUGNjK2RyUk5VaEh2M0VwYm9LeWNzQ1JmbWp2SVJpSGlqTEcyM1l5TkVXVnp1bTQ1NzN4Z2xtazFVeGFmZ0tYWERXTGRkT0NGbzhGcHI3eTlUQ052OERqaThYcWVEWThyZTROQWNXSVdLN3dZVGhyejBsSkx5YnhidVJOdlBSWHNXbjI3S0RNOGg3c2JseEZ2em45RzdVeWswemtHRk4wL3poYlQ2TXdITk14VTFZQXBzVG11YmFkTENMb0NncTFYQmlUWDM5R1JiZWpsYTlRN1ZvTVRmQWRRVVdGYTMxbloycnljc0kydWtLYzVFdzZadlNIOXlzUUxwdlYzSi8ycHp2cGxMSFduQ2tpRjcyMkU4SVUxSmRON0pPWW5BNEk2T0c0WTFIcVdUNlkvVnRPakVINHBtWnE1cDNtcGJhSUJ4Y1VqdVZZSEpQajBiaE5BZGltNmR0OC9hT0plOVJieVZEUlJhVkxiL2cwMnVVNm41djFNaEJBYXg1VCtOcnZnT0NyMmthMVU5LzZ2eUlwWFlxb05VNGNIUVkiLCJtYWMiOiIwMmRmZDk4YzVjMTBkOTc1YTU4NjU2OTNlMTBmYmY0NGY4ZWIxOGJiODFiZjgyNjM2NDI1YzMzNjI2YjY4MTVmIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:06:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Im1LWXI2RlVNQU1OL29MZXpvSE10Rnc9PSIsInZhbHVlIjoiUFZuQnBSR0IySDdrTFA4eGdmTXp2NUUxc0NMVUJPdlJEay9Bb3hZNG5LTmdMSG1MSS91TmNKTlhyMm9jR01Ya3BoYVZWWUpzREY5SEJJdSszclRaR2RkTDBrTFdqVlJnZ05CbXlNOXFBTCtLK2QyOWs4bE9zZVNNTjdaVE9LYnNna1UydGFxdURVdjh1N29yT2hWZXFEdjAxd1Jvcm5oWithMmJBM1pNeUhVVGlueWZUOUlZUWlaUkRaeUxJWWdoWmt3NTFGcWRGMkw2b3pyM1kzblFrS0ovdjlLMzV5S3hMRmtzNzBRbFhQc2xUUHE3T01lTXpOamd1SjE2Y2NHWDVuU2YrMjFjNTRaVE5LWlZGWXdiNnFoZlBweDdqWm5GM0ZBV1dZWTBTcis5dDd6Mzl2TlAxTXBKaFZZSFFhSUdlYkRwVFE3cHRibUprb1NjSjZHYXJpZ1BueXZzTHAwSWhPNmdzSlZGQ3dGVHlrRWdqa2NKTUVlM3I1VXY3M0psZklHd3JjeWNDdnNYL1E3VWdYWktCNU8xQXYrRDBqbzZ1WlVxWGhyV0hsL3lqVnByWnREQWc2Zzl1am82ekQxTzRzQ1ZXRVN0b1NhUkhNdm9QdTJWVDh4Y0t5UnRiSi9VN3hnQmEreXBUcmplVGdhSUtjOUZ4Mk1qM3Q1TG5RU0EiLCJtYWMiOiIxNTMxNjkzYjY0YjVhNTQ2ZDdmMWQwY2RjOTkxOTQ3Mjk4MjRlNDc4MDEyOTBjM2RhMzNjNGIyYTI5ODg2ZTJkIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:06:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilh1T0Q4RHJkRC80NDdKRlRPUjRVaEE9PSIsInZhbHVlIjoiUkVaK1hSYlhVOGRhV1picGYyTnZ4YTJaWFZ1OXJOa0FoOHhsTEV6b3czY2J4WW5JY0lsMkRlcnltUmFKdmtQVXBFaDhHTUdkOUxTMGdoeFBDZnNWSEtGK3BXTStsaFdvbExIK2pSUGNjK2RyUk5VaEh2M0VwYm9LeWNzQ1JmbWp2SVJpSGlqTEcyM1l5TkVXVnp1bTQ1NzN4Z2xtazFVeGFmZ0tYWERXTGRkT0NGbzhGcHI3eTlUQ052OERqaThYcWVEWThyZTROQWNXSVdLN3dZVGhyejBsSkx5YnhidVJOdlBSWHNXbjI3S0RNOGg3c2JseEZ2em45RzdVeWswemtHRk4wL3poYlQ2TXdITk14VTFZQXBzVG11YmFkTENMb0NncTFYQmlUWDM5R1JiZWpsYTlRN1ZvTVRmQWRRVVdGYTMxbloycnljc0kydWtLYzVFdzZadlNIOXlzUUxwdlYzSi8ycHp2cGxMSFduQ2tpRjcyMkU4SVUxSmRON0pPWW5BNEk2T0c0WTFIcVdUNlkvVnRPakVINHBtWnE1cDNtcGJhSUJ4Y1VqdVZZSEpQajBiaE5BZGltNmR0OC9hT0plOVJieVZEUlJhVkxiL2cwMnVVNm41djFNaEJBYXg1VCtOcnZnT0NyMmthMVU5LzZ2eUlwWFlxb05VNGNIUVkiLCJtYWMiOiIwMmRmZDk4YzVjMTBkOTc1YTU4NjU2OTNlMTBmYmY0NGY4ZWIxOGJiODFiZjgyNjM2NDI1YzMzNjI2YjY4MTVmIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:06:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Im1LWXI2RlVNQU1OL29MZXpvSE10Rnc9PSIsInZhbHVlIjoiUFZuQnBSR0IySDdrTFA4eGdmTXp2NUUxc0NMVUJPdlJEay9Bb3hZNG5LTmdMSG1MSS91TmNKTlhyMm9jR01Ya3BoYVZWWUpzREY5SEJJdSszclRaR2RkTDBrTFdqVlJnZ05CbXlNOXFBTCtLK2QyOWs4bE9zZVNNTjdaVE9LYnNna1UydGFxdURVdjh1N29yT2hWZXFEdjAxd1Jvcm5oWithMmJBM1pNeUhVVGlueWZUOUlZUWlaUkRaeUxJWWdoWmt3NTFGcWRGMkw2b3pyM1kzblFrS0ovdjlLMzV5S3hMRmtzNzBRbFhQc2xUUHE3T01lTXpOamd1SjE2Y2NHWDVuU2YrMjFjNTRaVE5LWlZGWXdiNnFoZlBweDdqWm5GM0ZBV1dZWTBTcis5dDd6Mzl2TlAxTXBKaFZZSFFhSUdlYkRwVFE3cHRibUprb1NjSjZHYXJpZ1BueXZzTHAwSWhPNmdzSlZGQ3dGVHlrRWdqa2NKTUVlM3I1VXY3M0psZklHd3JjeWNDdnNYL1E3VWdYWktCNU8xQXYrRDBqbzZ1WlVxWGhyV0hsL3lqVnByWnREQWc2Zzl1am82ekQxTzRzQ1ZXRVN0b1NhUkhNdm9QdTJWVDh4Y0t5UnRiSi9VN3hnQmEreXBUcmplVGdhSUtjOUZ4Mk1qM3Q1TG5RU0EiLCJtYWMiOiIxNTMxNjkzYjY0YjVhNTQ2ZDdmMWQwY2RjOTkxOTQ3Mjk4MjRlNDc4MDEyOTBjM2RhMzNjNGIyYTI5ODg2ZTJkIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:06:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2035926701\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2004455538 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">2mWCCmO5PRVd7B0BAfaLtNgLsz1yrWZcn9cAGMfZ</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2004455538\", {\"maxDepth\":0})</script>\n"}}