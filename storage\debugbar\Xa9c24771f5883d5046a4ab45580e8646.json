{"__meta": {"id": "Xa9c24771f5883d5046a4ab45580e8646", "datetime": "2025-07-29 05:32:55", "utime": 1753767175.058798, "method": "POST", "uri": "/api/get-pipeline-stages", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[05:32:54] LOG.info: ContactController: Getting stages for pipeline {\n    \"pipeline_id\": \"28\",\n    \"user_id\": 84,\n    \"creator_id\": 84,\n    \"request_data\": {\n        \"_token\": \"pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U\",\n        \"pipeline_id\": \"28\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.900901, "xdebug_link": null, "collector": "log"}, {"message": "[05:32:54] LOG.info: ContactController: Found stages {\n    \"pipeline_id\": \"28\",\n    \"count\": 4,\n    \"stages\": [\n        {\n            \"id\": 117,\n            \"name\": \"Flow\",\n            \"order\": 0\n        },\n        {\n            \"id\": 108,\n            \"name\": \"Draft One\",\n            \"order\": 1\n        },\n        {\n            \"id\": 109,\n            \"name\": \"Send One\",\n            \"order\": 2\n        },\n        {\n            \"id\": 110,\n            \"name\": \"Follow UP One\",\n            \"order\": 3\n        }\n    ]\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.91899, "xdebug_link": null, "collector": "log"}, {"message": "[05:32:55] LOG.info: Permissions refreshed after POST request {\n    \"user_id\": 84,\n    \"user_type\": \"company\",\n    \"request_url\": \"http:\\/\\/127.0.0.1:8000\\/api\\/get-pipeline-stages\",\n    \"has_pricing_plan\": true,\n    \"has_module_permissions\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": 1753767175.050668, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753767173.570446, "end": 1753767175.05883, "duration": 1.4883840084075928, "duration_str": "1.49s", "measures": [{"label": "Booting", "start": 1753767173.570446, "relative_start": 0, "end": **********.71375, "relative_end": **********.71375, "duration": 1.1433038711547852, "duration_str": "1.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.713764, "relative_start": 1.1433179378509521, "end": 1753767175.058833, "relative_end": 2.86102294921875e-06, "duration": 0.34506893157958984, "duration_str": "345ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48183296, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/get-pipeline-stages", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@getPipelineStages", "namespace": null, "prefix": "/api", "where": [], "as": "api.get-pipeline-stages", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=143\" onclick=\"\">app/Http/Controllers/ContactController.php:143-198</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.05869, "accumulated_duration_str": "58.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.807466, "duration": 0.04879, "duration_str": "48.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 83.132}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8876061, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 83.132, "width_percent": 2.028}, {"sql": "select `id`, `name`, `order` from `lead_stages` where `pipeline_id` = '28' and `created_by` = 84 order by `order` asc, `id` asc", "type": "query", "params": [], "bindings": ["28", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 168}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.903217, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "ContactController.php:168", "source": "app/Http/Controllers/ContactController.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=168", "ajax": false, "filename": "ContactController.php", "line": "168"}, "connection": "omx_sass_systam_db", "start_percent": 85.159, "width_percent": 4.362}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.944479, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "ensure-permissions:35", "source": "middleware::ensure-permissions:35", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FMiddleware%2FEnsurePermissionsAfterPost.php&line=35", "ajax": false, "filename": "EnsurePermissionsAfterPost.php", "line": "35"}, "connection": "omx_sass_systam_db", "start_percent": 89.521, "width_percent": 1.516}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (84) and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9705122, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 91.038, "width_percent": 1.431}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (84) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.978708, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 92.469, "width_percent": 1.482}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": "middleware", "name": "ensure-permissions", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\EnsurePermissionsAfterPost.php", "line": 37}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 26, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.985997, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": "app/Models/User.php:160", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_systam_db", "start_percent": 93.951, "width_percent": 6.049}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 543, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PricingPlan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 550, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/api/get-pipeline-stages", "status_code": "<pre class=sf-dump id=sf-dump-1582222553 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1582222553\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1621164598 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1621164598\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-761707403 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>pipeline_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">28</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-761707403\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1035135037 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">62</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Imd0R2MyTjlYWlpWcFNZanE0WVMyd2c9PSIsInZhbHVlIjoiUUU3U2pIeUhRcnRTMnhpTGhMdWJCaVdMMVMyNDM0WWhsZU53K1lPaUg2L2xTdEk5eTQxVFpSUmlsQTlOQ3hqZitxOElMdFB2R1E3cjN4WWFYdnZENzFPeXdFcEN0akhxVXh4c2RhQVE4Q3ZhWXhucHBVWkpHdnFSMFZVeWpaTmlQdURUa0hrZWp6Y3Y0TUp3OEpHNVBBdnNJZjNjaVlCbEVCM01oMzYwL0NnOFIyTUdJSHFTVzZkS0cxTkZKamtGUFRMQm1HWUJOZWF0dHdrV1JJaVhzTVc3K1pHcXNuZjF3dzZZZnV5OTV2bHozb0FQbklYemJEbjlZUXdNcG9PYU9rYVg5b3daNWQwMjErRHg1RVlaQWtZMjYyNlZrVGNWQW42aVI2Vno1SjAxT3lMOXd3dEpySVNzTWNoQmwvRXJvM3pQVURnaWJNSGEwdG02NFR0c3cwM1N1ckFSYm5PQ1lOb0RibmxoNEZWSXgybXVld2ZXS0c3QVVhdEFKS01SSS9iV0Q5b0hDanJ4ei8yc2ZOSStwTzJyQXg2K2dNWlhoa20yQmJUTk1VQzkwMUR1RU1rUElCbzlHYXJzTVRtcFZERW5mOHRraC9vQ1FHWkNja2NpQWpkcldTdzlRSWI2Y01meDVxVmFHaGtNd2gwbjFIMDBZdFlkdlk2RTNhbzciLCJtYWMiOiI4MWNlMzc5MjhiM2Y0YjZmOWFlYjJlYWZmNGQ2ZGJiYjFkOTkxMmI4MzkwNDA2OGMyZDMxMWY5OGQ2NDRmOTg2IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IkdWVUIzdW1PdGd4T2ZXL0xDWXRudUE9PSIsInZhbHVlIjoiblJScExaRzFVd3lta2FabDNrUDRaQ3Y0UHQ0bmpUV2w0NHdqMzgzTjcxUko5VExxQ0tMSW00TTFONzFwRXkxV28ySzZkeFovSTlnS0tYOWs2QVZPajVzQmgrazVyTWFGVjhYWFNUNWNUVkdxSENTdlk1dCs4S0VMWE80U1NnckVOd1dpbzg1RGsrSy9CR09ETm5IOHZiZ3krTHUwMk9Jb1l1eXcrbWkzcXY0bEViRitCOUJvTTBzYlV5TXdHWmJnSENGMVNLZTBHV0c4SWpzM3J4VlB0Y09lRmtCbGs4S3JxR0FaYStrN1R1dTVtUEVBdC9hZEEwTlJsVWxDK0ZUd2d5dnEvdGtYVUowSkc1VERFb3FpVXpoMVVrc0NrbE5UOGY5L1RBc2hScUprUGFHeU4wb1RGczBPdEtDdkdEOTE4Rm5QdW9UY0VnUG9xZHFXNk5CNWdDQ05VcGw3MVlPYkRkcDdINVpuVWhweFpXUmZKRDI4TGVGeUZlN091UHFTWTYrSTRkc0dicjFiT1FSM2V6Q3BrTXNoa0FKdWFpdzgvNlpoMFE4MkVVNmJaS3pDYjJWbXEyYmV1bjc5NUh5T25KWTVJQlk3dFFBWWlRc0dkcCtxN21HM29EK1ZGNnMzZU5TQmlHWEYxMVJqWGRKRDd2R204TmV6VG9teThYajgiLCJtYWMiOiI5YTBkNDViOTQ5OWM2OWVjNWRiMTAwMzZjZDdhZjYwOGUwMTA3YTBlYjk1OTFiYjUyMTAzY2I4ZjMzYmY5NWQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1035135037\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-491111070 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-491111070\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:32:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InUyWDk5REllTlRQalkvMzNCaHA2ZWc9PSIsInZhbHVlIjoiaWRleU92dEhvKy9wd0VzOFdCTXZILzNNcFNGdE4wOHVPNkl5bGR5QVE1VklkVk9zc0xNUDhidUlIT0pqM2FjMkFTVlBqY0N2SWRxcHB0K244alhleXVPdXFPWllYM2Q1YjNpZmpYSzVoWk1pNFpWNThUR3hFU1VVbW9aRlJyS2w5STBzZU9tWFRHT2xvNmRBTURCWDVEUnJqWmFJMHJFOEZMRXZwYWxlYlN3RUlSMzZOUzhEOTZJQVpzMnRrSldhQzI5aXI3Wi94dXNVaTZ4cjJjT3ArUzFBYlozMnJxLzgwdXJxU0JUalVmSUZHT1NBdzF0Z0Y4VE1XTURBcTRETUp0dm5EeWFpVWVob3JyMlNVR2lsRndyUlFmUlAxaFZwTE5CV2JMUDhnY21JallhcFNiZjltWE1ncmtzbHBrK0l6UTd2b0VBMXhpVVZVZU52RjkyQ1RXNXlaZUhwSjlOSnVVMXIzNDZuZkYzUkJJTDErMFlKNmxNUXhFOVNOQjloNEZOU3B6cysydEQ1aXUwY3Mzak5DUFFSZHpsclZ3UTMrWlFZUTJ3SzN6S25GMGhGbTVPcVZ2YTMybldway8zY3lqT3hlejRJU29mUUt2TkM4dThmR3lJQUxnVHpKSExSUjFoMlpmQU12VE8wSG5EQ0VzMS9vU1AyY1NNRnlVdjkiLCJtYWMiOiI0ZmFiMDY1Y2MwYTIxYmU0N2RmOTdkNDllMjZhYjZlZjAxMGVkYWM2YjcyYzIyNjRmM2ExZjQ5MTcxYjU5ZjNiIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:32:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImtCa3h6OTRQc00vR05hTXU1SWJjYmc9PSIsInZhbHVlIjoibzVXb3p0NUVCYkk2NWZ0bG4zcXY2QWswZEpGd21ybUpGZHA1TDJNWnNnanFmZVFiZllrcTVCT1ZxMFJXNGp2TXg0WXdXdDZRc1BTRk5ta2tGamE4VHEwalJPcHJ1OXIyVU96M1NhZlA3dHREUlhaNjBsNnFUb0FDWXBib3E0L0lkNUhYaHdoRlpYdG1aS3h3K2Y1ZnY0UUVrMTA1OS90Zjl1V3h3dk1OWDFaK3UrTFJFM3czS0prWmpwcE1UQmFqTUp5WW5XYlVLbWREQXVTemVqekZBSFYrOXcxc1E0NGR5UHRlcGFLaWszakEyUDlpY3NmS3U3VWhqc3Y4MmdrdFhaNWMwS3hJeXJmdXJwbUh2VFJrVEVsZzF0Z2V2Um1GaVN2dnN5QURQbEhlM0ZMbGY5MkpHT2x4NnhRQ0lyYlAvaXlJZXZLK1JKb2JLK2dSV0NmeUtFNzcxemxCZlRSSTlqTERwMWo4UzZXT3FEWEVSN2NBMEVXM2ZTRkdhZWpkcGZjZk9WZzk2UVV6R0lLWnVZZDhNUlVGTFNMSnAydXl0R1RzWTlYMmp5WVFBSi9TbVlZVmdQRnh6elNob2ErbGVUSzBHemsvS3FlODFGa240VWJxSEZVK3B1NjJsTTZKYnNHbmVUbmtramlDKzJwTXhzRmg5WFREN0puamVoWmIiLCJtYWMiOiI4OWU2YjdjYmU5Y2M2Y2ZlODM3YzJjYjVmZjFhMDkzZjIyZjZlYzUyMmJlMDk2NzdhYzI5MzFlOGQwYzgwYmM0IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:32:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InUyWDk5REllTlRQalkvMzNCaHA2ZWc9PSIsInZhbHVlIjoiaWRleU92dEhvKy9wd0VzOFdCTXZILzNNcFNGdE4wOHVPNkl5bGR5QVE1VklkVk9zc0xNUDhidUlIT0pqM2FjMkFTVlBqY0N2SWRxcHB0K244alhleXVPdXFPWllYM2Q1YjNpZmpYSzVoWk1pNFpWNThUR3hFU1VVbW9aRlJyS2w5STBzZU9tWFRHT2xvNmRBTURCWDVEUnJqWmFJMHJFOEZMRXZwYWxlYlN3RUlSMzZOUzhEOTZJQVpzMnRrSldhQzI5aXI3Wi94dXNVaTZ4cjJjT3ArUzFBYlozMnJxLzgwdXJxU0JUalVmSUZHT1NBdzF0Z0Y4VE1XTURBcTRETUp0dm5EeWFpVWVob3JyMlNVR2lsRndyUlFmUlAxaFZwTE5CV2JMUDhnY21JallhcFNiZjltWE1ncmtzbHBrK0l6UTd2b0VBMXhpVVZVZU52RjkyQ1RXNXlaZUhwSjlOSnVVMXIzNDZuZkYzUkJJTDErMFlKNmxNUXhFOVNOQjloNEZOU3B6cysydEQ1aXUwY3Mzak5DUFFSZHpsclZ3UTMrWlFZUTJ3SzN6S25GMGhGbTVPcVZ2YTMybldway8zY3lqT3hlejRJU29mUUt2TkM4dThmR3lJQUxnVHpKSExSUjFoMlpmQU12VE8wSG5EQ0VzMS9vU1AyY1NNRnlVdjkiLCJtYWMiOiI0ZmFiMDY1Y2MwYTIxYmU0N2RmOTdkNDllMjZhYjZlZjAxMGVkYWM2YjcyYzIyNjRmM2ExZjQ5MTcxYjU5ZjNiIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:32:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImtCa3h6OTRQc00vR05hTXU1SWJjYmc9PSIsInZhbHVlIjoibzVXb3p0NUVCYkk2NWZ0bG4zcXY2QWswZEpGd21ybUpGZHA1TDJNWnNnanFmZVFiZllrcTVCT1ZxMFJXNGp2TXg0WXdXdDZRc1BTRk5ta2tGamE4VHEwalJPcHJ1OXIyVU96M1NhZlA3dHREUlhaNjBsNnFUb0FDWXBib3E0L0lkNUhYaHdoRlpYdG1aS3h3K2Y1ZnY0UUVrMTA1OS90Zjl1V3h3dk1OWDFaK3UrTFJFM3czS0prWmpwcE1UQmFqTUp5WW5XYlVLbWREQXVTemVqekZBSFYrOXcxc1E0NGR5UHRlcGFLaWszakEyUDlpY3NmS3U3VWhqc3Y4MmdrdFhaNWMwS3hJeXJmdXJwbUh2VFJrVEVsZzF0Z2V2Um1GaVN2dnN5QURQbEhlM0ZMbGY5MkpHT2x4NnhRQ0lyYlAvaXlJZXZLK1JKb2JLK2dSV0NmeUtFNzcxemxCZlRSSTlqTERwMWo4UzZXT3FEWEVSN2NBMEVXM2ZTRkdhZWpkcGZjZk9WZzk2UVV6R0lLWnVZZDhNUlVGTFNMSnAydXl0R1RzWTlYMmp5WVFBSi9TbVlZVmdQRnh6elNob2ErbGVUSzBHemsvS3FlODFGa240VWJxSEZVK3B1NjJsTTZKYnNHbmVUbmtramlDKzJwTXhzRmg5WFREN0puamVoWmIiLCJtYWMiOiI4OWU2YjdjYmU5Y2M2Y2ZlODM3YzJjYjVmZjFhMDkzZjIyZjZlYzUyMmJlMDk2NzdhYzI5MzFlOGQwYzgwYmM0IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:32:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1846136159 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1846136159\", {\"maxDepth\":0})</script>\n"}}