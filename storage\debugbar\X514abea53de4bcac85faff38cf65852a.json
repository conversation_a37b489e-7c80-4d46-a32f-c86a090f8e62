{"__meta": {"id": "X514abea53de4bcac85faff38cf65852a", "datetime": "2025-07-29 05:07:32", "utime": **********.500675, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753765651.696801, "end": **********.500753, "duration": 0.8039519786834717, "duration_str": "804ms", "measures": [{"label": "Booting", "start": 1753765651.696801, "relative_start": 0, "end": **********.432426, "relative_end": **********.432426, "duration": 0.7356250286102295, "duration_str": "736ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.432442, "relative_start": 0.****************, "end": **********.500758, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "68.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2997\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1801 to 1807\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1801\" onclick=\"\">routes/web.php:1801-1807</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "40ECZI6M2xiqFAByEOANdcDW1jqDZZN2nFhcJWyt", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-481102614 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-481102614\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1970753258 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1970753258\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-449168031 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-449168031\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1570927738 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1570927738\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1860988353 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1860988353\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1131228020 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:07:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVEOGt3UElodFNRbVZaeVhPUTFxWWc9PSIsInZhbHVlIjoiWU1jL243OEVsZjF5bWI5cTVwL3NnR2phc2M5UWw3ZjVMRlNrODEwVzFLUk5RbVNZcTJ2MFZLL0ZGeEw2QmZrNW42K0RMUExPU0ZRRmxtUVgya2hWWnY5MWt2YzJFVHhqN20yNlBxQXhMbnY4Sm93YklUdElrMkZmbGQ2MlFPdWlUUGYzOGlEa3BGTFB2cUhjMnhiN3ptVnN5dU5qNHdRUUNVdTdxb01DNjVVWkpXTCthUUxJbnhMRWlXU2VlZHg4SklqQUFCS2grMWJvVUJmT2ZUNWlyV2ZvdzdLcm5SNFNIbURBRk43b2k0WHA2WmhQdjAweWNjUVlMakFQU21hZWhJWk0zWGs0dVB2OERrVTNRM2FpYVh2QlN5Zi9SbjNzb0RrcDBsNk8rK2lEU1NnUWJ6RndkbU9oYiswN0I0QUMxTGdiQmYzWlBONlB2YmM3bUxTY3NJdWFlWUF4cERveUpENnZPVm90cFl6aTlWSk5XcUI4YnV0dktleFRTVW9raVRYSUhWUjlWS3E2dk1EcXBseWtpZ08rc2NsWDJIZnBwLzNlS0FnQ1hMWjJERVRMbUJpaDkwOWlNbEJSRkVCQnh2YkVBZHBaWGorZzZYeVJtTXp6blNZM3FxS2pVQmg1d0VBMkZQNG5WdVRKVkE3TmxqcGc3L0Q2a21jQTRsYkciLCJtYWMiOiIwNmZlZjM0ZjM3ODNlYTRhYjAzNzdlYjU3NzA4MzY3YjM2YzE1MGRmZThjNzQzYTMwZWFlMjcyOTA5YWYxMzE3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:07:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkJGQmRsSWtKeTlsYklacjJXOWM1TGc9PSIsInZhbHVlIjoiRHBVRWQ4SWFwSzlqcDh1UnQvVVlvRGhDd29YbmtJT2Vsa3AzL1V2RUd6cnJrZlhQVG4xSm5PWE5pNkVzK2VPWHhMYW5McCtLQ1VQRW04VW5IcE5XZ2xhK1NRMHpSU2NzZmFMODc0VlFybVMzRm5xbmcwZ1BrT3VGVEx2U1RBUlgxeW1wSFBMNWtlZUNtTW54YmJRZUU0S2pnNStBV1VnWU14elp4YnlVSnBTR3F5OHYyeG5xVG1RQVl4Vk0xelU4NFdzY21Ib3hMdHVnQTIybmF2WEF5dGZUdXlNUFN1NW9mMUlaOGNMcysrV0M4bjUwOFdLVVloOWFGZ3VBYnRkald2WnN3QmhuemFscmNFN0xCeVBrL3I5c0FGbUZSb3J1MFdFVEhmdkx5YllFRHpkbFdLakptczVCd0hUaHJqbkZCS0VDWFBQcHBFczRVeHFsUXhtUHZJSTJORHFENWhxZmJBR1FXRk1sZ3VHZ1c1T2haeWtyYzZiTnFvNnZuNU4yRmFpVlJzcGFOL0pmVnIzS3NZTGtBMnkvWWVsSlAwWFBuVE9JZ1hiTWhzekx4NUJoT21RUlRHdk0rV3BLcndlcDFoWHR1NnVwaHIzRkdEMDloaWVUcnRhR3g1YXF4aUtFZGdXR3NYOVRhRXlpUXpsN0xDazQwUnlDc0E5MUZMaGkiLCJtYWMiOiJmZjJlMTJhNWRmZTg0MGY0YzhjOWZmNmU0MGE1ODlhYjdjZDNhYmJiYjRhMGNlNjdhNjAwNThkMGI0OTliZWE3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:07:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVEOGt3UElodFNRbVZaeVhPUTFxWWc9PSIsInZhbHVlIjoiWU1jL243OEVsZjF5bWI5cTVwL3NnR2phc2M5UWw3ZjVMRlNrODEwVzFLUk5RbVNZcTJ2MFZLL0ZGeEw2QmZrNW42K0RMUExPU0ZRRmxtUVgya2hWWnY5MWt2YzJFVHhqN20yNlBxQXhMbnY4Sm93YklUdElrMkZmbGQ2MlFPdWlUUGYzOGlEa3BGTFB2cUhjMnhiN3ptVnN5dU5qNHdRUUNVdTdxb01DNjVVWkpXTCthUUxJbnhMRWlXU2VlZHg4SklqQUFCS2grMWJvVUJmT2ZUNWlyV2ZvdzdLcm5SNFNIbURBRk43b2k0WHA2WmhQdjAweWNjUVlMakFQU21hZWhJWk0zWGs0dVB2OERrVTNRM2FpYVh2QlN5Zi9SbjNzb0RrcDBsNk8rK2lEU1NnUWJ6RndkbU9oYiswN0I0QUMxTGdiQmYzWlBONlB2YmM3bUxTY3NJdWFlWUF4cERveUpENnZPVm90cFl6aTlWSk5XcUI4YnV0dktleFRTVW9raVRYSUhWUjlWS3E2dk1EcXBseWtpZ08rc2NsWDJIZnBwLzNlS0FnQ1hMWjJERVRMbUJpaDkwOWlNbEJSRkVCQnh2YkVBZHBaWGorZzZYeVJtTXp6blNZM3FxS2pVQmg1d0VBMkZQNG5WdVRKVkE3TmxqcGc3L0Q2a21jQTRsYkciLCJtYWMiOiIwNmZlZjM0ZjM3ODNlYTRhYjAzNzdlYjU3NzA4MzY3YjM2YzE1MGRmZThjNzQzYTMwZWFlMjcyOTA5YWYxMzE3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:07:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkJGQmRsSWtKeTlsYklacjJXOWM1TGc9PSIsInZhbHVlIjoiRHBVRWQ4SWFwSzlqcDh1UnQvVVlvRGhDd29YbmtJT2Vsa3AzL1V2RUd6cnJrZlhQVG4xSm5PWE5pNkVzK2VPWHhMYW5McCtLQ1VQRW04VW5IcE5XZ2xhK1NRMHpSU2NzZmFMODc0VlFybVMzRm5xbmcwZ1BrT3VGVEx2U1RBUlgxeW1wSFBMNWtlZUNtTW54YmJRZUU0S2pnNStBV1VnWU14elp4YnlVSnBTR3F5OHYyeG5xVG1RQVl4Vk0xelU4NFdzY21Ib3hMdHVnQTIybmF2WEF5dGZUdXlNUFN1NW9mMUlaOGNMcysrV0M4bjUwOFdLVVloOWFGZ3VBYnRkald2WnN3QmhuemFscmNFN0xCeVBrL3I5c0FGbUZSb3J1MFdFVEhmdkx5YllFRHpkbFdLakptczVCd0hUaHJqbkZCS0VDWFBQcHBFczRVeHFsUXhtUHZJSTJORHFENWhxZmJBR1FXRk1sZ3VHZ1c1T2haeWtyYzZiTnFvNnZuNU4yRmFpVlJzcGFOL0pmVnIzS3NZTGtBMnkvWWVsSlAwWFBuVE9JZ1hiTWhzekx4NUJoT21RUlRHdk0rV3BLcndlcDFoWHR1NnVwaHIzRkdEMDloaWVUcnRhR3g1YXF4aUtFZGdXR3NYOVRhRXlpUXpsN0xDazQwUnlDc0E5MUZMaGkiLCJtYWMiOiJmZjJlMTJhNWRmZTg0MGY0YzhjOWZmNmU0MGE1ODlhYjdjZDNhYmJiYjRhMGNlNjdhNjAwNThkMGI0OTliZWE3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:07:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1131228020\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-809728023 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">40ECZI6M2xiqFAByEOANdcDW1jqDZZN2nFhcJWyt</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-809728023\", {\"maxDepth\":0})</script>\n"}}