{"__meta": {"id": "Xef89e68c64ba57de666f1414d0f05b46", "datetime": "2025-07-29 05:39:12", "utime": **********.241395, "method": "GET", "uri": "/api/leads/17", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753767551.237104, "end": **********.241412, "duration": 1.0043079853057861, "duration_str": "1s", "measures": [{"label": "Booting", "start": 1753767551.237104, "relative_start": 0, "end": **********.105106, "relative_end": **********.105106, "duration": 0.86800217628479, "duration_str": "868ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.105152, "relative_start": 0.8680479526519775, "end": **********.241414, "relative_end": 2.1457672119140625e-06, "duration": 0.1362621784210205, "duration_str": "136ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46097448, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/leads/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@getLeadForPreview", "namespace": null, "prefix": "", "where": [], "as": "api.leads.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=440\" onclick=\"\">app/Http/Controllers/ContactController.php:440-476</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02171, "accumulated_duration_str": "21.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.15596, "duration": 0.01594, "duration_str": "15.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 73.422}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.191306, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 73.422, "width_percent": 5.573}, {"sql": "select * from `leads` where `id` = '17' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["17", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 446}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1968012, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:446", "source": "app/Http/Controllers/ContactController.php:446", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=446", "ajax": false, "filename": "ContactController.php", "line": "446"}, "connection": "omx_sass_systam_db", "start_percent": 78.996, "width_percent": 3.731}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` in (118)", "type": "query", "params": [], "bindings": ["118"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 446}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.212684, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "ContactController.php:446", "source": "app/Http/Controllers/ContactController.php:446", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=446", "ajax": false, "filename": "ContactController.php", "line": "446"}, "connection": "omx_sass_systam_db", "start_percent": 82.727, "width_percent": 10.456}, {"sql": "select * from `pipelines` where `pipelines`.`id` in (30)", "type": "query", "params": [], "bindings": ["30"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 446}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.219251, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:446", "source": "app/Http/Controllers/ContactController.php:446", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=446", "ajax": false, "filename": "ContactController.php", "line": "446"}, "connection": "omx_sass_systam_db", "start_percent": 93.183, "width_percent": 3.547}, {"sql": "select * from `labels` where `id` in ('94')", "type": "query", "params": [], "bindings": ["94"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Lead.php", "line": 54}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 462}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.225663, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Lead.php:54", "source": "app/Models/Lead.php:54", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=54", "ajax": false, "filename": "Lead.php", "line": "54"}, "connection": "omx_sass_systam_db", "start_percent": 96.73, "width_percent": 3.27}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\LeadStage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}, "App\\Models\\Label": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLabel.php&line=1", "ajax": false, "filename": "Label.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contacts\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/api/leads/17", "status_code": "<pre class=sf-dump id=sf-dump-1232753411 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1232753411\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-213130272 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-213130272\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1373359157 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1373359157\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1058265689 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IktJY2pzdGFsVzBwYXg1RGd2RVNrNFE9PSIsInZhbHVlIjoiWnkxcjY1UWw5TktvRis4ZVhQSEhPSG05TWMrNUF2VlNacXlLek5SMEhIalNYdFVVLzQrZlpIelBUUjRHSGpCWTc1SDZvaTlGTFlXalZ0QmkzSEE0ZUIrb3MvRlNoRGp6N2dOVGF0bWRaTm9vUlpwWFl0V25TczJmQVV0SUM1bEhmeHJoVWdCR0t1Q1JLNHZvVnBxMmhENGR0M3VqT3M2RWtYVnExZ3BVU1BxNUpTOUJDWEMzNk4rR0hMK2hJbWJzZHdoNG5FeUFtUzBJOVdsU0RScDJmQjg1MC9DV1RVb2VLVThiRzVxTXkxMFNHdzFRRk1qcWNqMmFnaHMvSVdIV0xqeEMwRFh4WktmZXBBOFBjTS9SOUllcS9Fb1NOcWVMS3IvTHpBTDNqZ1k3ZVB0UDZmQ0p4eHFuR1VqbGRUYi8vRUM4bjNETGhBdisxTUZKUkNKa2RyL2tkNURnNThCVlFKaVVRYnVIRTBmMWYvY1BrUjJ4RkVYL2s2dFV5ajdqRUdhSVpodkNzWWRiNXlXaEJzT29jeTYyMXJYRkJTL2VleHM4YUU2UldVTzVIVHZWZGo4K3lhTW5wOXdybnplZ2FqTWhHTmJoek9NcHdRQ2dLVm1FbG1DSHRLR1NyU2NqclR2cU1IQ05rcDFnRi9ESVZwcjE2ajZvSG1va2k0UmMiLCJtYWMiOiIwYmJiMGQzN2RiZDRhOGM1NTBmYTBjN2IzZDc3MmFjNjRkZDRhYTJjMDhhNzQ1NWE0NzRmMTY3NjVjNzFjNDcwIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6ImhvSmxjYU9vbGp0V0R3LzlSaTV4TlE9PSIsInZhbHVlIjoiMnlETUMwZy9yZzlBNytoK1M3dkFyYU05MW9WaEJQL0swSGJ3NkpZMjdxUUVMNUdqMWQ3K2NlQ1FLL3RYYzV2V2hIRjIwQjEzZU5jTGE2T01seTBocUxEdzdiUEsxRUhlbzNIVDhMclJZb3BQZjVNc04rUGZXSmJDemNRbmtFVlY4MFJ3aFdWZTJmVzNhN3M0OUZCaWxJbWp1MG5oWk01anFBU3Ixd1dwWVM2RVUzZFM0QmVVY3Vra2w4QTZLem9pelVTeGtXeHZvZGxrTU9GVGNDdm95MFBSblFqVHZsZkwra2tUV0VSOWVtZmZndDBNelJMRUdGQVU5NDhqOHJGZ21US0V0c01iL245M3grMnlycy9MSTdkM0llRWdEY2lJTnA2d0NqbUlsTWVlVkV6R29oM2NLM2hZRmhXNW53c2pwektEWEMwdER4dW5VcXJ3NVF5NnM2bS9wWEdyQkl0My9kRW12OCtWTWU2VlhpaU5MRldLL2xpOW1OenVvSUkrQWJuZk83Zm0xaUk5S1d4bVlVTS9TdnAweGZsTXZlN0drR3ZJRStud0RCdTd0YWFiZjRYOXE5TzBFd1VLQ2F1M0tNa2cvL1dFMVR5OUVXVm5aSFFyeFlDWUkzbktIUzBZRGo2YUZpc0gxS2FuUEtuMmpKNUEvNzUxOFBrSWhiQ24iLCJtYWMiOiI5ZTkwZjJiM2Q2ZTU2OGJmOTRhNmU0OTU5M2RlNzE2YTMyMjhiNDI2YzYzNWExZjE2NmE5Mzg4NjVhMzQyZmMwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1058265689\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2024726964 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2024726964\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1939816209 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:39:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklZY0Q1dzVIZUhmMThUdnpNM3BFQmc9PSIsInZhbHVlIjoieWZKb1VxcVFob3c2MWlqclN4SEZUWU45aVZ6elM5SGJNY3hIbFNNNWF6KzdXa0hjUkVTdWNCKzQ0aFFqaGo0a1U1N29TMFpTM1FtZFZuVklmbmZidi9xeTlmbHlBdXVVb0FZOW5oZllpMzB2eVFsWU0rZDRCZGJNNmZyeXVtSFBaQnRHUlZtalFsLzRCTkVNTnMyWDZUVWIwWHYvUW82MEVtczlWMlpXM0dLeUtNMmlaV0t3TmdaZ0E3eTRpRFNUcHVGalBxRUo0WDhPWnd5c2lBUjAxWWwrZ21ER0hacCtnSHVvdGJlRTV3ditycFVETkpQMWx0eU5icFpGOTB6WlVrQi96cG40SFROOXlmaVdzMzA2dW1RZ0dMYy9TZE9KaTQ5TjR6YS84L1dFaGk2YkJ1ZlViZjJsaTRBUE1lUlo4YWpnNlJYTS8wc0wxaHpFc3Q0aDFBTHBHTWd3M1BpTFpMSjJ3c3h5VllXT0lwRTA1WjVaUzNRWUdWOExHWE9pMlpTajhnRXJaKzZVNUxkNmZGam1WcUN5ajJaRHl1UnN6NUROVnlocGJTbzAvVE8rbEIrRWlRcnlKOTZvdDJlVjl0NGpKdlRreW9YVWZHbmJYTVdRQytDdnczc1pBMUkwc09GclZnRFd4UUFiRWhPM3BEZFBmdXhCLzlKRlRsRFoiLCJtYWMiOiIzZjI1M2QyNzJhMDVjMjEyMjVmOWY3OGI0ZGRjZjM4M2VlNjAxNjMxMGJjMjNhYWE4Yzg5MjNmZDcyMWMxYjI5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:39:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImhLQVI3NU16UEQ5UlVORFFGQXJUdmc9PSIsInZhbHVlIjoiZm1hdysxN2JsMzJMVUNOaVlVN2lpZ0gya0FlYUNKdy84cWpZakNpN2k4VjlKa3lxdENRek9zMlE0TysyKzlOQlhoZ2RrM0tTNEZnNklkS3ZlWWFYQVVPZXNvOHNuYW51cUdWUVY1QTZwRFMzVHRkbGE4c3hXK1E3WGtoMnV2ckxoZ21qSjkveXJzRmgwSmM3QmJVRGVpd1NVZ3ZoOW9rMEkxdDhIN3RsU3pyS1JhNjNCU2tIUmkrMVpYazNsU1VqczFLRFpmRW5JWEk4V21HRmNxSHppODc5VFZjL2lobWhFbWtMSXo5TENUWmJYRG9ESzc1RlovaC8wTEdsbTFGWmtadlR0R0p6R0ZqeW96czNQTWhubjcxZzE1R25IQTFxYUZlcXF5M0tXQVE3TVIzZVVBSUlFdEQzZi9iSk14eFd6aG5FQ2JocWltOVFCcnFlY29DL1F6NnNEL3R4Nk1ObHN3Uzl5aUZ5eFNKUmNaQ09TVkk1eDZOS3VHcml4RGVPYjNWSUprYlFFK0poUERJRjNZSWhNZDdHdVRJZTJsdnlGQ3BIVUdUbXB2RVk0UnNkcUlGS1dxeDBXeVhWVjBJZFJyWUVidUJ6MWNlMDlQMHlBaktHMTJLVVVUQ3o5WWt2RTVjRmkrdEp2QUptTEpVZ2NqeGVXY09YaERHeUlnVkoiLCJtYWMiOiJhNzE1ODk3OWZjYmRmZmEzZDk2MzMxOGNhZmNjZmU4YTUyZjU2NTg4ZGViOTIwMjQ2NTgxNDlmZmE2Yjg1Yjc3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:39:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklZY0Q1dzVIZUhmMThUdnpNM3BFQmc9PSIsInZhbHVlIjoieWZKb1VxcVFob3c2MWlqclN4SEZUWU45aVZ6elM5SGJNY3hIbFNNNWF6KzdXa0hjUkVTdWNCKzQ0aFFqaGo0a1U1N29TMFpTM1FtZFZuVklmbmZidi9xeTlmbHlBdXVVb0FZOW5oZllpMzB2eVFsWU0rZDRCZGJNNmZyeXVtSFBaQnRHUlZtalFsLzRCTkVNTnMyWDZUVWIwWHYvUW82MEVtczlWMlpXM0dLeUtNMmlaV0t3TmdaZ0E3eTRpRFNUcHVGalBxRUo0WDhPWnd5c2lBUjAxWWwrZ21ER0hacCtnSHVvdGJlRTV3ditycFVETkpQMWx0eU5icFpGOTB6WlVrQi96cG40SFROOXlmaVdzMzA2dW1RZ0dMYy9TZE9KaTQ5TjR6YS84L1dFaGk2YkJ1ZlViZjJsaTRBUE1lUlo4YWpnNlJYTS8wc0wxaHpFc3Q0aDFBTHBHTWd3M1BpTFpMSjJ3c3h5VllXT0lwRTA1WjVaUzNRWUdWOExHWE9pMlpTajhnRXJaKzZVNUxkNmZGam1WcUN5ajJaRHl1UnN6NUROVnlocGJTbzAvVE8rbEIrRWlRcnlKOTZvdDJlVjl0NGpKdlRreW9YVWZHbmJYTVdRQytDdnczc1pBMUkwc09GclZnRFd4UUFiRWhPM3BEZFBmdXhCLzlKRlRsRFoiLCJtYWMiOiIzZjI1M2QyNzJhMDVjMjEyMjVmOWY3OGI0ZGRjZjM4M2VlNjAxNjMxMGJjMjNhYWE4Yzg5MjNmZDcyMWMxYjI5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:39:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImhLQVI3NU16UEQ5UlVORFFGQXJUdmc9PSIsInZhbHVlIjoiZm1hdysxN2JsMzJMVUNOaVlVN2lpZ0gya0FlYUNKdy84cWpZakNpN2k4VjlKa3lxdENRek9zMlE0TysyKzlOQlhoZ2RrM0tTNEZnNklkS3ZlWWFYQVVPZXNvOHNuYW51cUdWUVY1QTZwRFMzVHRkbGE4c3hXK1E3WGtoMnV2ckxoZ21qSjkveXJzRmgwSmM3QmJVRGVpd1NVZ3ZoOW9rMEkxdDhIN3RsU3pyS1JhNjNCU2tIUmkrMVpYazNsU1VqczFLRFpmRW5JWEk4V21HRmNxSHppODc5VFZjL2lobWhFbWtMSXo5TENUWmJYRG9ESzc1RlovaC8wTEdsbTFGWmtadlR0R0p6R0ZqeW96czNQTWhubjcxZzE1R25IQTFxYUZlcXF5M0tXQVE3TVIzZVVBSUlFdEQzZi9iSk14eFd6aG5FQ2JocWltOVFCcnFlY29DL1F6NnNEL3R4Nk1ObHN3Uzl5aUZ5eFNKUmNaQ09TVkk1eDZOS3VHcml4RGVPYjNWSUprYlFFK0poUERJRjNZSWhNZDdHdVRJZTJsdnlGQ3BIVUdUbXB2RVk0UnNkcUlGS1dxeDBXeVhWVjBJZFJyWUVidUJ6MWNlMDlQMHlBaktHMTJLVVVUQ3o5WWt2RTVjRmkrdEp2QUptTEpVZ2NqeGVXY09YaERHeUlnVkoiLCJtYWMiOiJhNzE1ODk3OWZjYmRmZmEzZDk2MzMxOGNhZmNjZmU4YTUyZjU2NTg4ZGViOTIwMjQ2NTgxNDlmZmE2Yjg1Yjc3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:39:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1939816209\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1312923385 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1312923385\", {\"maxDepth\":0})</script>\n"}}