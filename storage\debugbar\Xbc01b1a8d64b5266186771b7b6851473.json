{"__meta": {"id": "Xbc01b1a8d64b5266186771b7b6851473", "datetime": "2025-07-29 05:15:04", "utime": **********.236096, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753766103.04051, "end": **********.236141, "duration": 1.1956310272216797, "duration_str": "1.2s", "measures": [{"label": "Booting", "start": 1753766103.04051, "relative_start": 0, "end": **********.153384, "relative_end": **********.153384, "duration": 1.1128740310668945, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.153396, "relative_start": 1.****************, "end": **********.236158, "relative_end": 1.6927719116210938e-05, "duration": 0.*****************, "duration_str": "82.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2997\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1801 to 1807\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1801\" onclick=\"\">routes/web.php:1801-1807</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "WFoE0a5AHy5jAasihGVgAAQqNoweCFWdD8oxmswF", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1717398829 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1717398829\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1736013128 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1736013128\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-929260421 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-929260421\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1616805504 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1616805504\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1011461458 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1011461458\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:15:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndhTi9xYis1K2pJTHJEY2trcG94NkE9PSIsInZhbHVlIjoiVzkwRGlYVHh5ckRZVmNXRlk3Nk9MeXVFTWJTc003dUI4SHovanpPRWt6WlVaaHhlK3NDWkhYTWt0Q3Vkb2xUSFRtd1RPVDQ0VFpUYmNOU0xKdmpYWkYwYTF0U1JVME5Xek9EcXVDQ1l2Y3FUZlowbUxBVitGNEJFWkhoYmtVMEQ2a2s1Nk5rRm5xd3dwK1YySFlIOVFWZWgzQnRIWEZRM1U5TXIxR2YwYkw3OG1zcUFodTVFK0pmY1hIb0R5cUZoSEVmUmFGT1h2ZUNwaCtHcnFFeGhObWJuY0lObGxFTWlLcnc4M1R1OTRvT1FndXhWMU1heTFoY1o2cnozckxFbllrZjQreXpudGdod3Zhbm1IZnpyajNpcTJwaUpqdHoxWkFtbVVRSUwzYmFnR0drTktvSi91d1daMXJNTE9vNU0xd29MM04zR3UxWTZURmdlMS80SkZKeUZyZklKeW1PdjMzaHRLcFkyMU5ndGZhS0tLaUpjVm1FV0VZSzJ3SzVUSHI4VFh0SDhxWnBucVpnMUdTVGZnNDRxTm9PTkNVdWx4eEU2REVKczFzZkhROVNhVFFMMHkrTGNMRFlNa2RXK282R0pvcWV6NnEyb2hyVGpUNWk5KzNHRkh2UDdadFowbWJlcjNtUjRUclR0c3lOUnpNbzFKSWNmZnpGQW9vNlciLCJtYWMiOiJkODQ0MTgyYTA0NjBjNDE4NDU1MzVjNGI1MDQ2OTczNDQ3NGE2ZThiY2MyYjNkMWNiYmZlMjc2MjRhMWFjOGRlIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:15:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjBMNndLN3BTOTlpSDNQVXlBeUNsamc9PSIsInZhbHVlIjoiNG11UHdzZVozZW16YzQ2RHBQVlVkNWlFemZSRk9CMHpaUytCSG9pQngxcTRoVUp2Z1hVZzV4aDRKTlJPZjVBVHNWQmxITkpzWXVoM05QY3F4dEgwRmRnV0RuN2RKRW5rdXIzMjhJUUgxMHQvNnFJTFZ4MUdoT2pFYkNkWUkxOFZ6ampjdDRZZGpaNC90cE9BL2luZmw5YlIyVVBqS2FwbnoyVTU4VkJPZ1d4V29QclcwbFFWYVpwWTRqa0tWSFFLRi91SEpXSmtZUklSZzVIaWRiSEQrbDF2UHFFVXpGV0g1SXkxSUFORGkxbjlPMHYyNnBkc2VHbWQzNC9kdVpINmJkdEJJOGxxYmhtMVhLTklRcnlPQ01wd0xjcGltK2lRREhua2dnU0JJZGlhd1JibHJDVnRHUHdrZlV3TTZ0bTJwRnM4cVNmZHpaOWp3MkVwRUxWUzgzY0JsL0ZLMHBXbE1saGc5T1dXVmJIbE5NNDRHbUFjbkw1a3NPazd6dm04TURpenUwSkcyTUFSOWt2OHVzMjdDMUJiRW90MkRieHI5c2tUUmtNbmlmcFpNWjFldVYzVXlXaE5vS056U2F0MTdZVGJHclArYW9ES05PMnV1cSt6RTJSN1diazFUU3ZHOUZ4WEFzSStVdXptYkI4dGExV2k1eHBQQWZWSjMrNksiLCJtYWMiOiJlODU3M2I3ZGIxNmIwMzhiNDEwMTFjMmRhNTliNjcyZDQzOGFlZGI2YWE5MGE2YWIzZDY3YmY2YmM0NjRkMmYwIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:15:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndhTi9xYis1K2pJTHJEY2trcG94NkE9PSIsInZhbHVlIjoiVzkwRGlYVHh5ckRZVmNXRlk3Nk9MeXVFTWJTc003dUI4SHovanpPRWt6WlVaaHhlK3NDWkhYTWt0Q3Vkb2xUSFRtd1RPVDQ0VFpUYmNOU0xKdmpYWkYwYTF0U1JVME5Xek9EcXVDQ1l2Y3FUZlowbUxBVitGNEJFWkhoYmtVMEQ2a2s1Nk5rRm5xd3dwK1YySFlIOVFWZWgzQnRIWEZRM1U5TXIxR2YwYkw3OG1zcUFodTVFK0pmY1hIb0R5cUZoSEVmUmFGT1h2ZUNwaCtHcnFFeGhObWJuY0lObGxFTWlLcnc4M1R1OTRvT1FndXhWMU1heTFoY1o2cnozckxFbllrZjQreXpudGdod3Zhbm1IZnpyajNpcTJwaUpqdHoxWkFtbVVRSUwzYmFnR0drTktvSi91d1daMXJNTE9vNU0xd29MM04zR3UxWTZURmdlMS80SkZKeUZyZklKeW1PdjMzaHRLcFkyMU5ndGZhS0tLaUpjVm1FV0VZSzJ3SzVUSHI4VFh0SDhxWnBucVpnMUdTVGZnNDRxTm9PTkNVdWx4eEU2REVKczFzZkhROVNhVFFMMHkrTGNMRFlNa2RXK282R0pvcWV6NnEyb2hyVGpUNWk5KzNHRkh2UDdadFowbWJlcjNtUjRUclR0c3lOUnpNbzFKSWNmZnpGQW9vNlciLCJtYWMiOiJkODQ0MTgyYTA0NjBjNDE4NDU1MzVjNGI1MDQ2OTczNDQ3NGE2ZThiY2MyYjNkMWNiYmZlMjc2MjRhMWFjOGRlIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:15:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjBMNndLN3BTOTlpSDNQVXlBeUNsamc9PSIsInZhbHVlIjoiNG11UHdzZVozZW16YzQ2RHBQVlVkNWlFemZSRk9CMHpaUytCSG9pQngxcTRoVUp2Z1hVZzV4aDRKTlJPZjVBVHNWQmxITkpzWXVoM05QY3F4dEgwRmRnV0RuN2RKRW5rdXIzMjhJUUgxMHQvNnFJTFZ4MUdoT2pFYkNkWUkxOFZ6ampjdDRZZGpaNC90cE9BL2luZmw5YlIyVVBqS2FwbnoyVTU4VkJPZ1d4V29QclcwbFFWYVpwWTRqa0tWSFFLRi91SEpXSmtZUklSZzVIaWRiSEQrbDF2UHFFVXpGV0g1SXkxSUFORGkxbjlPMHYyNnBkc2VHbWQzNC9kdVpINmJkdEJJOGxxYmhtMVhLTklRcnlPQ01wd0xjcGltK2lRREhua2dnU0JJZGlhd1JibHJDVnRHUHdrZlV3TTZ0bTJwRnM4cVNmZHpaOWp3MkVwRUxWUzgzY0JsL0ZLMHBXbE1saGc5T1dXVmJIbE5NNDRHbUFjbkw1a3NPazd6dm04TURpenUwSkcyTUFSOWt2OHVzMjdDMUJiRW90MkRieHI5c2tUUmtNbmlmcFpNWjFldVYzVXlXaE5vS056U2F0MTdZVGJHclArYW9ES05PMnV1cSt6RTJSN1diazFUU3ZHOUZ4WEFzSStVdXptYkI4dGExV2k1eHBQQWZWSjMrNksiLCJtYWMiOiJlODU3M2I3ZGIxNmIwMzhiNDEwMTFjMmRhNTliNjcyZDQzOGFlZGI2YWE5MGE2YWIzZDY3YmY2YmM0NjRkMmYwIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:15:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1865590546 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">WFoE0a5AHy5jAasihGVgAAQqNoweCFWdD8oxmswF</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1865590546\", {\"maxDepth\":0})</script>\n"}}