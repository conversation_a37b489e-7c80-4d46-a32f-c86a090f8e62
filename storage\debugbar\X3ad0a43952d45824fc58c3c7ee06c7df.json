{"__meta": {"id": "X3ad0a43952d45824fc58c3c7ee06c7df", "datetime": "2025-07-29 05:43:11", "utime": **********.497913, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753767790.694172, "end": **********.497956, "duration": 0.8037841320037842, "duration_str": "804ms", "measures": [{"label": "Booting", "start": 1753767790.694172, "relative_start": 0, "end": **********.436306, "relative_end": **********.436306, "duration": 0.7421340942382812, "duration_str": "742ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.436318, "relative_start": 0.****************, "end": **********.497966, "relative_end": 1.0013580322265625e-05, "duration": 0.*****************, "duration_str": "61.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9uUlhOM5A5ptqcIf3QLf3hQabcytGY5aipihQVJv", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1212041246 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1212041246\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-9189285 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-9189285\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1245188718 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1245188718\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-369796999 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-369796999\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-786417998 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-786417998\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1641083189 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:43:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlXclk1bVJlUlBJemYyQzR2azBlWHc9PSIsInZhbHVlIjoiYjlhN0FnQVNoSTBEN1h0dS9uUi8rOXJ5dFRkOWNJQlBpZDNEZ0U1RVE3VVliYTY3aFdBbU93S1FKZmdGc25pSVFpRlFsYk83OGx5R2J1M0lQM3NSdzlXclZCU0NmSnpKeGVyc2dOa1NNYTJJaWR6YTJsWU1GYUd5Z2xuQzlyNEJjN09NekUvRk5ocWx6QXhUUmRTa2VSQzgycnpicUc1LzZYNXZna1ovanJtOGZVeGZsczZiTitYKzYxRzBacjRhTzlGbzVQemZ6Q0d5ajlpckZKU3Nkd3BWTWFKVE9wRFlVVldkVDFEMWhVQUlCWGZhOHVkNWE4UWUyZmZ2VnZLME9qUG9xcWtZTFEra1dXaG96dWRUdDdSb1M3RUFvY3dIbVVvcjArdmtDMG9pdFN4elBxZHhsbm5iMFhvK3NXVEpha2NGY3hWcnpsaitZY0FWNDN3MzI4SzhTQ2NkZ0U2a3lHNXlLY3dFVllMRm5YYzN4Rk9pRjJ0VUhnRSsycCtzTFk3Q0c2N3dNbFlDcC9LWjNmQzJtZ3BjeER5L0ExNFhPaUgvNXU0c0ViSkphNkZ3aEtmSjRDMURtNUxvakJjYy9jSU9MaVJyNm1KU3piTjBLOEduejNqREk2bGtGTnJTTWRKRjV3U2ZhVi95VkI1YVVuSXV6QXlETHN2VUEvQWsiLCJtYWMiOiJlMzAzMDhjZDQ1NTU3MmU3YWVhYzI3MzY0OWVjZTMzMDA2NGMwNTQ5NzdlMjQzNzM5NmNjMGNiNmJlNmNmODZlIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:43:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IlcyWnJ5YlBDTmtQNklFVEQxWHhzVWc9PSIsInZhbHVlIjoiSm1xU3FSOGRvQmVTNEJ1QnRGWnFydy85emUzUU00L20wMWxsWGhFZ0g0RjJiZXNxWG5EOW8xOERncjVxSVFlYndnZml6bDJvUGxIZXdTZDVNckxuSHJPNzNnaGFvOVpVM3AraGlvSUVqY3ArcThWNWlVTGhORVJDZ2xPRmVLeUhWOVJPNnd6YzhqUWxwcFV3VlljS1ZxYjNLVGMxQVZDMml2UjU3TUdvNVg0T1paVWREREFuLzZ2N0RMd0ttdnJ1RktvckxEVDRnYlhmMzErNGtZaDFaTGZ1Q0hFanlNR1BvbmxkVllTUm5aZmtnWU9zYWgySXNLQ0hiUlQvWDJFVm5zVVRQS3RoMTFwT1d2Qi9uRUxHb3d2UlR3SHB6cTB3RExoVlE1NjEzWTdLN1lpVThxSXZTMzJwVGVzWjlkUzFTc2VPd1BwY0VhdFlJUXBZYU9KalFWV2ZocU1EYURjbTBKT3VkWGtHcHFxT05DUkdzZk9zUzVJcXJoSlIrQW01bUdFMmwyZEpSVkxQaVBTRW5jbnZyT2JvZDQyaDFKaWM2YUhGSURySE1wY3grK0dRanN3WFJCZVdJQ3loeDJJUEtZazFzU1dsUnlMSFlzTTd4V0pNQlpQV2dQK25kSXgwK2hZajdTTkRQcUJTYXhxS1RuUUFibGFhMGlzeWpqcDciLCJtYWMiOiI3ZGRkY2ViNjQyYzRiYTRlZDdjNzM0NTFkOTAwYjI4ODkyMjY3YmVmNGM5YTVjZTk2NjNkODNmOGIwZjI3YTYyIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:43:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlXclk1bVJlUlBJemYyQzR2azBlWHc9PSIsInZhbHVlIjoiYjlhN0FnQVNoSTBEN1h0dS9uUi8rOXJ5dFRkOWNJQlBpZDNEZ0U1RVE3VVliYTY3aFdBbU93S1FKZmdGc25pSVFpRlFsYk83OGx5R2J1M0lQM3NSdzlXclZCU0NmSnpKeGVyc2dOa1NNYTJJaWR6YTJsWU1GYUd5Z2xuQzlyNEJjN09NekUvRk5ocWx6QXhUUmRTa2VSQzgycnpicUc1LzZYNXZna1ovanJtOGZVeGZsczZiTitYKzYxRzBacjRhTzlGbzVQemZ6Q0d5ajlpckZKU3Nkd3BWTWFKVE9wRFlVVldkVDFEMWhVQUlCWGZhOHVkNWE4UWUyZmZ2VnZLME9qUG9xcWtZTFEra1dXaG96dWRUdDdSb1M3RUFvY3dIbVVvcjArdmtDMG9pdFN4elBxZHhsbm5iMFhvK3NXVEpha2NGY3hWcnpsaitZY0FWNDN3MzI4SzhTQ2NkZ0U2a3lHNXlLY3dFVllMRm5YYzN4Rk9pRjJ0VUhnRSsycCtzTFk3Q0c2N3dNbFlDcC9LWjNmQzJtZ3BjeER5L0ExNFhPaUgvNXU0c0ViSkphNkZ3aEtmSjRDMURtNUxvakJjYy9jSU9MaVJyNm1KU3piTjBLOEduejNqREk2bGtGTnJTTWRKRjV3U2ZhVi95VkI1YVVuSXV6QXlETHN2VUEvQWsiLCJtYWMiOiJlMzAzMDhjZDQ1NTU3MmU3YWVhYzI3MzY0OWVjZTMzMDA2NGMwNTQ5NzdlMjQzNzM5NmNjMGNiNmJlNmNmODZlIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:43:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IlcyWnJ5YlBDTmtQNklFVEQxWHhzVWc9PSIsInZhbHVlIjoiSm1xU3FSOGRvQmVTNEJ1QnRGWnFydy85emUzUU00L20wMWxsWGhFZ0g0RjJiZXNxWG5EOW8xOERncjVxSVFlYndnZml6bDJvUGxIZXdTZDVNckxuSHJPNzNnaGFvOVpVM3AraGlvSUVqY3ArcThWNWlVTGhORVJDZ2xPRmVLeUhWOVJPNnd6YzhqUWxwcFV3VlljS1ZxYjNLVGMxQVZDMml2UjU3TUdvNVg0T1paVWREREFuLzZ2N0RMd0ttdnJ1RktvckxEVDRnYlhmMzErNGtZaDFaTGZ1Q0hFanlNR1BvbmxkVllTUm5aZmtnWU9zYWgySXNLQ0hiUlQvWDJFVm5zVVRQS3RoMTFwT1d2Qi9uRUxHb3d2UlR3SHB6cTB3RExoVlE1NjEzWTdLN1lpVThxSXZTMzJwVGVzWjlkUzFTc2VPd1BwY0VhdFlJUXBZYU9KalFWV2ZocU1EYURjbTBKT3VkWGtHcHFxT05DUkdzZk9zUzVJcXJoSlIrQW01bUdFMmwyZEpSVkxQaVBTRW5jbnZyT2JvZDQyaDFKaWM2YUhGSURySE1wY3grK0dRanN3WFJCZVdJQ3loeDJJUEtZazFzU1dsUnlMSFlzTTd4V0pNQlpQV2dQK25kSXgwK2hZajdTTkRQcUJTYXhxS1RuUUFibGFhMGlzeWpqcDciLCJtYWMiOiI3ZGRkY2ViNjQyYzRiYTRlZDdjNzM0NTFkOTAwYjI4ODkyMjY3YmVmNGM5YTVjZTk2NjNkODNmOGIwZjI3YTYyIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:43:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1641083189\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1390188836 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9uUlhOM5A5ptqcIf3QLf3hQabcytGY5aipihQVJv</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1390188836\", {\"maxDepth\":0})</script>\n"}}