{"__meta": {"id": "Xbba592b799aa03fd73ab6df644be284e", "datetime": "2025-07-29 04:51:48", "utime": **********.102516, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753764706.102006, "end": **********.102572, "duration": 2.000566005706787, "duration_str": "2s", "measures": [{"label": "Booting", "start": 1753764706.102006, "relative_start": 0, "end": 1753764707.891314, "relative_end": 1753764707.891314, "duration": 1.7893080711364746, "duration_str": "1.79s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753764707.89135, "relative_start": 1.***************, "end": **********.10258, "relative_end": 8.106231689453125e-06, "duration": 0.*****************, "duration_str": "211ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2997\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1801 to 1807\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1801\" onclick=\"\">routes/web.php:1801-1807</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "n3EIZeDwUEp34hWI7nXk4AAAAWtQcamHSS2zNw2v", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-991281677 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-991281677\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-700485171 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-700485171\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-351596926 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-351596926\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-384074004 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-384074004\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1252671408 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1252671408\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1660552003 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 04:51:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZJNDBDRjdlVFFRTXpXd2U2MWpDU0E9PSIsInZhbHVlIjoiVEN2VFpqNHNKdG56d1RhL1U0Rjlya2lBdTBjcUEvaGRJM1A5TkVVM1ZqbzRwcnJyZ3RLRXJJaFVlN2xlQ3crUUtid0lmeVJKTzZnRnAra09qb3NLYTk4TFJtcytJSEpJb2VQZ0ZtR2F0NjRVS010OEYzSnJsb2hrUjFqVVFKWFZvcWNtb0VTbjZCc0RUWUduOWhqTDJzWTQ1c1ZGTmNwWWE0RWVDTmpOdUJnZFdIaUZGRVlBSGhpYUxmUUdQS2xua2xwUlIxMWl6N3AzamFGakY0TUpXSEZsdXlhRzhpQUxjN1B4U1QvSExtdVcvcTkrdU94b3Z0V2I0aVhKUUMwVXJRMGNNMUJwbUtPZVhnWlFUZm5EU3BaVXZGMU9ZUUExeEZFd2c5YjBZMUcrc2FmRldxcWtQelZKT3AxMDdleXdJa1NaRGF1alF1S0hYcHpGamhIbFFINkRZZE14UDRYTE1seWR4T01xT1FRM0Fkcm1ZK29kVGRJdWtGYU15V3hoUTFMclNCbHNYU3l5c1Nzd3J6dmxkQy9KVHRvcVJCbVhSTk5aL05OVnUvWG9oQmZmYUR6OTd1aW9DcG9HVm5PTWlGbkplT3E1ZE1US1kvb0ZDdjU5S3AzcUplejluUk02VFo1NmU0VWV5VFpNazVTZG1DcS9KUFVoS28rZkVtVFEiLCJtYWMiOiIxY2ExYzk4ZGYyMDhmZjViYzYyZGJmZmZlNTJhOGQzYjI2Y2Y5MDA1MjU3NmFkODUxMGQzMWYxZjk4MGM0MDkyIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 06:51:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ikg2WHNVM1lsWGFRbFlhcmoxL3dRZHc9PSIsInZhbHVlIjoiYlhDK0ttQ3ozVmVzK3JLVUFEVUo4OUZiSWk5Q1dOMTJRZnBYQ0pWNzRkV1RJR0VaVWc2YnBhbUt0R0J5aVpPbmNUNHUvblZ5RHR0WU13SXczZ0owL0RncEkyckJVLy9LY0czSmNHdmh1ZGl3QXNIeW5BcGhKOVlNNzlBdWdXMURPSEtuYmlGWHBtZklqQXlHSFpCdU5Jd3RFYmlNaEdxSDZ1UmNFTU94NzFkYkdRMmNzemdCWVo3ek1lbGxDSUs5UnRxcFJMTWN5ckhXZ2U5N0l5Y0ZCcXhWOTVRUngyYzd0OTJxam1IZUxsblJFYlpNbzNoaVQ1VVNMZ2RYSjJSckJOQzR3OHRINFBWMUVBYzJpUVY3SGFLaktQTmtnWGZTN3AzK3Jzblk2OTg2ZzVkSHM4MGZuYTQwMk05UVJjVUNpenduUm9jYXMxV3NYc01UV1VaeGg1T1hrdGdlL2Riem1DNWRBOFBqNUhTSlo3VzY0Z2Qwd0Z5Snlkd2c4aFloZTBZQ3RISllvZERwNEoxcERlZzJ2MXBUMDh6OUdRL0N3TFkvbWVLaTQ0QU50YVoxN0xhbisxR3ZtTmkvWmxCcjRUSHR1U2dZOE1DU2ZrYTRYQk1LckVtL0t3VG96VHdxWUxQWnpBY2IyTTFJc0RzQ1RWVGhleGs1NExESHV5b2UiLCJtYWMiOiI0MmU0MDE2NTZhOGJjM2NlYzA4MGIwNjg5MWQyOTg0NThjM2ZmNmU4ODQxZjA1YjEwOGJiMzIzNTRiMmFjMDNkIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 06:51:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZJNDBDRjdlVFFRTXpXd2U2MWpDU0E9PSIsInZhbHVlIjoiVEN2VFpqNHNKdG56d1RhL1U0Rjlya2lBdTBjcUEvaGRJM1A5TkVVM1ZqbzRwcnJyZ3RLRXJJaFVlN2xlQ3crUUtid0lmeVJKTzZnRnAra09qb3NLYTk4TFJtcytJSEpJb2VQZ0ZtR2F0NjRVS010OEYzSnJsb2hrUjFqVVFKWFZvcWNtb0VTbjZCc0RUWUduOWhqTDJzWTQ1c1ZGTmNwWWE0RWVDTmpOdUJnZFdIaUZGRVlBSGhpYUxmUUdQS2xua2xwUlIxMWl6N3AzamFGakY0TUpXSEZsdXlhRzhpQUxjN1B4U1QvSExtdVcvcTkrdU94b3Z0V2I0aVhKUUMwVXJRMGNNMUJwbUtPZVhnWlFUZm5EU3BaVXZGMU9ZUUExeEZFd2c5YjBZMUcrc2FmRldxcWtQelZKT3AxMDdleXdJa1NaRGF1alF1S0hYcHpGamhIbFFINkRZZE14UDRYTE1seWR4T01xT1FRM0Fkcm1ZK29kVGRJdWtGYU15V3hoUTFMclNCbHNYU3l5c1Nzd3J6dmxkQy9KVHRvcVJCbVhSTk5aL05OVnUvWG9oQmZmYUR6OTd1aW9DcG9HVm5PTWlGbkplT3E1ZE1US1kvb0ZDdjU5S3AzcUplejluUk02VFo1NmU0VWV5VFpNazVTZG1DcS9KUFVoS28rZkVtVFEiLCJtYWMiOiIxY2ExYzk4ZGYyMDhmZjViYzYyZGJmZmZlNTJhOGQzYjI2Y2Y5MDA1MjU3NmFkODUxMGQzMWYxZjk4MGM0MDkyIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 06:51:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ikg2WHNVM1lsWGFRbFlhcmoxL3dRZHc9PSIsInZhbHVlIjoiYlhDK0ttQ3ozVmVzK3JLVUFEVUo4OUZiSWk5Q1dOMTJRZnBYQ0pWNzRkV1RJR0VaVWc2YnBhbUt0R0J5aVpPbmNUNHUvblZ5RHR0WU13SXczZ0owL0RncEkyckJVLy9LY0czSmNHdmh1ZGl3QXNIeW5BcGhKOVlNNzlBdWdXMURPSEtuYmlGWHBtZklqQXlHSFpCdU5Jd3RFYmlNaEdxSDZ1UmNFTU94NzFkYkdRMmNzemdCWVo3ek1lbGxDSUs5UnRxcFJMTWN5ckhXZ2U5N0l5Y0ZCcXhWOTVRUngyYzd0OTJxam1IZUxsblJFYlpNbzNoaVQ1VVNMZ2RYSjJSckJOQzR3OHRINFBWMUVBYzJpUVY3SGFLaktQTmtnWGZTN3AzK3Jzblk2OTg2ZzVkSHM4MGZuYTQwMk05UVJjVUNpenduUm9jYXMxV3NYc01UV1VaeGg1T1hrdGdlL2Riem1DNWRBOFBqNUhTSlo3VzY0Z2Qwd0Z5Snlkd2c4aFloZTBZQ3RISllvZERwNEoxcERlZzJ2MXBUMDh6OUdRL0N3TFkvbWVLaTQ0QU50YVoxN0xhbisxR3ZtTmkvWmxCcjRUSHR1U2dZOE1DU2ZrYTRYQk1LckVtL0t3VG96VHdxWUxQWnpBY2IyTTFJc0RzQ1RWVGhleGs1NExESHV5b2UiLCJtYWMiOiI0MmU0MDE2NTZhOGJjM2NlYzA4MGIwNjg5MWQyOTg0NThjM2ZmNmU4ODQxZjA1YjEwOGJiMzIzNTRiMmFjMDNkIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 06:51:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1660552003\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-38911119 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">n3EIZeDwUEp34hWI7nXk4AAAAWtQcamHSS2zNw2v</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-38911119\", {\"maxDepth\":0})</script>\n"}}