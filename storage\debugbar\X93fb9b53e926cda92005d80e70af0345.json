{"__meta": {"id": "X93fb9b53e926cda92005d80e70af0345", "datetime": "2025-07-29 05:41:58", "utime": **********.722326, "method": "GET", "uri": "/api/leads/13", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753767717.783233, "end": **********.722344, "duration": 0.9391109943389893, "duration_str": "939ms", "measures": [{"label": "Booting", "start": 1753767717.783233, "relative_start": 0, "end": **********.620221, "relative_end": **********.620221, "duration": 0.8369879722595215, "duration_str": "837ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.620234, "relative_start": 0.8370010852813721, "end": **********.722346, "relative_end": 2.1457672119140625e-06, "duration": 0.1021120548248291, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46061648, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/leads/{id}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ContactController@getLeadForPreview", "namespace": null, "prefix": "", "where": [], "as": "api.leads.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=440\" onclick=\"\">app/Http/Controllers/ContactController.php:440-476</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.011689999999999999, "accumulated_duration_str": "11.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.671363, "duration": 0.00873, "duration_str": "8.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 74.679}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.69075, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 74.679, "width_percent": 6.587}, {"sql": "select * from `leads` where `id` = '13' and `created_by` = 84 limit 1", "type": "query", "params": [], "bindings": ["13", "84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 446}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6969228, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:446", "source": "app/Http/Controllers/ContactController.php:446", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=446", "ajax": false, "filename": "ContactController.php", "line": "446"}, "connection": "omx_sass_systam_db", "start_percent": 81.266, "width_percent": 6.672}, {"sql": "select * from `lead_stages` where `lead_stages`.`id` in ('')", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 446}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7036788, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:446", "source": "app/Http/Controllers/ContactController.php:446", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=446", "ajax": false, "filename": "ContactController.php", "line": "446"}, "connection": "omx_sass_systam_db", "start_percent": 87.938, "width_percent": 5.047}, {"sql": "select * from `pipelines` where `pipelines`.`id` in (27)", "type": "query", "params": [], "bindings": ["27"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/ContactController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactController.php", "line": 446}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.706906, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ContactController.php:446", "source": "app/Http/Controllers/ContactController.php:446", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactController.php&line=446", "ajax": false, "filename": "ContactController.php", "line": "446"}, "connection": "omx_sass_systam_db", "start_percent": 92.985, "width_percent": 7.015}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\Pipeline": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/leads/13\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/api/leads/13", "status_code": "<pre class=sf-dump id=sf-dump-14599306 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-14599306\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1921340537 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1921340537\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1976617483 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1976617483\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-978410288 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/contacts</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImdMYnJZdmNkQkVpa2N1Y3N2RmRQaVE9PSIsInZhbHVlIjoic2orZk85NkhOQ1J4WjdtTmwrL3hvczh3TjBSMVR5SUdJMjB1QXhHTFB6RE8wa24vWWt6VGtBOGZRSms0K29SUGdxcFpFeEs0TkVsZXZVRjZ3V3puaHdTVEpBSUp4THhObnNZYzl5a0RZSjV4OHF1TFNpelJ0WUgrUHRjTVB0VnVmOGlmOVdlNGlSQ0Yva21GVENhRDZzZnVLU3hBWGNKVnZteUE2Y0pUNTl1bnZoZ1B2UjNvRGZhUHkvK0tEQUNyWE94V29vL0ZxOGR3aUhEYVhDOE1kbFM4VHpBZCtKeTlmVjZSUE9FMUtyVTl0OVMwVjc0MGh4Tmg0c3JoenF6UUlVckZicG1WVndTZmFjM0RSS0UycXNCWVYyOGg5QVRVWHN5Q0VTeHgxL2xERmtRcitaOEN3SWo0Y1N3WlZnWjBNMXRpUVlGd2cvKyswMTRmQlpYTDZzN3J2UlByN0F1bFovSVFDVDVqYTRMS2haaHJ1aXg5QW85cmw2OVJDTGhKQ1B0a3JHWDJqUU9WUDFTcm94bWtWZU1hOHRyekwvdFJTV1g5OU9UYlR6MkQ1ZnlUMjhmRU1OczdGM3l0VXdwR3c2TWlNZTI0N3orbG5JTHlwd0ZyMjZJeVpraEZGcWk2dTVKQjdZQU1PQnhtakZTL0tWaW9BbEJlQzY5T0VXS2UiLCJtYWMiOiJjZjE3MmNiZTIyM2RiNmQ5MzdmNTk4YTIzYzk2ZGM1NGE1Y2Q3YWVhMTMzNThiZGM4NTIxYTUzNzNhMjdlNjBiIiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlJVR3cwSUVhYlJ4NCtsSy9RUnVGelE9PSIsInZhbHVlIjoiOXpGR3hFZ08yS3JTejBHVUROYlJkQkppMFY0VmFMZVBoYkVxWWNHMXBtL2NMTTV5eVZQRGRSQktNS1o0dEJieEx3eU1kcTlHbGU5aDRLMXFTOExMekpTUDlLRm5WN1huTHFUcnhDYlNRU0NNU09rN2IxUndZeTFQa2RDYXduYTUzRmN1THZ0aHBzci9RQk5ORkxoOWpSUkxOOFR4SXNEQnl4ekxoQlpTdlB6bjd1Umh1TmRwUldZbThYUVNSOU5iVUdMWGdsQXJMZVVONlI3eUhycnFjcHZXWFd1YVNtQ0F0TGVrOVlzTTUrOHU5RkFtS0JyZ0lpcmQ0NUVJbVYrV0pUNXUzS3VrSGF3dHZiQ3RLYlAxLzY1NURuM0FsVElqaDdGd0liR2cxaUJHNnlVcGRUN3doaDRPNDczenBZV1g3WDBRWkgzcHpGRXNBOHJ4Tkt4N1hNSUU4M2ZIZmREb0loaEhYYkZuVEwwSXVtbnJtQzl1cVorMjRYWjZZVmVVdDNyUEM3eWVPRlpIN2pJN1ZMa3YycVZmL0M5ZGhPSXVwUXJEaUJPL1RobEJqWDRRUkZvVVFlTWhTMUtUVEQza0tUM09aNUJUYVJQV3IzSndnanlMbGJiTnNHc0M3d240MEx0eVRPa21aZG9Hc0FCaWZtUUJGNmtKdm9YenF1SlAiLCJtYWMiOiIwYjg3OTllYjg4ZDhlOGEwNDgzNWNmNWVlM2VhM2IwOTI3N2FmMzFmNWZjZGI5NzI3ODg5NDM0N2RlZWIyNThjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-978410288\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-779311788 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-779311788\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-443561586 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:41:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitndW1zeGNNY2ZwWDZKL0paOGVtbmc9PSIsInZhbHVlIjoiS2drYTR1Ukg2YXZYQVhzUTU2ZEdzR1ZKK2N5aU1DZzB5bkJjeHNYNTFDSUMzR1gzRUlWTmtnbGVrQlJ1VHk2N2tDSENkSVZ0UnhXaThCMDJ6bk1zMEI4UE5kT1ZoV1p4RmZKZUdOZGdXT3lZZ25BQS82RU9EaUtQNmZzSjcvN0QwUjVGZ1kxbXQ2aDM2T0hnNVIrUWMzNXM4N00vZXJnaFo1ZUQrb3hoTm9Mc1ZYRHJTOTdtNXRvOVJJMXREeU00amtlK29mZ2VTOEFQL2JOd0VUc2NiaGtUamV1OUNqdXVUL0lLZ3BNalRLZGFiZTVTb01Xc3VYejR3OTV4MzlQQVZzcG9hQ2VJYVZKRXhVK2JHd0V4VFZqQjhEMDVWeDJtcGg2dGpKTDgxMW5GSWZ0V2ZpNk9RV0swTXI2U1VQNlZaTEx1dEhJa0JCdndrVFYzVExvSlFCb0Jtc3F4aC8xM211YmZraWJmc2J3dEIyeW1ZWHovUUNrcnVPVE5lR082R2VuOEJrZkpoTmFIMXBLU3FOR29YZFRhZ1h3VFpNNUlEMy9UWmlMdkViWVYyRlZtQTMyY1hvVnVwS3ZKU1RvOEtlOHF0eE8reXR5My9YRDBtUWN6cVhBUTlDRlZRTjJua1FsR2ZOWDRERUdxVC9ZeXhVOFB4dXM5RnVoa2NMN2wiLCJtYWMiOiIzNjk0ZjgyMzJjYWYxMDA3NjQzODQ2YmI3YzIxOWVkZmJjMzZhMTdiNDY2ZmM1MzkyMzQ2ZDAxOWMxMTU5NDVlIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:41:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IktRTlpwNHdFYnFPWG95QUQxdC94UVE9PSIsInZhbHVlIjoiL0JJQkY3Q3RBaVRsVUdDUitsdElsRkE1dXhhRnUyWmdDYTlIWGVMQmRBdnlYTFJ2TzJETlE0a25KdERtOStNRE55Mm9VLzJnaTlKeUd4a044ME5HRFFyWi9WYnc1L1g4clhVSisvTjA1dG55ZTB0TlA1NGFqSEwxZkplUWo5NVI5M0UycTZOallteHdxbnMvK3VUelo4cHYxNTVsdThpV2lpMnZWUEVFRTB6K0J3Z2JiYmlvcjZIMmM3T0sxZWtrMlFkRkVoK29TNmlaYU4vRFI3cVhLTVNsS3M4WjJUSC91TzdlcTVFbDF4QVlFOFlCdXJqckREaDQya2pxKzdpVmI5M0J4SnZOZERqTDg2K0pXalY5eWRTazlJYXVLT2dhd1U2bm5SS3d1SEFkL2pxZ3BsRE5Va0xJQ056RDNNbklKSU9pazJONVM3SUUvL2VoWXJwVk8xZmNKc253MFVOYmtMdFBjRVlwSm43QTF3S1FsUEV4dWo4YlRNbDd6Q1l6eDJiZW5pVnY3bGI3TmI3ZGtTMDVGKzRQREZBTHJ2dUE1NHlwbEVsVVRYak4rZlFpOUJ2ZkpHSnNOZjdJbVYxN2FlOWE5Ny9EcmMwcnVpTDRSKzVIb2g1Q2JXVndqY3VXWWdQTTkrWFZYVTZuR3VxQXlwRGRQUGdWNlZGVzBwS1AiLCJtYWMiOiIzY2FjZmM0MWFkNzIyODQ0Y2JhNzU5NTg4MWNmNzIwYWNhM2JjNWJiNWE2ZTc5OTZlYWExZjBiMmZlY2YwMjIwIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:41:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitndW1zeGNNY2ZwWDZKL0paOGVtbmc9PSIsInZhbHVlIjoiS2drYTR1Ukg2YXZYQVhzUTU2ZEdzR1ZKK2N5aU1DZzB5bkJjeHNYNTFDSUMzR1gzRUlWTmtnbGVrQlJ1VHk2N2tDSENkSVZ0UnhXaThCMDJ6bk1zMEI4UE5kT1ZoV1p4RmZKZUdOZGdXT3lZZ25BQS82RU9EaUtQNmZzSjcvN0QwUjVGZ1kxbXQ2aDM2T0hnNVIrUWMzNXM4N00vZXJnaFo1ZUQrb3hoTm9Mc1ZYRHJTOTdtNXRvOVJJMXREeU00amtlK29mZ2VTOEFQL2JOd0VUc2NiaGtUamV1OUNqdXVUL0lLZ3BNalRLZGFiZTVTb01Xc3VYejR3OTV4MzlQQVZzcG9hQ2VJYVZKRXhVK2JHd0V4VFZqQjhEMDVWeDJtcGg2dGpKTDgxMW5GSWZ0V2ZpNk9RV0swTXI2U1VQNlZaTEx1dEhJa0JCdndrVFYzVExvSlFCb0Jtc3F4aC8xM211YmZraWJmc2J3dEIyeW1ZWHovUUNrcnVPVE5lR082R2VuOEJrZkpoTmFIMXBLU3FOR29YZFRhZ1h3VFpNNUlEMy9UWmlMdkViWVYyRlZtQTMyY1hvVnVwS3ZKU1RvOEtlOHF0eE8reXR5My9YRDBtUWN6cVhBUTlDRlZRTjJua1FsR2ZOWDRERUdxVC9ZeXhVOFB4dXM5RnVoa2NMN2wiLCJtYWMiOiIzNjk0ZjgyMzJjYWYxMDA3NjQzODQ2YmI3YzIxOWVkZmJjMzZhMTdiNDY2ZmM1MzkyMzQ2ZDAxOWMxMTU5NDVlIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:41:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IktRTlpwNHdFYnFPWG95QUQxdC94UVE9PSIsInZhbHVlIjoiL0JJQkY3Q3RBaVRsVUdDUitsdElsRkE1dXhhRnUyWmdDYTlIWGVMQmRBdnlYTFJ2TzJETlE0a25KdERtOStNRE55Mm9VLzJnaTlKeUd4a044ME5HRFFyWi9WYnc1L1g4clhVSisvTjA1dG55ZTB0TlA1NGFqSEwxZkplUWo5NVI5M0UycTZOallteHdxbnMvK3VUelo4cHYxNTVsdThpV2lpMnZWUEVFRTB6K0J3Z2JiYmlvcjZIMmM3T0sxZWtrMlFkRkVoK29TNmlaYU4vRFI3cVhLTVNsS3M4WjJUSC91TzdlcTVFbDF4QVlFOFlCdXJqckREaDQya2pxKzdpVmI5M0J4SnZOZERqTDg2K0pXalY5eWRTazlJYXVLT2dhd1U2bm5SS3d1SEFkL2pxZ3BsRE5Va0xJQ056RDNNbklKSU9pazJONVM3SUUvL2VoWXJwVk8xZmNKc253MFVOYmtMdFBjRVlwSm43QTF3S1FsUEV4dWo4YlRNbDd6Q1l6eDJiZW5pVnY3bGI3TmI3ZGtTMDVGKzRQREZBTHJ2dUE1NHlwbEVsVVRYak4rZlFpOUJ2ZkpHSnNOZjdJbVYxN2FlOWE5Ny9EcmMwcnVpTDRSKzVIb2g1Q2JXVndqY3VXWWdQTTkrWFZYVTZuR3VxQXlwRGRQUGdWNlZGVzBwS1AiLCJtYWMiOiIzY2FjZmM0MWFkNzIyODQ0Y2JhNzU5NTg4MWNmNzIwYWNhM2JjNWJiNWE2ZTc5OTZlYWExZjBiMmZlY2YwMjIwIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:41:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-443561586\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1481788402 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://127.0.0.1:8000/leads/13</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1481788402\", {\"maxDepth\":0})</script>\n"}}