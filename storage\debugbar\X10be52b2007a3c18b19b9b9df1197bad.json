{"__meta": {"id": "X10be52b2007a3c18b19b9b9df1197bad", "datetime": "2025-07-29 05:07:49", "utime": **********.684914, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753765668.878343, "end": **********.684952, "duration": 0.8066089153289795, "duration_str": "807ms", "measures": [{"label": "Booting", "start": 1753765668.878343, "relative_start": 0, "end": **********.611117, "relative_end": **********.611117, "duration": 0.7327737808227539, "duration_str": "733ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.611147, "relative_start": 0.****************, "end": **********.684957, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "73.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2997\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1801 to 1807\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1801\" onclick=\"\">routes/web.php:1801-1807</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JGp0Bj4ibgYR5JNY0vkrolZkk367ZS31J8xXR0Ey", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-880839914 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-880839914\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-485196343 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-485196343\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1671142883 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1671142883\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-928077940 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-928077940\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1300536574 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1300536574\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-663073412 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:07:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inh3UXhZbTJwM0JmUk1EQ0hqKzZWVGc9PSIsInZhbHVlIjoiNjgwRXM5KzVITDFoK1Zickx1bWROc1pCSFFIVVR0VEFlOGhaMFhzK3hDdkFxMG5HV2lnS200UUNlNU9ER3gzM0E5UVdUOXY1aHJSMitzTC9ZbE00QWJuTTJKRHI3dE56SnF1S2FUb0kvZnNFTzVRVU8vamZ5Ri9FVHhlcU9CbmZpdU9zSnJXSkhNS2c0c0FweTR1SnVTN0lIdWhHWU1HNkNaS0Y4Rno5UVlyV3B0SmRNc21VQklja1NkZWRncmYrdzU3bkZMT2tIbU4zVkxGU0g2aDVJdit6amVuOE4vZU53ZDFEcXgySGtaUVVpb0VYUDBkMkUxRzd5UUliWmdlNjRpNURpbVRORm5WT1piRDhzRkNOME9idXlDMVEvcFlpMGhJQ3pQTkxmVEZWemVKWkRILzNtWEpSdSs5eEY2d3htVFQzY1pIV0RUcFRzckxIeGpBUEFyUndUYS91Q29zM0I3MFZTMFVicmdXTlFtMktrMTRIRDhNZVd0R2VEeCsvTEJrM0RoTEEwYlJmZWFmZHFIVVMyd3AwT3FXcDBYaUZwWm51b2tzQWxxcThlL0xPc0tEMDNwQXdrQ1ZXbVdWS3V3M1YvdVJRNzllSWFCQVN2U1ZMdk9tdm1yNHNsdDR6QjJ4dXN0d3UrL2ZvZGVncldiUTBjTDJDbDhrRkw1MU4iLCJtYWMiOiIxNTlkNzEwNDA4NDdjZTliODY3YjBmMTRjMjI1ZTE0ZDUwNzk5MjIxNTBlMDVkMDQ1NjNkODE2NmNhODlkMmMxIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:07:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IjNGSm1HSkNaSkdxOWZsemRJb3RZUXc9PSIsInZhbHVlIjoiN0t4SVhVbkZaS2duTGtqZHhPUE15OWRvZUJtM21zMTR4ZjdpU1NEVWJreXlWZ0tHb0JURldDaU9Sbm41ZUhYRElVcnNnV3U1Vmd6Q1JYWjVlcjRDRVlJYnVOemR6MkV6dzJ6S1ZRSG1wZWQ4S2c4bXlNOTYrN1BmV21Nak1qLzg0VFg5eCs4UHpxZUlwbTJPQTMzS3hOQnc4WnAzMHNQOHFYekUrL2RoN0Q3Zk15eDRPTEtXUm9BZWZsMDZEM2pmVkZ0RHVRYkdLZ05DSVNSQkt5eGs3ckRuZU1CYTA0aWNORVYwYm02T01FZ09jUWxIWUF6WlJURXRXbmJOWm8rK1dTTzgxb3BDNUhjTFpRMXgzaHU2UWVRUlgvZDAzYm82VHZTQTJJMThyNFB6VXNUSHBWc2dSdkNTWDhtakRwQW9IRDg5cVc1dzNtcGZRQkFKVkRaNFBzanU5T3NUS1R0KzVrMzZJbGk0S0FMQUxZZnYrKzBQUHhwZjA5U2Z3Y0RNa2RzeTFuemJJYTBZTXd6L1NDTk5wRjU0N3RrUTAvdGVMVksvcXJpejkrREFLa1l4djRKZEx5cUd6S2JkeW4vQ0MzbDFkMENlWDlTVy9yNjk4Z3Nxbkp1RWIvT05UT1RrMlZZMEkrQ2VXeVF1V1p4dUZSQStYUHdFYWxCZ2d0cE0iLCJtYWMiOiJiYjg5NjQwZWMzMDE4NDgwNDg3MThjNDRmNmRiNDIzZjIxNmFkOTgwMTI2ZWYzYTNhMDg5ZmM1Mjg2ODM0NWQ1IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:07:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inh3UXhZbTJwM0JmUk1EQ0hqKzZWVGc9PSIsInZhbHVlIjoiNjgwRXM5KzVITDFoK1Zickx1bWROc1pCSFFIVVR0VEFlOGhaMFhzK3hDdkFxMG5HV2lnS200UUNlNU9ER3gzM0E5UVdUOXY1aHJSMitzTC9ZbE00QWJuTTJKRHI3dE56SnF1S2FUb0kvZnNFTzVRVU8vamZ5Ri9FVHhlcU9CbmZpdU9zSnJXSkhNS2c0c0FweTR1SnVTN0lIdWhHWU1HNkNaS0Y4Rno5UVlyV3B0SmRNc21VQklja1NkZWRncmYrdzU3bkZMT2tIbU4zVkxGU0g2aDVJdit6amVuOE4vZU53ZDFEcXgySGtaUVVpb0VYUDBkMkUxRzd5UUliWmdlNjRpNURpbVRORm5WT1piRDhzRkNOME9idXlDMVEvcFlpMGhJQ3pQTkxmVEZWemVKWkRILzNtWEpSdSs5eEY2d3htVFQzY1pIV0RUcFRzckxIeGpBUEFyUndUYS91Q29zM0I3MFZTMFVicmdXTlFtMktrMTRIRDhNZVd0R2VEeCsvTEJrM0RoTEEwYlJmZWFmZHFIVVMyd3AwT3FXcDBYaUZwWm51b2tzQWxxcThlL0xPc0tEMDNwQXdrQ1ZXbVdWS3V3M1YvdVJRNzllSWFCQVN2U1ZMdk9tdm1yNHNsdDR6QjJ4dXN0d3UrL2ZvZGVncldiUTBjTDJDbDhrRkw1MU4iLCJtYWMiOiIxNTlkNzEwNDA4NDdjZTliODY3YjBmMTRjMjI1ZTE0ZDUwNzk5MjIxNTBlMDVkMDQ1NjNkODE2NmNhODlkMmMxIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:07:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IjNGSm1HSkNaSkdxOWZsemRJb3RZUXc9PSIsInZhbHVlIjoiN0t4SVhVbkZaS2duTGtqZHhPUE15OWRvZUJtM21zMTR4ZjdpU1NEVWJreXlWZ0tHb0JURldDaU9Sbm41ZUhYRElVcnNnV3U1Vmd6Q1JYWjVlcjRDRVlJYnVOemR6MkV6dzJ6S1ZRSG1wZWQ4S2c4bXlNOTYrN1BmV21Nak1qLzg0VFg5eCs4UHpxZUlwbTJPQTMzS3hOQnc4WnAzMHNQOHFYekUrL2RoN0Q3Zk15eDRPTEtXUm9BZWZsMDZEM2pmVkZ0RHVRYkdLZ05DSVNSQkt5eGs3ckRuZU1CYTA0aWNORVYwYm02T01FZ09jUWxIWUF6WlJURXRXbmJOWm8rK1dTTzgxb3BDNUhjTFpRMXgzaHU2UWVRUlgvZDAzYm82VHZTQTJJMThyNFB6VXNUSHBWc2dSdkNTWDhtakRwQW9IRDg5cVc1dzNtcGZRQkFKVkRaNFBzanU5T3NUS1R0KzVrMzZJbGk0S0FMQUxZZnYrKzBQUHhwZjA5U2Z3Y0RNa2RzeTFuemJJYTBZTXd6L1NDTk5wRjU0N3RrUTAvdGVMVksvcXJpejkrREFLa1l4djRKZEx5cUd6S2JkeW4vQ0MzbDFkMENlWDlTVy9yNjk4Z3Nxbkp1RWIvT05UT1RrMlZZMEkrQ2VXeVF1V1p4dUZSQStYUHdFYWxCZ2d0cE0iLCJtYWMiOiJiYjg5NjQwZWMzMDE4NDgwNDg3MThjNDRmNmRiNDIzZjIxNmFkOTgwMTI2ZWYzYTNhMDg5ZmM1Mjg2ODM0NWQ1IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:07:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-663073412\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1693045463 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JGp0Bj4ibgYR5JNY0vkrolZkk367ZS31J8xXR0Ey</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1693045463\", {\"maxDepth\":0})</script>\n"}}