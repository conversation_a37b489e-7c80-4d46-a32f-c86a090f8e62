{"__meta": {"id": "Xa293978e1cbc957b69bc7fa55ae26be9", "datetime": "2025-07-29 05:04:13", "utime": **********.478743, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753765452.547134, "end": **********.478788, "duration": 0.9316539764404297, "duration_str": "932ms", "measures": [{"label": "Booting", "start": 1753765452.547134, "relative_start": 0, "end": **********.395461, "relative_end": **********.395461, "duration": 0.8483271598815918, "duration_str": "848ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.39548, "relative_start": 0.****************, "end": **********.478791, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "83.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2997\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1801 to 1807\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1801\" onclick=\"\">routes/web.php:1801-1807</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "ha8zb4yBwA01NKtBQ8FSc9Bi0OSV4tSRm4SSxuZf", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1481052332 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1481052332\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1220366852 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1220366852\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1594967532 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1594967532\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1272389856 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1272389856\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1825655551 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1825655551\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:04:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlI0clBQdVhkWkFySHFOTXcyUndIaWc9PSIsInZhbHVlIjoiRnAxVWVSbi9qdUFhRDZ5d09FUVROcENsN0FKLzlmaGVJTGlpbDE3U2FQeXR0SFRWOVl3M0tUWTlMSzI0V2d1YlNRUmhENjlFR25LZE56NHZ1ZU9JVG13SjFNWFdVYzRDemk4ZWU3ak5vc0NZSGt6ZzVVZW9OVkNTbDZ1Y2o0bkJid3JVWjdsTXlaSDl3SjQvank3WVM1R1pjOEpqaUtyMExvZWNpU3JROUU2c09DQ0sxcjhWVUkxa1hFaFlnTUNWZFVaQjk4a3hxMW5qT2o2bVQzZWZ6b2ZYU0ZCNFlYa2lrdWtNZnNKYjlJOG9wTkdMZGY3Z2RreGJuWGxOMmZ3ZWV0NXhIRVZvVWhtY2QzbnVyU21RVDdqMnl0d1hqZjhkOE1XMHhZSmVLa1hGSXVTVTVON0wyUDVYbUZQUkFLVnEyakxUdExQcERzQ0hGUzlZcnVneDFheEtlZDNacEU3Wk50cXVjS1pMQW9xMlh3UStsaTVvdmhicmNKU2NYTE9ZRVVndjBtSzJOZ3NrQUQ4NVVjb0Y4SXN6OWxpemJEdjYrU082MEwwb2RMVlpnRkVLMjNEeE1oaWxrVktRN2x5MjROSkhmOTl0VzQvcUY2N0MzUU1TZENuOVpudzdweVpOZE5ScmFXSWhVVlg5TERSL0g4REtMOExxbWxQNzZUL1kiLCJtYWMiOiI3NWQzOWVlMWM4NmE1YjA3OTQ0NWYwMmJmNjU2ZWJiMmU2ZjhjNGI3ZTdhMGFiOWNhOTlkNmI2MmViOGE3ZjhjIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:04:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6Ik9lT1ZOcVJFa2k2Q3laRXFISEZ6M0E9PSIsInZhbHVlIjoiekVGdUR5Q2RBNGxXcGUyTnJrVGh2NFhRY0k5VlhBWm1HdmR0S09RSkFFandCNXhkMTBDWlB2d0tSZmxGdEFYa0pjUWsvcDVXV1hIcHZselduaDRDdUdyT3h0by9waU56a3NpcVM1UWtDTmNtNy9OckNQby9RTDNLSFdZblJOWHdvNlhsT2NyZ0xEYXV4VjVTL2NaODdON2xSeGp3UTE4bDRrZUQxcXZ0aGpyRzU4UisyTTlMS1I1Sy9nWGtqcmdzWnJrUjZGWnZ3d3FrUmNNN2JXQ1BWT3oxSkR6ckVXNkd4QUp0U3VvaHJMQnlYdHNuOUppdlBzekZHYVpvTFpIYzVnd0dMay9FYlBiTHdqTnFhcm11bWFaVUJ5NHpQeUNyN0hiYlJ4dzc3aExVSlRwcDhaUlZYQTJmYktYajlvWUxDeTZPeTg5M0h4UjlKaCsxRytIVDBYVzBKcmNLbU9OT3hpUzFQVEtoNUVROG0xcmtsMkN1SUlQZ25nRXI2NFNlMm50TE5xRHJ1UjF1NnJ1M0xnbVpNNWVHT21VNFUxYXlnNGZpNU51M0w5M0xUMUluUHJMaXM0NHpMUFlqaEd5OTNUQXVYMnk5SHg3NTlqMjJualVNelZyenFhZE1LMU5VWTltOFRRUklyYUNiQk1FNkZycUZ6b3ZuUEdBQnNGNmkiLCJtYWMiOiIwNjc2OTA3NzkwNGIwNDkwZTljNDhhY2ZjZWQ2MDcwOTk1Y2IxMTg5YmJjM2Y4MWM0ZmQxYTI2ZWQ5NDFiNGMwIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:04:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlI0clBQdVhkWkFySHFOTXcyUndIaWc9PSIsInZhbHVlIjoiRnAxVWVSbi9qdUFhRDZ5d09FUVROcENsN0FKLzlmaGVJTGlpbDE3U2FQeXR0SFRWOVl3M0tUWTlMSzI0V2d1YlNRUmhENjlFR25LZE56NHZ1ZU9JVG13SjFNWFdVYzRDemk4ZWU3ak5vc0NZSGt6ZzVVZW9OVkNTbDZ1Y2o0bkJid3JVWjdsTXlaSDl3SjQvank3WVM1R1pjOEpqaUtyMExvZWNpU3JROUU2c09DQ0sxcjhWVUkxa1hFaFlnTUNWZFVaQjk4a3hxMW5qT2o2bVQzZWZ6b2ZYU0ZCNFlYa2lrdWtNZnNKYjlJOG9wTkdMZGY3Z2RreGJuWGxOMmZ3ZWV0NXhIRVZvVWhtY2QzbnVyU21RVDdqMnl0d1hqZjhkOE1XMHhZSmVLa1hGSXVTVTVON0wyUDVYbUZQUkFLVnEyakxUdExQcERzQ0hGUzlZcnVneDFheEtlZDNacEU3Wk50cXVjS1pMQW9xMlh3UStsaTVvdmhicmNKU2NYTE9ZRVVndjBtSzJOZ3NrQUQ4NVVjb0Y4SXN6OWxpemJEdjYrU082MEwwb2RMVlpnRkVLMjNEeE1oaWxrVktRN2x5MjROSkhmOTl0VzQvcUY2N0MzUU1TZENuOVpudzdweVpOZE5ScmFXSWhVVlg5TERSL0g4REtMOExxbWxQNzZUL1kiLCJtYWMiOiI3NWQzOWVlMWM4NmE1YjA3OTQ0NWYwMmJmNjU2ZWJiMmU2ZjhjNGI3ZTdhMGFiOWNhOTlkNmI2MmViOGE3ZjhjIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:04:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6Ik9lT1ZOcVJFa2k2Q3laRXFISEZ6M0E9PSIsInZhbHVlIjoiekVGdUR5Q2RBNGxXcGUyTnJrVGh2NFhRY0k5VlhBWm1HdmR0S09RSkFFandCNXhkMTBDWlB2d0tSZmxGdEFYa0pjUWsvcDVXV1hIcHZselduaDRDdUdyT3h0by9waU56a3NpcVM1UWtDTmNtNy9OckNQby9RTDNLSFdZblJOWHdvNlhsT2NyZ0xEYXV4VjVTL2NaODdON2xSeGp3UTE4bDRrZUQxcXZ0aGpyRzU4UisyTTlMS1I1Sy9nWGtqcmdzWnJrUjZGWnZ3d3FrUmNNN2JXQ1BWT3oxSkR6ckVXNkd4QUp0U3VvaHJMQnlYdHNuOUppdlBzekZHYVpvTFpIYzVnd0dMay9FYlBiTHdqTnFhcm11bWFaVUJ5NHpQeUNyN0hiYlJ4dzc3aExVSlRwcDhaUlZYQTJmYktYajlvWUxDeTZPeTg5M0h4UjlKaCsxRytIVDBYVzBKcmNLbU9OT3hpUzFQVEtoNUVROG0xcmtsMkN1SUlQZ25nRXI2NFNlMm50TE5xRHJ1UjF1NnJ1M0xnbVpNNWVHT21VNFUxYXlnNGZpNU51M0w5M0xUMUluUHJMaXM0NHpMUFlqaEd5OTNUQXVYMnk5SHg3NTlqMjJualVNelZyenFhZE1LMU5VWTltOFRRUklyYUNiQk1FNkZycUZ6b3ZuUEdBQnNGNmkiLCJtYWMiOiIwNjc2OTA3NzkwNGIwNDkwZTljNDhhY2ZjZWQ2MDcwOTk1Y2IxMTg5YmJjM2Y4MWM0ZmQxYTI2ZWQ5NDFiNGMwIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:04:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1859409157 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ha8zb4yBwA01NKtBQ8FSc9Bi0OSV4tSRm4SSxuZf</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1859409157\", {\"maxDepth\":0})</script>\n"}}