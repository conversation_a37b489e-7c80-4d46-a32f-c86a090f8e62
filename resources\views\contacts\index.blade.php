@extends('layouts.admin')

@section('page-title')
    {{ __('Contacts') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('Contacts') }}</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-lg-8">
                            <h5>{{ __('Contacts') }}</h5>
                        </div>
                        <div class="col-lg-4">
                            <div class="d-flex align-items-center justify-content-end">
                                @php
                                    $hasFilters = request('filter_name') || request('filter_email') || request('filter_phone') || request('filter_type');
                                @endphp

                                <!-- Action Buttons Group -->
                                <div class="action-buttons-group d-inline-flex align-items-center me-2">
                                    <!-- Add New Contact -->
                                    <button class="btn-action-modern me-1" data-bs-toggle="tooltip" data-bs-placement="top" title="{{ __('Add a New Contact') }}" onclick="openAddContactModal()">
                                        <i class="fas fa-user-plus" style="color: #4CAF50;"></i>
                                    </button>

                                    <!-- Add to Contact Group -->
                                    <button class="btn-action-modern me-1" data-bs-toggle="tooltip" data-bs-placement="top" title="{{ __('Add to a Contact Group') }}" onclick="openContactGroupModal()">
                                        <i class="fas fa-users" style="color: #2196F3;"></i>
                                    </button>

                                    <!-- Add to Workflow -->
                                    <button class="btn-action-modern me-1" data-bs-toggle="tooltip" data-bs-placement="top" title="{{ __('Add to Workflow') }}" onclick="openWorkflowModal()">
                                        <i class="fas fa-project-diagram" style="color: #FF9800;"></i>
                                    </button>

                                    <!-- Add Tag -->
                                    <button class="btn-action-modern me-1" data-bs-toggle="tooltip" data-bs-placement="top" title="{{ __('Add Tag') }}" onclick="openTagModal()">
                                        <i class="fas fa-tag" style="color: #9C27B0;"></i>
                                    </button>

                                    <!-- Appointment Booking -->
                                    <button class="btn-action-modern me-1" data-bs-toggle="tooltip" data-bs-placement="top" title="{{ __('Appointment Booking') }}" onclick="openAppointmentModal()">
                                        <i class="fas fa-calendar-plus" style="color: #E91E63;"></i>
                                    </button>

                                    <!-- Bulk Import -->
                                    <button class="btn-action-modern me-1" data-bs-toggle="tooltip" data-bs-placement="top" title="{{ __('Bulk Import') }}" onclick="openBulkImportModal()">
                                        <i class="fas fa-file-import" style="color: #607D8B;"></i>
                                    </button>

                                    <!-- Bulk Export -->
                                    <button class="btn-action-modern me-1" data-bs-toggle="tooltip" data-bs-placement="top" title="{{ __('Bulk Export') }}" onclick="exportContacts()">
                                        <i class="fas fa-file-export" style="color: #795548;"></i>
                                    </button>

                                    <!-- Search Filter -->
                                    <button class="btn-action-modern me-1" data-bs-toggle="tooltip" data-bs-placement="top" title="{{ __('Search Filter') }}" onclick="openSearchModal()">
                                        <i class="fas fa-search" style="color: #3F51B5;"></i>
                                    </button>
                                </div>

                                <!-- Filter Button -->
                                <button class="btn btn-sm {{ $hasFilters ? 'btn-warning' : 'btn-primary' }}" type="button" data-bs-toggle="offcanvas" data-bs-target="#contactsFilter" aria-controls="contactsFilter">
                                    <i class="fa fa-filter fa-2"></i> {{ __('') }}
                                    @if($hasFilters)
                                        <span class="badge bg-light text-dark ms-1">{{ __('Active') }}</span>
                                    @endif
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if($hasFilters)
                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                            <strong>{{ __('Active Filters:') }}</strong>
                            @if(request('filter_name'))
                                <span class="badge bg-primary me-1">{{ __('Name') }}: {{ request('filter_name') }}</span>
                            @endif
                            @if(request('filter_email'))
                                <span class="badge bg-primary me-1">{{ __('Email') }}: {{ request('filter_email') }}</span>
                            @endif
                            @if(request('filter_phone'))
                                <span class="badge bg-primary me-1">{{ __('Phone') }}: {{ request('filter_phone') }}</span>
                            @endif
                            @if(request('filter_type'))
                                <span class="badge bg-primary me-1">{{ __('Type') }}:
                                    @if(request('filter_type') == 'WhatsApp')
                                        {{ __('External Contact') }}
                                    @else
                                        {{ request('filter_type') }}
                                    @endif
                                </span>
                            @endif
                            <a href="{{ route('contacts.index') }}" class="btn btn-sm btn-outline-secondary ms-2">
                                <i class="fa fa-times"></i> {{ __('Clear All') }}
                            </a>
                        </div>
                    @endif

                    <!-- Bulk Actions -->
                    <div class="d-flex align-items-center justify-content-between mb-3" id="bulk-actions" style="display: none !important;">
                        <div class="d-flex align-items-center">
                            <span class="text-muted me-3" id="selected-count">0 contacts selected</span>
                            <div class="btn-group" role="group" aria-label="Bulk Actions">
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="openCreateContactGroupModal()">
                                    <i class="fa fa-users"></i> {{ __('Add to Contact Group') }}
                                </button>
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="bulkDeleteContacts()">
                                    <i class="fa fa-trash"></i> {{ __('Delete Selected') }}
                                </button>
                            </div>
                        </div>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                            <i class="fa fa-times"></i> {{ __('Clear Selection') }}
                        </button>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped" id="contacts-table">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="select-all-contacts"></th>
                                    <th class="text-center">{{ __('Communication') }}</th>
                                    <th class="text-center">{{ __('Created on') }}</th>
                                    <th class="text-center">{{ __('Updated on') }}</th>
                                    <th class="text-center">{{ __('Source') }}</th>
                                    <th class="text-center">{{ __('Tags') }}</th>
                                    <th class="text-center">{{ __('Type') }}</th>
                                    <th class="text-center">{{ __('Group Name') }}</th>
                                    <th class="text-center">{{ __('Option') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Loading indicator for external contacts -->
                                <tr id="loading-external" style="display: none;">
                                    <td colspan="9" class="text-center py-4">
                                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </td>
                                </tr>
                                @forelse($contacts as $contact)
                                    <tr>
                                        <td class="text-center">
                                            <input type="checkbox" class="contact-checkbox" value="{{ $contact['id'] }}" data-type="{{ $contact['type'] }}" data-name="{{ $contact['name'] }}">
                                        </td>
                                        <td class="text-left">
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-3">
                                                    <div class="avatar-title bg-primary rounded-circle">
                                                        <i class="fas fa-user text-white"></i>
                                                    </div>
                                                </div>
                                                <div>
                                                    <strong>{{ $contact['name'] }}</strong><br>
                                                    @if($contact['email'])
                                                        <small class="text-muted">
                                                            <i class="fa fa-envelope"></i> {{ $contact['email'] }}
                                                        </small><br>
                                                    @endif
                                                    @if($contact['phone'])
                                                        <small class="text-muted">
                                                            <i class="fa fa-phone"></i> {{ $contact['phone'] }}
                                                        </small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            @if($contact['type'] == 'Lead')
                                                @php
                                                    $lead = \App\Models\Lead::find($contact['id']);
                                                    $createdDate = $lead ? $lead->created_at : now();
                                                @endphp
                                                {{ $createdDate->format('d/m/Y') }}<br>
                                                <small class="text-muted">{{ $createdDate->format('H:i:s A') }}</small>
                                            @elseif($contact['type'] == 'Deal')
                                                @php
                                                    $deal = \App\Models\Deal::find($contact['id']);
                                                    $createdDate = $deal ? $deal->created_at : now();
                                                @endphp
                                                {{ $createdDate->format('d/m/Y') }}<br>
                                                <small class="text-muted">{{ $createdDate->format('H:i:s A') }}</small>
                                            @else
                                                {{ now()->format('d/m/Y') }}<br>
                                                <small class="text-muted">{{ now()->format('H:i:s A') }}</small>
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            @if($contact['type'] == 'Lead')
                                                @php
                                                    $lead = \App\Models\Lead::find($contact['id']);
                                                    $updatedDate = $lead ? $lead->updated_at : now();
                                                @endphp
                                                {{ $updatedDate->format('d/m/Y') }}<br>
                                                <small class="text-muted">{{ $updatedDate->format('H:i:s A') }}</small>
                                            @elseif($contact['type'] == 'Deal')
                                                @php
                                                    $deal = \App\Models\Deal::find($contact['id']);
                                                    $updatedDate = $deal ? $deal->updated_at : now();
                                                @endphp
                                                {{ $updatedDate->format('d/m/Y') }}<br>
                                                <small class="text-muted">{{ $updatedDate->format('H:i:s A') }}</small>
                                            @else
                                                {{ now()->format('d/m/Y') }}<br>
                                                <small class="text-muted">{{ now()->format('H:i:s A') }}</small>
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            @if($contact['type'] == 'Lead')
                                                <span class="badge bg-info">Form</span>
                                            @elseif($contact['type'] == 'Deal')
                                                <span class="badge bg-warning">Manual</span>
                                            @else
                                                <span class="badge bg-success">Meta Form</span>
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            @if($contact['type'] == 'Lead')
                                                @php
                                                    $lead = \App\Models\Lead::find($contact['id']);
                                                    $labels = $lead ? $lead->labelsList() : collect();
                                                @endphp
                                                @if($labels->count() > 0)
                                                    @foreach($labels as $label)
                                                        <span class="badge bg-primary me-1">{{ $label->name }}</span>
                                                    @endforeach
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            @php
                                                $badgeClass = 'info';
                                                $displayType = $contact['type'];
                                                if ($contact['type'] == 'Lead') {
                                                    $badgeClass = 'success';
                                                } elseif ($contact['type'] == 'Deal') {
                                                    $badgeClass = 'info';
                                                } elseif ($contact['type'] == 'WhatsApp') {
                                                    $badgeClass = 'warning';
                                                    $displayType = 'External Contact';
                                                }
                                            @endphp
                                            <span class="badge bg-{{ $badgeClass }}">{{ $displayType }}</span>
                                        </td>
                                        <td class="text-center">
                                            @if($contact['type'] == 'Lead' && isset($contact['contact_group']) && $contact['contact_group'])
                                                <span class="badge bg-secondary">{{ $contact['contact_group'] }}</span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            <div class="action-btn">
                                                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    @if($contact['type'] == 'Lead')
                                                        <li><a class="dropdown-item" href="{{ route('leads.show', $contact['id']) }}"><i class="fa fa-eye me-2"></i>{{ __('View') }}</a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="previewContact('{{ $contact['id'] }}', 'lead')"><i class="fa fa-search me-2"></i>{{ __('Preview') }}</a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="editContact('{{ $contact['id'] }}', 'lead')"><i class="fa fa-edit me-2"></i>{{ __('Edit') }}</a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="openAddPipelineModal('{{ $contact['id'] }}', '{{ $contact['name'] }}')"><i class="fa fa-plus me-2"></i>{{ __('Add Pipeline') }}</a></li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteContact('{{ $contact['id'] }}', 'lead', '{{ $contact['name'] }}')"><i class="fa fa-trash me-2"></i>{{ __('Delete') }}</a></li>
                                                    @elseif($contact['type'] == 'Deal')
                                                        <li><a class="dropdown-item" href="{{ route('deals.show', $contact['id']) }}"><i class="fa fa-eye me-2"></i>{{ __('View') }}</a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="previewContact('{{ $contact['id'] }}', 'deal')"><i class="fa fa-search me-2"></i>{{ __('Preview') }}</a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="editContact('{{ $contact['id'] }}', 'deal')"><i class="fa fa-edit me-2"></i>{{ __('Edit') }}</a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="openAddPipelineModal('{{ $contact['id'] }}', '{{ $contact['name'] }}')"><i class="fa fa-plus me-2"></i>{{ __('Add Pipeline') }}</a></li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteContact('{{ $contact['id'] }}', 'deal', '{{ $contact['name'] }}')"><i class="fa fa-trash me-2"></i>{{ __('Delete') }}</a></li>
                                                    @elseif($contact['type'] == 'WhatsApp')
                                                        <li><a class="dropdown-item" href="#"><i class="fab fa-whatsapp me-2"></i>{{ __('External Contact') }}</a></li>
                                                    @endif
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="9" class="text-center py-4">
                                            <div class="text-center">
                                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                                <h5 class="text-muted">{{ __('No contacts found') }}</h5>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Contact Group Modal -->
    <div class="modal fade" id="createContactGroupModal" tabindex="-1" aria-labelledby="createContactGroupModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createContactGroupModalLabel">{{ __('Create Contact Group') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="createContactGroupForm" action="{{ route('contact-groups.store') }}" method="POST">
                        @csrf
                        <div class="mb-3">
                            <label for="contact_group_name" class="form-label">{{ __('Contact Group Name') }}</label>
                            <input type="text" class="form-control" id="contact_group_name" name="name" placeholder="Enter your contact group name" required>
                        </div>

                        <div class="text-center my-3">
                            <span class="text-muted">---{{ __('OR') }}---</span>
                        </div>

                        <div class="mb-3">
                            <label for="existing_contact_group" class="form-label">{{ __('Select Group') }}</label>
                            <select class="form-control" id="existing_contact_group" name="existing_group_id">
                                <option value="">{{ __('Select Contact Group') }}</option>
                            </select>
                        </div>

                        <input type="hidden" id="selected_contacts_input" name="selected_contacts">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                    <button type="button" class="btn btn-success" onclick="saveContactGroup()">{{ __('SAVE') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Pipeline Modal -->
    <div class="modal fade" id="addPipelineModal" tabindex="-1" aria-labelledby="addPipelineModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addPipelineModalLabel">{{ __('Add Contact to Pipeline') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addPipelineForm">
                        @csrf
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="pipeline_contact_name" class="form-label">{{ __('Contact Name') }}</label>
                                    <input type="text" class="form-control" id="pipeline_contact_name" name="contact_name" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="pipeline_select" class="form-label">{{ __('Pipeline') }} <span class="text-danger">*</span></label>
                                    <select class="form-control" id="pipeline_select" name="pipeline_id" required>
                                        <option value="">{{ __('Select Pipeline') }}</option>
                                        @foreach($pipelines as $pipeline)
                                            <option value="{{ $pipeline->id }}">{{ $pipeline->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="stage_select" class="form-label">{{ __('Stage') }} <span class="text-danger">*</span></label>
                                    <select class="form-control" id="stage_select" name="stage_id" required>
                                        <option value="">{{ __('Select Stage') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="pipeline_notes" class="form-label">{{ __('Notes') }}</label>
                                    <textarea class="form-control" id="pipeline_notes" name="notes" rows="3" placeholder="{{ __('Add any notes about this pipeline assignment...') }}"></textarea>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" id="pipeline_contact_id" name="contact_id">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="button" class="btn btn-primary" onclick="savePipelineAssignment()">{{ __('Add to Pipeline') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Off-canvas -->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="contactsFilter" aria-labelledby="contactsFilterLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="contactsFilterLabel">{{ __('Filter Contacts') }}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <form method="GET" action="{{ route('contacts.index') }}" id="contactsFilterForm">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="filter_name">{{ __('Name') }}</label>
                            <input type="text" class="form-control" name="filter_name" id="filter_name"
                                   value="{{ request('filter_name') }}" placeholder="{{ __('Enter name...') }}">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="filter_email">{{ __('Email') }}</label>
                            <input type="email" class="form-control" name="filter_email" id="filter_email"
                                   value="{{ request('filter_email') }}" placeholder="{{ __('Enter email...') }}">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="filter_phone">{{ __('Phone') }}</label>
                            <input type="text" class="form-control" name="filter_phone" id="filter_phone"
                                   value="{{ request('filter_phone') }}" placeholder="{{ __('Enter phone...') }}">
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="filter_type">{{ __('Type') }}</label>
                            <select class="form-control" name="filter_type" id="filter_type">
                                <option value="">{{ __('All Types') }}</option>
                                <option value="Lead" {{ request('filter_type') == 'Lead' ? 'selected' : '' }}>{{ __('Lead') }}</option>
                                <option value="Deal" {{ request('filter_type') == 'Deal' ? 'selected' : '' }}>{{ __('Deal') }}</option>
                                <option value="WhatsApp" {{ request('filter_type') == 'WhatsApp' ? 'selected' : '' }}>{{ __('External Contact') }}</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <button type="submit" class="btn btn-primary w-100">
                            {{ __('Apply Filter') }}
                        </button>
                    </div>
                    <div class="col-md-6">
                        <a href="{{ route('contacts.index') }}" class="btn btn-secondary w-100">
                            {{ __('Clear Filter') }}
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Contact Modal -->
    <div class="modal fade" id="editContactModal" tabindex="-1" aria-labelledby="editContactModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editContactModalLabel">{{ __('Edit Contact') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="editContactForm">
                        <!-- Form will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Contact Modal -->
    <div class="modal fade" id="deleteContactModal" tabindex="-1" aria-labelledby="deleteContactModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteContactModalLabel">{{ __('Delete Contact') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h5>{{ __('Are you sure?') }}</h5>
                        <p class="text-muted" id="deleteContactMessage">
                            {{ __('This action cannot be undone.') }}
                        </p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">{{ __('Delete') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Preview Contact Modal -->
    <div class="modal fade" id="previewContactModal" tabindex="-1" aria-labelledby="previewContactModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="previewContactModalLabel">{{ __('Contact Preview') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="previewContactContent">
                        <!-- Content will be loaded here -->
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 text-muted">{{ __('Loading contact information...') }}</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                    <button type="button" class="btn btn-primary" id="viewFullContactBtn" style="display: none;">
                        <i class="fa fa-external-link-alt me-2"></i>{{ __('View Full Details') }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Appointment Booking Modal -->
    <div class="modal fade" id="appointmentBookingModal" tabindex="-1" aria-labelledby="appointmentBookingModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="appointmentBookingModalLabel">{{ __('Appointment Booking') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="appointmentBookingForm">
                        @csrf
                        <!-- Contact Name Section -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="appointment_contact_name" class="form-label">{{ __('Contact Name') }}</label>
                                    <input type="text" class="form-control" id="appointment_contact_name" name="contact_name" placeholder="Contact Name" readonly>
                                </div>
                            </div>
                        </div>

                        <!-- Warning Message -->
                        <div class="alert alert-warning d-none" id="appointment_warning" role="alert">
                            <strong>{{ __('Warning!') }}</strong> {{ __('Please select at least one contact.') }}
                        </div>

                        <!-- Calendar Event Section -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="calendar_event_select" class="form-label">{{ __('Calendar Event') }}</label>
                                    <select class="form-control" id="calendar_event_select" name="event_id" required>
                                        <option value="">{{ __('Select Event') }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Event Location and Value Section -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="event_location_select" class="form-label">{{ __('Event Location') }}</label>
                                    <select class="form-control" id="event_location_select" name="event_location" required>
                                        <option value="">{{ __('Please select event') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="event_location_value" class="form-label">{{ __('Event Location Value') }}</label>
                                    <input type="text" class="form-control" id="event_location_value" name="event_location_value" readonly>
                                </div>
                            </div>
                        </div>

                        <!-- Event Date and Timezone Section -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="event_date" class="form-label">{{ __('Event Date') }}</label>
                                    <input type="date" class="form-control" id="event_date" name="event_date" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="timezone_select" class="form-label">{{ __('Timezone') }}</label>
                                    <select class="form-control" id="timezone_select" name="time_zone" required>
                                        <option value="Asia/Calcutta">Asia/Calcutta</option>
                                        <option value="America/New_York">America/New_York</option>
                                        <option value="Europe/London">Europe/London</option>
                                        <option value="Asia/Tokyo">Asia/Tokyo</option>
                                        <option value="Australia/Sydney">Australia/Sydney</option>
                                        <option value="America/Los_Angeles">America/Los_Angeles</option>
                                        <option value="Europe/Paris">Europe/Paris</option>
                                        <option value="Asia/Dubai">Asia/Dubai</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Timeslots Section -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="timeslots_select" class="form-label">{{ __('Timeslots') }}</label>
                                    <select class="form-control" id="timeslots_select" name="time_slots" required>
                                        <option value="">{{ __('Please select event and date') }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <input type="hidden" id="selected_contact_ids" name="selected_contact_ids">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                    <button type="button" class="btn btn-success" id="saveAppointmentBtn">{{ __('ADD') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Convert to Lead Modal -->
    <div class="modal fade" id="convertToLeadModal" tabindex="-1" aria-labelledby="convertToLeadModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="convertToLeadModalLabel">{{ __('Convert to Lead') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="convertToLeadForm">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="contact_name" class="form-label">{{ __('Contact Name') }}</label>
                                    <input type="text" class="form-control" id="contact_name" name="contact_name" readonly>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="contact_email" class="form-label">{{ __('Contact Email') }}</label>
                                    <input type="email" class="form-control" id="contact_email" name="contact_email" readonly>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="contact_phone" class="form-label">{{ __('Contact Phone') }}</label>
                                    <input type="text" class="form-control" id="contact_phone" name="contact_phone" readonly>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="pipeline_id" class="form-label">{{ __('Pipeline') }} <span class="text-danger">*</span></label>
                                    <select class="form-control" id="pipeline_id" name="pipeline_id" required>
                                        <option value="">{{ __('Select Pipeline') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="stage_id" class="form-label">{{ __('Stage') }} <span class="text-danger">*</span></label>
                                    <select class="form-control" id="stage_id" name="stage_id" required>
                                        <option value="">{{ __('Select Stage') }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" id="external_id" name="external_id">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="button" class="btn btn-primary" id="confirmConvertBtn">{{ __('Convert to Lead') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add New Contact Modal -->
    <div class="modal fade" id="addContactModal" tabindex="-1" aria-labelledby="addContactModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addContactModalLabel">{{ __('Add New Contact') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addContactForm" action="{{ route('leads.store') }}" method="POST">
                        @csrf
                        <!-- Personal Information Section -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="name" class="form-label text-muted">{{ __('Full Name') }} <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="name" id="name" placeholder="Enter full name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="email" class="form-label text-muted">{{ __('Email') }}</label>
                                    <input type="email" class="form-control" name="email" id="email" placeholder="<EMAIL>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="phone" class="form-label text-muted">{{ __('Phone') }}</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <img src="https://flagcdn.com/w20/in.png" alt="India" width="20" class="me-1">
                                            +91
                                        </span>
                                        <input type="text" class="form-control" name="phone" id="phone" placeholder="93737 27148">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="date_of_birth" class="form-label text-muted">{{ __('Date of Birth') }}</label>
                                    <input type="date" class="form-control" name="date_of_birth" id="date_of_birth" placeholder="dd-mm-yyyy">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="contact_type" class="form-label text-muted">{{ __('Contact Type') }}</label>
                                    <select class="form-control" name="contact_type" id="contact_type">
                                        <option value="Lead" selected>{{ __('Lead') }}</option>
                                        <option value="Customer">{{ __('Customer') }}</option>
                                        <option value="Prospect">{{ __('Prospect') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="tags" class="form-label text-muted">{{ __('Tags') }}</label>
                                    <input type="text" class="form-control" name="tags" id="tags" placeholder="Write & hit enter to add or select tags">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="postal_code" class="form-label text-muted">{{ __('Postal Code') }}</label>
                                    <input type="text" class="form-control" name="postal_code" id="postal_code" placeholder="Postal Code">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="city" class="form-label text-muted">{{ __('City') }}</label>
                                    <input type="text" class="form-control" name="city" id="city" placeholder="City">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="state" class="form-label text-muted">{{ __('State') }}</label>
                                    <input type="text" class="form-control" name="state" id="state" placeholder="State">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group mb-4">
                                    <label for="country" class="form-label text-muted">{{ __('Country') }}</label>
                                    <input type="text" class="form-control" name="country" id="country" placeholder="Country">
                                </div>
                            </div>
                        </div>

                        <!-- Business Details Section -->
                        <div class="row">
                            <div class="col-12">
                                <hr class="my-4">
                                <h6 class="text-center text-muted mb-4">{{ __('Business Details') }}</h6>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="business_name" class="form-label text-muted">{{ __('Business Name') }}</label>
                                    <input type="text" class="form-control" name="business_name" id="business_name" placeholder="Enter Business Name">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="business_gst" class="form-label text-muted">{{ __('Business GST No') }}</label>
                                    <input type="text" class="form-control" name="business_gst" id="business_gst" placeholder="Enter Business GST No">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="business_state" class="form-label text-muted">{{ __('Business State') }}</label>
                                    <select class="form-control" name="business_state" id="business_state">
                                        <option value="">{{ __('Select State') }}</option>
                                        <option value="Andhra Pradesh">Andhra Pradesh</option>
                                        <option value="Arunachal Pradesh">Arunachal Pradesh</option>
                                        <option value="Assam">Assam</option>
                                        <option value="Bihar">Bihar</option>
                                        <option value="Chhattisgarh">Chhattisgarh</option>
                                        <option value="Goa">Goa</option>
                                        <option value="Gujarat">Gujarat</option>
                                        <option value="Haryana">Haryana</option>
                                        <option value="Himachal Pradesh">Himachal Pradesh</option>
                                        <option value="Jharkhand">Jharkhand</option>
                                        <option value="Karnataka">Karnataka</option>
                                        <option value="Kerala">Kerala</option>
                                        <option value="Madhya Pradesh">Madhya Pradesh</option>
                                        <option value="Maharashtra">Maharashtra</option>
                                        <option value="Manipur">Manipur</option>
                                        <option value="Meghalaya">Meghalaya</option>
                                        <option value="Mizoram">Mizoram</option>
                                        <option value="Nagaland">Nagaland</option>
                                        <option value="Odisha">Odisha</option>
                                        <option value="Punjab">Punjab</option>
                                        <option value="Rajasthan">Rajasthan</option>
                                        <option value="Sikkim">Sikkim</option>
                                        <option value="Tamil Nadu">Tamil Nadu</option>
                                        <option value="Telangana">Telangana</option>
                                        <option value="Tripura">Tripura</option>
                                        <option value="Uttar Pradesh">Uttar Pradesh</option>
                                        <option value="Uttarakhand">Uttarakhand</option>
                                        <option value="West Bengal">West Bengal</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="business_postal_code" class="form-label text-muted">{{ __('Business Postal Code') }}</label>
                                    <input type="text" class="form-control" name="business_postal_code" id="business_postal_code" placeholder="Enter Business Postal Code">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group mb-4">
                                    <label for="business_address" class="form-label text-muted">{{ __('Business Address') }}</label>
                                    <textarea class="form-control" name="business_address" id="business_address" rows="3" placeholder="Enter Business Address"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- DND Settings Section -->
                        <div class="row">
                            <div class="col-12">
                                <hr class="my-4">
                                <h6 class="text-center text-muted mb-4">{{ __('DND Settings') }}</h6>
                            </div>
                            <div class="col-12">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="dnd_all" id="dnd_all">
                                    <label class="form-check-label text-muted" for="dnd_all">
                                        {{ __('DND all channels') }}
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="dnd_emails" id="dnd_emails">
                                    <label class="form-check-label text-muted" for="dnd_emails">
                                        <i class="fas fa-envelope me-2"></i>{{ __('Emails') }}
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="dnd_whatsapp" id="dnd_whatsapp">
                                    <label class="form-check-label text-muted" for="dnd_whatsapp">
                                        <i class="fab fa-whatsapp me-2"></i>{{ __('Whatsapp') }}
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="dnd_sms" id="dnd_sms">
                                    <label class="form-check-label text-muted" for="dnd_sms">
                                        <i class="fas fa-sms me-2"></i>{{ __('Text Messages') }}
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="dnd_calls" id="dnd_calls">
                                    <label class="form-check-label text-muted" for="dnd_calls">
                                        <i class="fas fa-phone me-2"></i>{{ __('Calls & Voicemails') }}
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Hidden fields for lead creation -->
                        <input type="hidden" name="user_id" value="{{ Auth::user()->id }}">
                        <input type="hidden" name="subject" value="New Contact">
                        <input type="hidden" name="pipeline_id" id="hidden_pipeline_id">
                        <input type="hidden" name="stage_id" id="hidden_stage_id">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="button" class="btn btn-primary" onclick="saveNewContact()">{{ __('Save Contact') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Group Modal -->
    <div class="modal fade" id="contactGroupModal" tabindex="-1" aria-labelledby="contactGroupModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="contactGroupModalLabel">{{ __('Add to Contact Group') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group mb-3">
                        <label for="contact_group">{{ __('Select Contact Group') }}</label>
                        <select class="form-control" id="contact_group">
                            <option value="">{{ __('Select Group') }}</option>
                            <option value="customers">{{ __('Customers') }}</option>
                            <option value="prospects">{{ __('Prospects') }}</option>
                            <option value="partners">{{ __('Partners') }}</option>
                        </select>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ __('Select contacts from the table and choose a group to add them to.') }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="button" class="btn btn-primary" onclick="addToContactGroup()">{{ __('Add to Group') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Workflow Modal -->
    <div class="modal fade" id="workflowModal" tabindex="-1" aria-labelledby="workflowModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="workflowModalLabel">{{ __('Add to Workflow') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group mb-3">
                        <label for="workflow_select">{{ __('Select Workflow') }}</label>
                        <select class="form-control" id="workflow_select">
                            <option value="">{{ __('Select Workflow') }}</option>
                            <option value="welcome_series">{{ __('Welcome Series') }}</option>
                            <option value="follow_up">{{ __('Follow Up Sequence') }}</option>
                            <option value="nurture">{{ __('Nurture Campaign') }}</option>
                        </select>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ __('Select contacts from the table and choose a workflow to add them to.') }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="button" class="btn btn-primary" onclick="addToWorkflow()">{{ __('Add to Workflow') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Tag Modal -->
    <div class="modal fade" id="tagModal" tabindex="-1" aria-labelledby="tagModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="tagModalLabel">{{ __('Add Tag') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group mb-3">
                        <label for="tag_input">{{ __('Tags') }}</label>
                        <input type="text" class="form-control" id="tag_input" placeholder="{{ __('Enter tags separated by commas') }}">
                        <small class="form-text text-muted">{{ __('Example: VIP, Customer, Hot Lead') }}</small>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ __('Select contacts from the table and add tags to organize them.') }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="button" class="btn btn-primary" onclick="addTags()">{{ __('Add Tags') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Appointment Modal -->
    <div class="modal fade" id="appointmentModal" tabindex="-1" aria-labelledby="appointmentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="appointmentModalLabel">{{ __('Appointment Booking') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="appointmentForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="appointment_title">{{ __('Appointment Title') }}</label>
                                    <input type="text" class="form-control" id="appointment_title" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="appointment_date">{{ __('Date') }}</label>
                                    <input type="date" class="form-control" id="appointment_date" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="appointment_time">{{ __('Time') }}</label>
                                    <input type="time" class="form-control" id="appointment_time" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="appointment_duration">{{ __('Duration (minutes)') }}</label>
                                    <select class="form-control" id="appointment_duration">
                                        <option value="30">30 minutes</option>
                                        <option value="60">1 hour</option>
                                        <option value="90">1.5 hours</option>
                                        <option value="120">2 hours</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    <label for="appointment_notes">{{ __('Notes') }}</label>
                                    <textarea class="form-control" id="appointment_notes" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="button" class="btn btn-primary" onclick="bookAppointment()">{{ __('Book Appointment') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Import Modal -->
    <div class="modal fade" id="bulkImportModal" tabindex="-1" aria-labelledby="bulkImportModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="bulkImportModalLabel">{{ __('Bulk Import Contacts') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="bulkImportForm" enctype="multipart/form-data">
                        @csrf
                        <div class="form-group mb-3">
                            <label for="import_file">{{ __('Choose CSV File') }}</label>
                            <input type="file" class="form-control" id="import_file" accept=".csv,.xlsx,.xls" required>
                            <small class="form-text text-muted">{{ __('Supported formats: CSV, Excel (.xlsx, .xls)') }}</small>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            {{ __('Make sure your file has columns: Name, Email, Phone, etc.') }}
                        </div>
                        <div class="form-group mb-3">
                            <a href="#" class="btn btn-outline-primary btn-sm" onclick="downloadTemplate()">
                                <i class="fas fa-download me-2"></i>{{ __('Download Template') }}
                            </a>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="button" class="btn btn-primary" onclick="importContacts()">{{ __('Import') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Modal -->
    <div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="searchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="searchModalLabel">{{ __('Advanced Search') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="advancedSearchForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="search_name">{{ __('Name') }}</label>
                                    <input type="text" class="form-control" id="search_name" placeholder="{{ __('Search by name') }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="search_email">{{ __('Email') }}</label>
                                    <input type="email" class="form-control" id="search_email" placeholder="{{ __('Search by email') }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="search_phone">{{ __('Phone') }}</label>
                                    <input type="text" class="form-control" id="search_phone" placeholder="{{ __('Search by phone') }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="search_type">{{ __('Type') }}</label>
                                    <select class="form-control" id="search_type">
                                        <option value="">{{ __('All Types') }}</option>
                                        <option value="Lead">{{ __('Lead') }}</option>
                                        <option value="Deal">{{ __('Deal') }}</option>
                                        <option value="WhatsApp">{{ __('External Contact') }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="search_date_from">{{ __('Created From') }}</label>
                                    <input type="date" class="form-control" id="search_date_from">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="search_date_to">{{ __('Created To') }}</label>
                                    <input type="date" class="form-control" id="search_date_to">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="clearAdvancedSearch()">{{ __('Clear') }}</button>
                    <button type="button" class="btn btn-primary" onclick="performAdvancedSearch()">{{ __('Search') }}</button>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Action Buttons Styling */
        .action-buttons-group {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-right: 10px;
        }

        .btn-action-modern {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            background: #ffffff;
            color: #64748b;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .btn-action-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s ease;
        }

        .btn-action-modern:hover::before {
            left: 100%;
        }

        .btn-action-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: var(--bs-primary);
            background: rgba(var(--bs-primary-rgb), 0.1);
        }

        /* Avatar styling */
        .avatar-sm {
            width: 40px;
            height: 40px;
        }

        .avatar-title {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }

        /* Table improvements */
        .table th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            color: #495057;
        }

        .table td {
            vertical-align: middle;
            padding: 12px 8px;
        }

        /* Checkbox styling */
        .contact-checkbox, #select-all-contacts {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        /* Badge improvements */
        .badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
        }

        /* Dropdown improvements */
        .dropdown-menu {
            border: 1px solid #e2e8f0;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        /* Modal improvements */
        .modal-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e2e8f0;
        }

        .modal-title {
            font-weight: 600;
            color: #1e293b;
        }

        /* Form improvements */
        .form-control:focus {
            border-color: var(--bs-primary);
            box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .action-buttons-group {
                flex-wrap: wrap;
                gap: 4px;
            }

            .btn-action-modern {
                width: 35px;
                height: 35px;
                font-size: 14px;
            }
        }

        /* External Contacts Loading Styles */
        .external-loading-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px dashed #dee2e6;
            border-radius: 12px;
            padding: 2rem;
            margin: 1rem 0;
            position: relative;
            overflow: hidden;
        }

        .external-loading-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .external-loading-content {
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .external-loading-spinner {
            margin-bottom: 1rem;
        }

        .external-loading-spinner .spinner-border {
            width: 2.5rem;
            height: 2.5rem;
            border-width: 0.3em;
            animation: spin 1s linear infinite;
        }

        .external-loading-text {
            font-size: 1.1rem;
            color: #495057;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .external-loading-text i {
            font-size: 1.2rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .external-loading-subtext {
            color: #6c757d;
            font-style: italic;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .external-loading-container {
                padding: 1.5rem;
                margin: 0.5rem 0;
            }

            .external-loading-text {
                font-size: 1rem;
            }

            .external-loading-spinner .spinner-border {
                width: 2rem;
                height: 2rem;
            }
        }
    </style>
@endsection

@push('script-page')
    <script>
        // Pass pipelines data to JavaScript
        const pipelines = @json($pipelines);
    </script>
    <script>
        function editContact(id, type) {
            // Get current contact data from the table row
            let row = $(`button[onclick="editContact('${id}', '${type}')"]`).closest('tr');
            let name = row.find('strong').text();
            let email = row.find('i.fa-envelope').parent().text().replace('✉', '').trim();
            let phone = row.find('i.fa-phone').parent().text().replace('📞', '').trim();

            // Create form based on type
            let formHtml = '';
            let actionUrl = '';

            if (type === 'lead') {
                actionUrl = `/contacts/leads/${id}`;
                formHtml = `
                    <form id="editContactForm" action="${actionUrl}" method="POST">
                        @csrf
                        <input type="hidden" name="_method" value="PUT">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="name">{{ __('Name') }} <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="name" value="${name}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email">{{ __('Email') }}</label>
                                    <input type="email" class="form-control" name="email" value="${email}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="phone">{{ __('Phone') }}</label>
                                    <input type="text" class="form-control" name="phone" value="${phone}">
                                </div>
                            </div>
                        </div>
                    </form>
                `;
            } else {
                actionUrl = `/contacts/deals/${id}`;
                formHtml = `
                    <form id="editContactForm" action="${actionUrl}" method="POST">
                        @csrf
                        <input type="hidden" name="_method" value="PUT">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="name">{{ __('Name') }} <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="name" value="${name}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="phone">{{ __('Phone') }}</label>
                                    <input type="text" class="form-control" name="phone" value="${phone}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="price">{{ __('Price') }}</label>
                                    <input type="number" class="form-control" name="price" step="0.01">
                                </div>
                            </div>
                        </div>
                    </form>
                `;
            }

            // Set modal content
            $('#editContactForm').html(formHtml);

            // Add modal footer with buttons
            let modalFooter = `
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="button" class="btn btn-primary" onclick="updateContact('${type}')">{{ __('Update') }}</button>
                </div>
            `;

            $('.modal-content').find('.modal-footer').remove();
            $('.modal-content').append(modalFooter);

            // Show modal
            $('#editContactModal').modal('show');
        }

        function updateContact(type) {
            let form = $('#editContactForm form')[0];
            let formData = new FormData(form);

            // Show loading on submit button
            let submitBtn = $('.modal-footer .btn-primary');
            let originalText = submitBtn.text();
            submitBtn.html('<i class="fa fa-spinner fa-spin"></i> {{ __("Updating...") }}').prop('disabled', true);

            // Clear previous errors
            $('.alert-danger').remove();

            $.ajax({
                url: form.action,
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    $('#editContactModal').modal('hide');

                    // Show success message
                    $('body').append(`
                        <div class="alert alert-success alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
                            <strong>{{ __('Success!') }}</strong> ${response.success}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    `);

                    // Auto hide success message
                    setTimeout(function() {
                        $('.alert-success').fadeOut();
                    }, 3000);

                    // Reload the page to show updated data
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                },
                error: function(xhr) {
                    let errors = xhr.responseJSON?.errors || {};
                    let errorHtml = '<div class="alert alert-danger"><ul class="mb-0">';

                    if (Object.keys(errors).length > 0) {
                        Object.values(errors).forEach(function(error) {
                            errorHtml += `<li>${error[0]}</li>`;
                        });
                    } else {
                        errorHtml += '<li>{{ __("An error occurred while updating") }}</li>';
                    }

                    errorHtml += '</ul></div>';

                    $('#editContactForm').prepend(errorHtml);

                    // Reset submit button
                    submitBtn.text(originalText).prop('disabled', false);
                }
            });
        }

        function previewContact(id, type) {
            // Show the modal
            $('#previewContactModal').modal('show');

            // Reset modal content
            $('#previewContactContent').html(`
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">{{ __('Loading contact information...') }}</p>
                </div>
            `);

            // Hide the view full button initially
            $('#viewFullContactBtn').hide();

            // Determine the API endpoint based on type
            let apiUrl = '';
            let fullViewUrl = '';

            if (type === 'lead') {
                apiUrl = `/api/leads/${id}`;
                fullViewUrl = `/leads/${id}`;
            } else if (type === 'deal') {
                apiUrl = `/api/deals/${id}`;
                fullViewUrl = `/deals/${id}`;
            }

            // Fetch contact data
            $.ajax({
                url: apiUrl,
                method: 'GET',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'Accept': 'application/json'
                },
                success: function(response) {
                    if (response.success && response.data) {
                        const contact = response.data;
                        renderContactPreview(contact, type);

                        // Show and configure the view full button
                        $('#viewFullContactBtn').show().off('click').on('click', function() {
                            window.open(fullViewUrl, '_blank');
                        });
                    } else {
                        showPreviewError('{{ __("Failed to load contact information") }}');
                    }
                },
                error: function(xhr) {
                    console.error('Preview error:', xhr);
                    let errorMessage = '{{ __("Failed to load contact information") }}';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    showPreviewError(errorMessage);
                }
            });
        }

        function renderContactPreview(contact, type) {
            const typeLabel = type === 'lead' ? '{{ __("Lead") }}' : '{{ __("Deal") }}';
            const typeBadgeClass = type === 'lead' ? 'bg-success' : 'bg-info';

            // Helper function to format date
            const formatDate = (dateString) => {
                if (!dateString) return '{{ __("Not Available") }}';
                return new Date(dateString).toLocaleDateString();
            };

            // Helper function to get DND status
            const getDndStatus = (dndParsed, channel) => {
                if (!dndParsed) return false;
                return dndParsed[channel] === true || dndParsed[channel] === 'true';
            };

            const html = `
                <div class="container-fluid">
                    <!-- Contact Header -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex align-items-center">
                                <div class="avatar-lg me-3">
                                    <div class="avatar-title bg-primary rounded-circle">
                                        <i class="fas fa-user text-white fs-3"></i>
                                    </div>
                                </div>
                                <div>
                                    <h4 class="mb-1">${contact.name || '{{ __("No Name") }}'}</h4>
                                    <span class="badge ${typeBadgeClass} fs-6">${typeLabel}</span>
                                    ${contact.contact_type ? `<span class="badge bg-secondary ms-2">${contact.contact_type}</span>` : ''}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Personal Information Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3"><i class="fas fa-user me-2"></i>{{ __('Personal Information') }}</h5>
                        </div>
                        <div class="col-lg-4 col-md-6 col-12 mb-3">
                            <div class="card h-100 border-0" style="box-shadow: 0 2px 10px rgba(0,128,0,0.15);">
                                <div class="card-body">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center justify-content-center rounded-circle bg-primary bg-gradient" style="width: 40px; height: 40px;">
                                            <i class="fas fa-envelope text-white"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h6 class="mb-1 text-dark fw-semibold">{{ __('Email') }}</h6>
                                            <p class="text-muted mb-0 text-break small">
                                                ${contact.email || '{{ __("Not Available") }}'}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6 col-12 mb-3">
                            <div class="card h-100 border-0" style="box-shadow: 0 2px 10px rgba(0,128,0,0.15);">
                                <div class="card-body">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center justify-content-center rounded-circle bg-success bg-gradient" style="width: 40px; height: 40px;">
                                            <i class="fas fa-phone text-white"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h6 class="mb-1 text-dark fw-semibold">{{ __('Phone') }}</h6>
                                            <p class="text-muted mb-0 text-break small">
                                                ${contact.phone || '{{ __("Not Available") }}'}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6 col-12 mb-3">
                            <div class="card h-100 border-0" style="box-shadow: 0 2px 10px rgba(0,128,0,0.15);">
                                <div class="card-body">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center justify-content-center rounded-circle bg-info bg-gradient" style="width: 40px; height: 40px;">
                                            <i class="fas fa-birthday-cake text-white"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h6 class="mb-1 text-dark fw-semibold">{{ __('Date of Birth') }}</h6>
                                            <p class="text-muted mb-0 text-break small">
                                                ${formatDate(contact.date_of_birth)}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6 col-12 mb-3">
                            <div class="card h-100 border-0" style="box-shadow: 0 2px 10px rgba(0,128,0,0.15);">
                                <div class="card-body">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center justify-content-center rounded-circle bg-warning bg-gradient" style="width: 40px; height: 40px;">
                                            <i class="fas fa-tags text-white"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h6 class="mb-1 text-dark fw-semibold">{{ __('Tags') }}</h6>
                                            <p class="text-muted mb-0 text-break small">
                                                ${contact.tags || '{{ __("No Tags") }}'}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6 col-12 mb-3">
                            <div class="card h-100 border-0" style="box-shadow: 0 2px 10px rgba(0,128,0,0.15);">
                                <div class="card-body">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center justify-content-center rounded-circle bg-secondary bg-gradient" style="width: 40px; height: 40px;">
                                            <i class="fas fa-map-marker-alt text-white"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h6 class="mb-1 text-dark fw-semibold">{{ __('Location') }}</h6>
                                            <p class="text-muted mb-0 text-break small">
                                                ${[contact.city, contact.state, contact.country].filter(Boolean).join(', ') || '{{ __("Not Available") }}'}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-6 col-12 mb-3">
                            <div class="card h-100 border-0" style="box-shadow: 0 2px 10px rgba(0,128,0,0.15);">
                                <div class="card-body">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center justify-content-center rounded-circle bg-dark bg-gradient" style="width: 40px; height: 40px;">
                                            <i class="fas fa-mail-bulk text-white"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h6 class="mb-1 text-dark fw-semibold">{{ __('Postal Code') }}</h6>
                                            <p class="text-muted mb-0 text-break small">
                                                ${contact.postal_code || '{{ __("Not Available") }}'}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Business Information Section -->
                    ${contact.business_name || contact.business_gst || contact.business_state || contact.business_postal_code || contact.business_address ? `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3"><i class="fas fa-building me-2"></i>{{ __('Business Information') }}</h5>
                        </div>
                        ${contact.business_name ? `
                        <div class="col-lg-6 col-md-6 col-12 mb-3">
                            <div class="card h-100 border-0" style="box-shadow: 0 2px 10px rgba(0,128,0,0.15);">
                                <div class="card-body">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center justify-content-center rounded-circle bg-primary bg-gradient" style="width: 40px; height: 40px;">
                                            <i class="fas fa-building text-white"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h6 class="mb-1 text-dark fw-semibold">{{ __('Business Name') }}</h6>
                                            <p class="text-muted mb-0 text-break small">${contact.business_name}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        ` : ''}

                        ${contact.business_gst ? `
                        <div class="col-lg-6 col-md-6 col-12 mb-3">
                            <div class="card h-100 border-0" style="box-shadow: 0 2px 10px rgba(0,128,0,0.15);">
                                <div class="card-body">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center justify-content-center rounded-circle bg-success bg-gradient" style="width: 40px; height: 40px;">
                                            <i class="fas fa-file-invoice text-white"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h6 class="mb-1 text-dark fw-semibold">{{ __('Business GST') }}</h6>
                                            <p class="text-muted mb-0 text-break small">${contact.business_gst}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        ` : ''}

                        ${contact.business_state ? `
                        <div class="col-lg-6 col-md-6 col-12 mb-3">
                            <div class="card h-100 border-0" style="box-shadow: 0 2px 10px rgba(0,128,0,0.15);">
                                <div class="card-body">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center justify-content-center rounded-circle bg-info bg-gradient" style="width: 40px; height: 40px;">
                                            <i class="fas fa-map text-white"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h6 class="mb-1 text-dark fw-semibold">{{ __('Business State') }}</h6>
                                            <p class="text-muted mb-0 text-break small">${contact.business_state}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        ` : ''}

                        ${contact.business_postal_code ? `
                        <div class="col-lg-6 col-md-6 col-12 mb-3">
                            <div class="card h-100 border-0" style="box-shadow: 0 2px 10px rgba(0,128,0,0.15);">
                                <div class="card-body">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center justify-content-center rounded-circle bg-warning bg-gradient" style="width: 40px; height: 40px;">
                                            <i class="fas fa-mail-bulk text-white"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h6 class="mb-1 text-dark fw-semibold">{{ __('Business Postal Code') }}</h6>
                                            <p class="text-muted mb-0 text-break small">${contact.business_postal_code}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        ` : ''}

                        ${contact.business_address ? `
                        <div class="col-12 mb-3">
                            <div class="card border-0" style="box-shadow: 0 2px 10px rgba(0,128,0,0.15);">
                                <div class="card-body">
                                    <div class="d-flex align-items-start gap-3">
                                        <div class="d-flex align-items-center justify-content-center rounded-circle bg-secondary bg-gradient" style="width: 40px; height: 40px;">
                                            <i class="fas fa-address-card text-white"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h6 class="mb-1 text-dark fw-semibold">{{ __('Business Address') }}</h6>
                                            <p class="text-muted mb-0 small">${contact.business_address}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                    ` : ''}

                    <!-- Pipeline & Stage Information -->
                    ${type === 'lead' || type === 'deal' ? `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3"><i class="fas fa-project-diagram me-2"></i>{{ __('Pipeline & Stage') }}</h5>
                        </div>
                        <div class="col-lg-6 col-md-6 col-12 mb-3">
                            <div class="card h-100 border-0" style="box-shadow: 0 2px 10px rgba(0,128,0,0.15);">
                                <div class="card-body">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center justify-content-center rounded-circle bg-primary bg-gradient" style="width: 40px; height: 40px;">
                                            <i class="fas fa-project-diagram text-white"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h6 class="mb-1 text-dark fw-semibold">{{ __('Pipeline') }}</h6>
                                            <p class="text-muted mb-0 text-break small">
                                                ${contact.pipeline ? contact.pipeline.name : '{{ __("Not Assigned") }}'}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 col-md-6 col-12 mb-3">
                            <div class="card h-100 border-0" style="box-shadow: 0 2px 10px rgba(0,128,0,0.15);">
                                <div class="card-body">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center justify-content-center rounded-circle bg-success bg-gradient" style="width: 40px; height: 40px;">
                                            <i class="fas fa-layer-group text-white"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h6 class="mb-1 text-dark fw-semibold">{{ __('Stage') }}</h6>
                                            <p class="text-muted mb-0 text-break small">
                                                ${contact.stage ? contact.stage.name : '{{ __("Not Assigned") }}'}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    ` : ''}

                    <!-- DND Settings Section -->
                    ${contact.dnd_parsed ? `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3"><i class="fas fa-ban me-2"></i>{{ __('DND Settings') }}</h5>
                        </div>
                        <div class="col-12">
                            <div class="card border-0" style="box-shadow: 0 2px 10px rgba(0,128,0,0.15);">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-lg-3 col-md-6 col-12 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-envelope me-2 ${getDndStatus(contact.dnd_parsed, 'dnd_emails') ? 'text-danger' : 'text-success'}"></i>
                                                <span class="small ${getDndStatus(contact.dnd_parsed, 'dnd_emails') ? 'text-danger' : 'text-success'}">
                                                    {{ __('Emails') }}: ${getDndStatus(contact.dnd_parsed, 'dnd_emails') ? '{{ __("Blocked") }}' : '{{ __("Allowed") }}'}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-12 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="fab fa-whatsapp me-2 ${getDndStatus(contact.dnd_parsed, 'dnd_whatsapp') ? 'text-danger' : 'text-success'}"></i>
                                                <span class="small ${getDndStatus(contact.dnd_parsed, 'dnd_whatsapp') ? 'text-danger' : 'text-success'}">
                                                    {{ __('WhatsApp') }}: ${getDndStatus(contact.dnd_parsed, 'dnd_whatsapp') ? '{{ __("Blocked") }}' : '{{ __("Allowed") }}'}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-12 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-sms me-2 ${getDndStatus(contact.dnd_parsed, 'dnd_sms') ? 'text-danger' : 'text-success'}"></i>
                                                <span class="small ${getDndStatus(contact.dnd_parsed, 'dnd_sms') ? 'text-danger' : 'text-success'}">
                                                    {{ __('SMS') }}: ${getDndStatus(contact.dnd_parsed, 'dnd_sms') ? '{{ __("Blocked") }}' : '{{ __("Allowed") }}'}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-12 mb-2">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-phone me-2 ${getDndStatus(contact.dnd_parsed, 'dnd_calls') ? 'text-danger' : 'text-success'}"></i>
                                                <span class="small ${getDndStatus(contact.dnd_parsed, 'dnd_calls') ? 'text-danger' : 'text-success'}">
                                                    {{ __('Calls') }}: ${getDndStatus(contact.dnd_parsed, 'dnd_calls') ? '{{ __("Blocked") }}' : '{{ __("Allowed") }}'}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    ` : ''}

                    <!-- Notes Section -->
                    ${contact.notes ? `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3"><i class="fas fa-sticky-note me-2"></i>{{ __('Notes') }}</h5>
                        </div>
                        <div class="col-12">
                            <div class="card border-0" style="box-shadow: 0 2px 10px rgba(0,128,0,0.15);">
                                <div class="card-body">
                                    <div class="text-muted">${contact.notes}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    ` : ''}

                    <!-- System Information -->
                    <div class="row">
                        <div class="col-12">
                            <h5 class="text-primary mb-3"><i class="fas fa-info-circle me-2"></i>{{ __('System Information') }}</h5>
                        </div>
                        <div class="col-lg-6 col-md-6 col-12 mb-3">
                            <div class="card h-100 border-0" style="box-shadow: 0 2px 10px rgba(0,128,0,0.15);">
                                <div class="card-body">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center justify-content-center rounded-circle bg-info bg-gradient" style="width: 40px; height: 40px;">
                                            <i class="fas fa-calendar-plus text-white"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h6 class="mb-1 text-dark fw-semibold">{{ __('Created Date') }}</h6>
                                            <p class="text-muted mb-0 text-break small">
                                                ${formatDate(contact.created_at)}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 col-md-6 col-12 mb-3">
                            <div class="card h-100 border-0" style="box-shadow: 0 2px 10px rgba(0,128,0,0.15);">
                                <div class="card-body">
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="d-flex align-items-center justify-content-center rounded-circle bg-warning bg-gradient" style="width: 40px; height: 40px;">
                                            <i class="fas fa-calendar-edit text-white"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h6 class="mb-1 text-dark fw-semibold">{{ __('Last Updated') }}</h6>
                                            <p class="text-muted mb-0 text-break small">
                                                ${formatDate(contact.updated_at)}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#previewContactContent').html(html);
        }

        function showPreviewError(message) {
            $('#previewContactContent').html(`
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h5 class="text-muted">{{ __('Error Loading Contact') }}</h5>
                    <p class="text-muted">${message}</p>
                </div>
            `);
        }

        function deleteContact(id, type, name) {
            // Set the message with contact name and type
            let message = `{{ __('Are you sure you want to delete') }} "${name}"?<br>`;
            message += `<small class="text-muted">{{ __('This') }} ${type} {{ __('will be permanently removed from the system.') }}</small>`;

            $('#deleteContactMessage').html(message);

            // Set up the confirm button click handler
            $('#confirmDeleteBtn').off('click').on('click', function() {
                let deleteUrl = '';
                if (type === 'lead') {
                    deleteUrl = `{{ url('/leads') }}/${id}`;
                } else {
                    deleteUrl = `{{ url('/deals') }}/${id}`;
                }

                // Show loading on delete button
                let deleteBtn = $(this);
                let originalText = deleteBtn.text();
                deleteBtn.html('<i class="fa fa-spinner fa-spin"></i> {{ __("Deleting...") }}').prop('disabled', true);

                // Create form and submit
                let form = $('<form>', {
                    'method': 'POST',
                    'action': deleteUrl
                });

                form.append($('<input>', {
                    'type': 'hidden',
                    'name': '_token',
                    'value': '{{ csrf_token() }}'
                }));

                form.append($('<input>', {
                    'type': 'hidden',
                    'name': '_method',
                    'value': 'DELETE'
                }));

                // Submit via AJAX
                $.ajax({
                    url: deleteUrl,
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                        'Accept': 'application/json'
                    },
                    success: function(response) {
                        $('#deleteContactModal').modal('hide');

                        // Show success message using toastr if available, otherwise use alert
                        if (typeof show_toastr === 'function') {
                            show_toastr('success', `${type} {{ __('deleted successfully') }}`);
                        } else {
                            // Fallback to custom alert
                            $('body').append(`
                                <div class="alert alert-success alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
                                    <strong>{{ __('Success!') }}</strong> ${type} {{ __('deleted successfully') }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            `);

                            // Auto-hide alert after 3 seconds
                            setTimeout(function() {
                                $('.alert-success').fadeOut();
                            }, 3000);
                        }

                        // Remove the row from table with animation
                        const contactRow = $(`.contact-checkbox[value="${id}"]`).closest('tr');
                        if (contactRow.length) {
                            contactRow.fadeOut(500, function() {
                                $(this).remove();

                                // Check if table is empty
                                if ($('#contacts-table tbody tr:visible').length === 0) {
                                    $('#contacts-table tbody').html(`
                                        <tr>
                                            <td colspan="9" class="text-center py-4">
                                                <div class="text-center">
                                                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                                    <h5 class="text-muted">{{ __('No contacts found') }}</h5>
                                                </div>
                                            </td>
                                        </tr>
                                    `);
                                }
                            });
                        } else {
                            // Fallback: reload page if row not found
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        }
                    },
                    error: function(xhr) {
                        $('#deleteContactModal').modal('hide');

                        let errorMessage = `{{ __('Failed to delete') }} ${type}`;
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        // Show error message using toastr if available, otherwise use alert
                        if (typeof show_toastr === 'function') {
                            show_toastr('error', errorMessage);
                        } else {
                            // Fallback to custom alert
                            $('body').append(`
                                <div class="alert alert-danger alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
                                    <strong>{{ __('Error!') }}</strong> ${errorMessage}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            `);

                            // Auto-hide alert after 5 seconds
                            setTimeout(function() {
                                $('.alert-danger').fadeOut();
                            }, 5000);
                        }

                        // Reset button
                        deleteBtn.html(originalText).prop('disabled', false);
                    }
                });
            });

            // Show the modal
            $('#deleteContactModal').modal('show');
        }

        // Function to get proper SSO token from backend (same as leads)
        function getSsoToken() {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: '/api/generate-sso-token',
                    type: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success && response.token) {
                            resolve(response.token);
                        } else {
                            reject('Failed to generate SSO token');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('SSO Token generation failed:', xhr.responseJSON);
                        reject('Failed to generate SSO token: ' + error);
                    }
                });
            });
        }

        // Variable to prevent multiple simultaneous loads
        let isLoadingExternalContacts = false;

        // Function to load and display external contacts in the table
        function loadExternalContacts() {
            // Prevent multiple simultaneous loads
            if (isLoadingExternalContacts) {
                return;
            }

            isLoadingExternalContacts = true;
            $('#loading-external').show();

            @if($omxFlowModule)
            const moduleBaseUrl = '{{ $omxFlowModule->base_url }}';
            @else
            const moduleBaseUrl = config('app.url');
            @endif

            getSsoToken().then(function(ssoToken) {
                $.ajax({
                    url: moduleBaseUrl + 'api/sso/contacts/simple-list',
                    type: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + ssoToken,
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'X-User-Email': '{{ Auth::user()->email }}'
                    },
                    success: function(response) {
                        $('#loading-external').hide();
                        isLoadingExternalContacts = false;

                        if (response.data && response.data.contacts && response.data.contacts.length > 0) {
                            // Always remove existing external contacts first
                            $('.external-contact').remove();

                            // Filter out contacts that are already converted to leads
                            filterAndAddExternalContacts(response.data.contacts);
                        }
                    },
                    error: function(xhr, status, error) {
                        $('#loading-external').hide();
                        isLoadingExternalContacts = false;
                        console.error('Failed to load external contacts:', error);

                        if (xhr.status !== 0) { // Don't show error if it's just a connection issue
                            console.warn('Could not load external contacts. External service may be unavailable.');
                        }
                    }
                });
            }).catch(function(error) {
                $('#loading-external').hide();
                isLoadingExternalContacts = false;
                console.error('Failed to generate SSO token for external contacts:', error);
            });
        }

        // Function to add external contact to table
        function addExternalContactToTable(contact) {
            const contactName = contact.full_name || contact.name || 'N/A';
            const contactEmail = contact.email || 'N/A';
            const contactPhone = contact.phone || contact.mobile || contact.phone_number || 'N/A';
            const externalId = contact.id;

            // Check if this contact is already in the table (prevent duplicates)
            const existingRow = $(`.external-contact[data-external-id="${externalId}"]`);
            if (existingRow.length > 0) {
                return; // Contact already exists, don't add duplicate
            }

            const row = `
                <tr class="external-contact" data-source="external" data-external-id="${externalId}" data-email="${contactEmail.toLowerCase()}">
                    <td class="text-center">
                        <div>
                            <strong>${contactName}</strong><br>
                            ${contactEmail !== 'N/A' ? `<small class="text-muted"><i class="fa fa-envelope"></i> ${contactEmail}</small><br>` : ''}
                            ${contactPhone !== 'N/A' ? `<small class="text-muted"><i class="fa fa-phone"></i> ${contactPhone}</small>` : ''}
                        </div>
                    </td>
                    <td class="text-center">
                        <span class="badge bg-warning">
                            <i class="fab fa-whatsapp"></i> External Contact
                        </span>
                    </td>
                    <td class="text-center">
                        <div class="action-btn me-2">
                            <button class="btn btn-sm align-items-center bg-primary convert-to-lead-btn"
                                    data-external-id="${externalId}"
                                    data-contact-name="${contactName}"
                                    data-contact-email="${contactEmail}"
                                    data-contact-phone="${contactPhone}"
                                    data-bs-toggle="tooltip"
                                    title="{{ __('Convert to Lead') }}">
                                <i class="ti ti-user-plus text-white"></i> {{ __('Convert to Lead') }}
                            </button>
                        </div>
                    </td>
                </tr>
            `;

            $('#contacts-table tbody').append(row);
        }

        // Function to show convert to lead modal
        function showConvertToLeadModal(externalId, contactName, contactEmail, contactPhone, button) {
            // Store the button reference for later use
            window.currentConvertButton = button;

            console.log('Opening modal for:', {externalId, contactName, contactEmail, contactPhone});
            console.log('Available pipelines:', pipelines);

            // Populate modal fields
            $('#contact_name').val(contactName);
            $('#contact_email').val(contactEmail);
            $('#contact_phone').val(contactPhone || 'N/A');
            $('#external_id').val(externalId);

            // Clear and populate pipeline dropdown
            const pipelineSelect = $('#pipeline_id');
            pipelineSelect.empty().append('<option value="">{{ __("Select Pipeline") }}</option>');

            if (pipelines && pipelines.length > 0) {
                pipelines.forEach(pipeline => {
                    console.log('Adding pipeline:', pipeline);
                    pipelineSelect.append(`<option value="${pipeline.id}">${pipeline.name}</option>`);
                });
            } else {
                console.log('No pipelines available');
                pipelineSelect.append('<option value="">{{ __("No pipelines available") }}</option>');
            }

            // Clear stage dropdown
            $('#stage_id').empty().append('<option value="">{{ __("Select Stage") }}</option>');

            // Show the modal
            $('#convertToLeadModal').modal('show');
        }

        // Handle pipeline selection change
        $(document).on('change', '#pipeline_id', function() {
            const pipelineId = $(this).val();
            const stageSelect = $('#stage_id');

            console.log('Pipeline selected:', pipelineId);

            // Clear stage dropdown
            stageSelect.empty().append('<option value="">{{ __("Select Stage") }}</option>');

            if (pipelineId) {
                // Show loading in stage dropdown
                stageSelect.append('<option value="">{{ __("Loading stages...") }}</option>');

                // Fetch stages for selected pipeline
                $.ajax({
                    url: '/api/get-pipeline-stages',
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: {
                        pipeline_id: pipelineId
                    },
                    success: function(response) {
                        console.log('Stages response:', response);

                        // Clear loading option
                        stageSelect.empty().append('<option value="">{{ __("Select Stage") }}</option>');

                        if (response.stages && response.stages.length > 0) {
                            response.stages.forEach(stage => {
                                console.log('Adding stage:', stage);
                                stageSelect.append(`<option value="${stage.id}">${stage.name}</option>`);
                            });
                        } else {
                            stageSelect.append('<option value="">{{ __("No stages found") }}</option>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Failed to fetch stages:', error);
                        console.error('XHR response:', xhr.responseJSON);

                        // Clear loading option and show error
                        stageSelect.empty().append('<option value="">{{ __("Error loading stages") }}</option>');
                    }
                });
            }
        });

        // Handle convert confirmation
        $(document).on('click', '#confirmConvertBtn', function() {
            const form = $('#convertToLeadForm');
            const pipelineId = $('#pipeline_id').val();
            const stageId = $('#stage_id').val();

            // Validate required fields
            if (!pipelineId || !stageId) {
                alert('{{ __("Please select both pipeline and stage") }}');
                return;
            }

            // Get form data
            const contactPhone = $('#contact_phone').val();
            const formData = {
                name: $('#contact_name').val(),
                email: $('#contact_email').val(),
                phone: contactPhone !== 'N/A' ? contactPhone : '',
                subject: 'Converted from External Contact',
                pipeline_id: pipelineId,
                stage_id: stageId,
                user_id: '{{ Auth::user()->id }}',
                _token: $('meta[name="csrf-token"]').attr('content')
            };

            // Perform the conversion
            convertExternalContactToLeadWithData(formData, window.currentConvertButton);
        });

        // Function to filter external contacts and check against existing leads
        function filterAndAddExternalContacts(externalContacts) {
            // Get existing lead emails from the current contacts table
            const existingLeadEmails = new Set();

            // Collect emails from server-side rendered contacts (leads and deals)
            $('#contacts-table tbody tr:not(.external-contact)').each(function() {
                const emailElement = $(this).find('i.fa-envelope').parent();
                if (emailElement.length > 0) {
                    const emailText = emailElement.text().trim();
                    if (emailText) {
                        existingLeadEmails.add(emailText.toLowerCase());
                    }
                }
            });

            // Get emails from external contacts to check
            const externalEmails = externalContacts.map(contact => contact.email).filter(email => email);

            // If no external contacts have emails, just add them all
            if (externalEmails.length === 0) {
                externalContacts.forEach(contact => {
                    addExternalContactToTable(contact);
                });
                return;
            }

            // Make AJAX call to get all existing leads to check for converted contacts
            $.ajax({
                url: '/api/check-converted-contacts',
                type: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                data: JSON.stringify({
                    emails: externalEmails
                }),
                success: function(response) {
                    const convertedEmails = new Set(response.converted_emails || []);

                    // Use a Set to track unique contacts by email to prevent duplicates
                    const uniqueContacts = new Map();

                    // Filter out contacts that are already converted or exist as leads
                    externalContacts.forEach(contact => {
                        const contactEmail = (contact.email || '').toLowerCase();
                        const contactName = contact.full_name || contact.name || 'N/A';

                        // Only add if email exists and not already converted/existing
                        if (contactEmail &&
                            !existingLeadEmails.has(contactEmail) &&
                            !convertedEmails.has(contactEmail)) {

                            // Use email as unique key to prevent duplicates
                            uniqueContacts.set(contactEmail, contact);
                        }
                    });

                    // Add unique filtered contacts to table
                    uniqueContacts.forEach(contact => {
                        addExternalContactToTable(contact);
                    });

                    // Apply current filters to newly added external contacts
                    setTimeout(function() {
                        applyFiltersToAllContacts();
                    }, 100);
                },
                error: function(xhr, status, error) {
                    console.error('Failed to check converted contacts:', error);
                    // If the check fails, show all contacts (fallback)
                    externalContacts.forEach(contact => {
                        addExternalContactToTable(contact);
                    });

                    // Apply current filters to newly added external contacts
                    setTimeout(function() {
                        applyFiltersToAllContacts();
                    }, 100);
                }
            });
        }

        // Function to convert external contact to lead with form data
        function convertExternalContactToLeadWithData(leadData, button) {
            const originalHtml = button.html();
            button.prop('disabled', true).html('<i class="ti ti-loader ti-spin"></i> Converting...');

            // Make AJAX call to create new lead
            $.ajax({
                url: '{{ route("leads.store") }}',
                type: 'POST',
                data: leadData,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    // Hide the modal
                    $('#convertToLeadModal').modal('hide');

                    // Remove the external contact row
                    const externalRow = button.closest('tr');
                    externalRow.remove();

                    // Show success message
                    if (typeof show_toastr === 'function') {
                        show_toastr('success', `${leadData.name} has been successfully converted to a lead!`, 'success');
                    } else {
                        alert(`${leadData.name} has been successfully converted to a lead!`);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Conversion failed:', xhr.responseJSON);
                    let errorMessage = 'Failed to convert contact to lead';

                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                        const errors = Object.values(xhr.responseJSON.errors).flat();
                        errorMessage = errors.join(', ');
                    }

                    // Show error message
                    if (typeof show_toastr === 'function') {
                        show_toastr('error', errorMessage, 'error');
                    } else {
                        alert('Error: ' + errorMessage);
                    }
                },
                complete: function() {
                    // Reset button state
                    button.prop('disabled', false).html(originalHtml);
                }
            });
        }



        // Convert to lead button click handler
        $(document).on("click", ".convert-to-lead-btn", function (e) {
            e.preventDefault();
            const externalId = $(this).data('external-id');
            const contactName = $(this).data('contact-name');
            const contactEmail = $(this).data('contact-email');
            const contactPhone = $(this).data('contact-phone');

            // Show the convert to lead modal
            showConvertToLeadModal(externalId, contactName, contactEmail, contactPhone, $(this));
        });

        // Function to apply filters to all contacts (both server-side and external)
        function applyFiltersToAllContacts() {
            const nameFilter = $('#filter_name').val().toLowerCase();
            const emailFilter = $('#filter_email').val().toLowerCase();
            const phoneFilter = $('#filter_phone').val().toLowerCase();
            const typeFilter = $('#filter_type').val();

            // Handle server-side contacts (leads and deals)
            $('tbody tr:not(.external-contact)').each(function() {
                const row = $(this);
                let showRow = true;

                // Get the contact type from the badge in the Type column
                const typeBadge = row.find('td:nth-child(2) .badge').text().trim();
                const contactText = row.text().toLowerCase();

                // Apply type filter
                if (typeFilter) {
                    if (typeFilter === 'WhatsApp') {
                        // If External Contact is selected, hide all server-side contacts
                        showRow = false;
                    } else if (typeFilter === 'Lead' && typeBadge !== 'Lead') {
                        // If Lead is selected, show only leads
                        showRow = false;
                    } else if (typeFilter === 'Deal' && typeBadge !== 'Deal') {
                        // If Deal is selected, show only deals
                        showRow = false;
                    }
                }

                // Apply other filters only if type filter passes
                if (showRow) {
                    if (nameFilter && !contactText.includes(nameFilter)) {
                        showRow = false;
                    }
                    if (emailFilter && !contactText.includes(emailFilter)) {
                        showRow = false;
                    }
                    if (phoneFilter && !contactText.includes(phoneFilter)) {
                        showRow = false;
                    }
                }

                if (showRow) {
                    row.show();
                } else {
                    row.hide();
                }
            });

            // Handle external contacts
            $('.external-contact').each(function() {
                const row = $(this);
                const contactText = row.find('td:first').text().toLowerCase();

                let showRow = true;

                // Apply type filter first - external contacts should only show when WhatsApp is selected or no filter
                if (typeFilter) {
                    if (typeFilter === 'WhatsApp') {
                        // Show external contacts when External Contact is selected
                        showRow = true;
                    } else {
                        // Hide external contacts when Lead or Deal is selected
                        showRow = false;
                    }
                }

                // Apply other filters only if type filter passes
                if (showRow) {
                    if (nameFilter && !contactText.includes(nameFilter)) {
                        showRow = false;
                    }
                    if (emailFilter && !contactText.includes(emailFilter)) {
                        showRow = false;
                    }
                    if (phoneFilter && !contactText.includes(phoneFilter)) {
                        showRow = false;
                    }
                }

                // Show/hide row based on filters
                if (showRow) {
                    row.show();
                } else {
                    row.hide();
                }
            });
        }

        // Handle filter form submission
        $(document).on('submit', '#contactsFilterForm', function(e) {
            // Prevent default form submission for client-side filtering
            e.preventDefault();

            // Apply filters to all contacts (both server-side and external)
            applyFiltersToAllContacts();
        });

        // Handle filter input changes for real-time filtering
        $(document).on('input change', '#filter_name, #filter_email, #filter_phone, #filter_type', function() {
            applyFiltersToAllContacts();
        });

        // Load external contacts when page loads
        $(document).ready(function() {
            // Load external contacts after a short delay to ensure page is fully loaded
            setTimeout(function() {
                // Always load external contacts if none exist
                if ($('.external-contact').length === 0) {
                    loadExternalContacts();
                }

                // Apply filters to external contacts after they load
                setTimeout(function() {
                    applyFiltersToAllContacts();
                }, 2000);
            }, 1000);

            // Initialize tooltips
            $('[data-bs-toggle="tooltip"]').tooltip();

            // Handle select all checkbox
            $('#select-all-contacts').change(function() {
                $('.contact-checkbox').prop('checked', this.checked);
            });

            // Handle individual checkboxes
            $(document).on('change', '.contact-checkbox', function() {
                var totalCheckboxes = $('.contact-checkbox').length;
                var checkedCheckboxes = $('.contact-checkbox:checked').length;
                $('#select-all-contacts').prop('checked', totalCheckboxes === checkedCheckboxes);
            });
        });

        // New Contact Functions
        function openAddContactModal() {
            // Don't set any default pipeline or stage - let user add pipeline later
            $('#hidden_pipeline_id').val('');
            $('#hidden_stage_id').val('');

            $('#addContactModal').modal('show');
        }

        function loadStagesForPipeline(pipelineId, callback) {
            console.log('🔄 Loading stages for pipeline ID:', pipelineId);

            if (!pipelineId || pipelineId === '' || pipelineId === '0') {
                console.error('❌ Invalid pipeline ID:', pipelineId);
                if (callback) callback([]);
                return;
            }

            $.ajax({
                url: '{{ route("api.get-pipeline-stages") }}',
                method: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    pipeline_id: pipelineId
                },
                success: function(response) {
                    console.log('✅ Stages response:', response);
                    if (response.success && response.stages) {
                        console.log('Found', response.stages.length, 'stages');
                        if (callback) callback(response.stages);
                    } else {
                        console.log('⚠️ No stages found or invalid response');
                        if (callback) callback([]);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('❌ Error loading stages:', error);
                    console.error('Response:', xhr.responseText);
                    if (callback) callback([]);
                }
            });
        }

        function saveNewContact() {
            // Clear any previous error messages
            $('.alert-danger').remove();

            var form = $('#addContactForm')[0];
            var formData = new FormData(form);

            // Get the full name
            var fullName = formData.get('name');

            // Validate required fields
            if (!fullName || !fullName.trim()) {
                showFormError('Full name is required');
                return;
            }

            // Pipeline and stage are not required during contact creation
            // User can add pipeline later using "Add Pipeline" feature

            // Prepare DND settings as JSON
            var dndSettings = {
                all: formData.get('dnd_all') === 'on',
                emails: formData.get('dnd_emails') === 'on',
                whatsapp: formData.get('dnd_whatsapp') === 'on',
                sms: formData.get('dnd_sms') === 'on',
                calls: formData.get('dnd_calls') === 'on'
            };
            formData.set('dnd_settings', JSON.stringify(dndSettings));

            // Show loading state
            var saveBtn = $('button[onclick="saveNewContact()"]');
            var originalText = saveBtn.text();
            saveBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Saving...');

            $.ajax({
                url: form.action,
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    $('#addContactModal').modal('hide');
                    show_toastr('success', 'Contact added successfully!');

                    // Add the new contact to the table immediately
                    addContactToTable(response.lead || {
                        id: response.id || Date.now(),
                        name: fullName,
                        email: formData.get('email') || '',
                        phone: formData.get('phone') || '',
                        type: 'Lead',
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    });

                    // Reset form
                    form.reset();
                },
                error: function(xhr) {
                    var errors = xhr.responseJSON?.errors || {};
                    var message = xhr.responseJSON?.message || 'An error occurred while adding contact';

                    if (Object.keys(errors).length > 0) {
                        var errorMessages = [];
                        Object.values(errors).forEach(function(error) {
                            if (Array.isArray(error)) {
                                errorMessages.push(error[0]);
                            } else {
                                errorMessages.push(error);
                            }
                        });
                        showFormError(errorMessages.join('<br>'));
                    } else {
                        showFormError(message);
                    }
                },
                complete: function() {
                    // Reset button state
                    saveBtn.prop('disabled', false).text(originalText);
                }
            });
        }

        function addContactToTable(contact) {
            // Remove "No contacts found" row if it exists
            $('#contacts-table tbody tr').each(function() {
                if ($(this).find('td').length === 1 && $(this).find('td').attr('colspan') === '9') {
                    $(this).remove();
                }
            });

            // Format dates
            var createdDate = new Date(contact.created_at || new Date());
            var updatedDate = new Date(contact.updated_at || new Date());

            var formattedCreatedDate = createdDate.toLocaleDateString('en-GB');
            var formattedCreatedTime = createdDate.toLocaleTimeString('en-US', { hour12: true });
            var formattedUpdatedDate = updatedDate.toLocaleDateString('en-GB');
            var formattedUpdatedTime = updatedDate.toLocaleTimeString('en-US', { hour12: true });

            // Create new row HTML
            var newRow = `
                <tr>
                    <td class="text-center">
                        <input type="checkbox" class="contact-checkbox" value="${contact.id}" data-type="${contact.type}">
                    </td>
                    <td class="text-left">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm me-3">
                                <div class="avatar-title bg-primary rounded-circle">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                            </div>
                            <div>
                                <strong>${contact.name}</strong><br>
                                ${contact.email ? `<small class="text-muted"><i class="fa fa-envelope"></i> ${contact.email}</small><br>` : ''}
                                ${contact.phone ? `<small class="text-muted"><i class="fa fa-phone"></i> ${contact.phone}</small>` : ''}
                            </div>
                        </div>
                    </td>
                    <td class="text-center">
                        ${formattedCreatedDate}<br>
                        <small class="text-muted">${formattedCreatedTime}</small>
                    </td>
                    <td class="text-center">
                        ${formattedUpdatedDate}<br>
                        <small class="text-muted">${formattedUpdatedTime}</small>
                    </td>
                    <td class="text-center">
                        <span class="badge bg-info">Form</span>
                    </td>
                    <td class="text-center">
                        <span class="text-muted">-</span>
                    </td>
                    <td class="text-center">
                        <span class="badge bg-success">${contact.type}</span>
                    </td>
                    <td class="text-center">
                        <span class="text-muted">-</span>
                    </td>
                    <td class="text-center">
                        <div class="action-btn">
                            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="/leads/${contact.id}"><i class="fa fa-eye me-2"></i>View</a></li>
                                <li><a class="dropdown-item" href="#" onclick="previewContact('${contact.id}', 'lead')"><i class="fa fa-search me-2"></i>Preview</a></li>
                                <li><a class="dropdown-item" href="#" onclick="editContact('${contact.id}', 'lead')"><i class="fa fa-edit me-2"></i>Edit</a></li>
                                <li><a class="dropdown-item" href="#" onclick="openAddPipelineModal('${contact.id}', '${contact.name}')"><i class="fa fa-plus me-2"></i>Add Pipeline</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteContact('${contact.id}', 'lead', '${contact.name}')"><i class="fa fa-trash me-2"></i>Delete</a></li>
                            </ul>
                        </div>
                    </td>
                </tr>
            `;

            // Add the new row to the table
            $('#contacts-table tbody').prepend(newRow);
        }

        function showFormError(message) {
            var errorHtml = '<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
                           '<i class="fas fa-exclamation-triangle me-2"></i>' + message +
                           '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                           '</div>';
            $('#addContactForm').prepend(errorHtml);
        }

        // Handle pipeline change for add contact modal
        $(document).on('change', '#add_pipeline_id', function() {
            const pipelineId = $(this).val();
            const stageSelect = $('#add_stage_id');

            stageSelect.empty().append('<option value="">Select Stage</option>');

            if (pipelineId) {
                $.ajax({
                    url: '/api/get-pipeline-stages',
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: {
                        pipeline_id: pipelineId
                    },
                    success: function(response) {
                        if (response.stages && response.stages.length > 0) {
                            response.stages.forEach(stage => {
                                stageSelect.append(`<option value="${stage.id}">${stage.name}</option>`);
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Failed to fetch stages:', error);
                    }
                });
            }
        });

        function openContactGroupModal() {
            var selectedContacts = $('.contact-checkbox:checked').length;
            if (selectedContacts === 0) {
                alert('Please select at least one contact first.');
                return;
            }
            $('#contactGroupModal').modal('show');
        }

        function addToContactGroup() {
            var selectedGroup = $('#contact_group').val();
            var selectedContacts = $('.contact-checkbox:checked');

            if (!selectedGroup) {
                alert('Please select a contact group.');
                return;
            }

            if (selectedContacts.length === 0) {
                alert('Please select at least one contact.');
                return;
            }

            // Here you would implement the actual group assignment logic
            $('#contactGroupModal').modal('hide');
            show_toastr('success', `${selectedContacts.length} contact(s) added to ${selectedGroup} group successfully!`);
        }

        function openWorkflowModal() {
            var selectedContacts = $('.contact-checkbox:checked').length;
            if (selectedContacts === 0) {
                alert('Please select at least one contact first.');
                return;
            }
            $('#workflowModal').modal('show');
        }

        function addToWorkflow() {
            var selectedWorkflow = $('#workflow_select').val();
            var selectedContacts = $('.contact-checkbox:checked');

            if (!selectedWorkflow) {
                alert('Please select a workflow.');
                return;
            }

            if (selectedContacts.length === 0) {
                alert('Please select at least one contact.');
                return;
            }

            // Here you would implement the actual workflow assignment logic
            $('#workflowModal').modal('hide');
            show_toastr('success', `${selectedContacts.length} contact(s) added to workflow successfully!`);
        }

        function openTagModal() {
            var selectedContacts = $('.contact-checkbox:checked').length;
            if (selectedContacts === 0) {
                alert('Please select at least one contact first.');
                return;
            }
            $('#tagModal').modal('show');
        }

        function addTags() {
            var tags = $('#tag_input').val();
            var selectedContacts = $('.contact-checkbox:checked');

            if (!tags.trim()) {
                alert('Please enter at least one tag.');
                return;
            }

            if (selectedContacts.length === 0) {
                alert('Please select at least one contact.');
                return;
            }

            // Here you would implement the actual tag assignment logic
            $('#tagModal').modal('hide');
            show_toastr('success', `Tags added to ${selectedContacts.length} contact(s) successfully!`);
        }

        function openAppointmentModal() {
            $('#appointmentModal').modal('show');
        }

        function bookAppointment() {
            var title = $('#appointment_title').val();
            var date = $('#appointment_date').val();
            var time = $('#appointment_time').val();

            if (!title || !date || !time) {
                alert('Please fill in all required fields.');
                return;
            }

            // Here you would implement the actual appointment booking logic
            $('#appointmentModal').modal('hide');
            show_toastr('success', 'Appointment booked successfully!');
        }

        function openBulkImportModal() {
            $('#bulkImportModal').modal('show');
        }

        function importContacts() {
            var fileInput = $('#import_file')[0];
            if (!fileInput.files.length) {
                alert('Please select a file to import.');
                return;
            }

            var formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('_token', $('meta[name="csrf-token"]').attr('content'));

            // Here you would implement the actual import logic
            $('#bulkImportModal').modal('hide');
            show_toastr('success', 'Contacts imported successfully!');
        }

        function downloadTemplate() {
            // Create a simple CSV template
            var csvContent = "Name,Email,Phone,Type\n";
            csvContent += "John Doe,<EMAIL>,+1234567890,Lead\n";
            csvContent += "Jane Smith,<EMAIL>,+0987654321,Deal\n";

            var blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            var link = document.createElement("a");
            var url = URL.createObjectURL(blob);
            link.setAttribute("href", url);
            link.setAttribute("download", "contacts_template.csv");
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function exportContacts() {
            // Here you would implement the actual export logic
            show_toastr('success', 'Contacts exported successfully!');
        }

        function openSearchModal() {
            $('#searchModal').modal('show');
        }

        function performAdvancedSearch() {
            var searchData = {
                name: $('#search_name').val(),
                email: $('#search_email').val(),
                phone: $('#search_phone').val(),
                type: $('#search_type').val(),
                date_from: $('#search_date_from').val(),
                date_to: $('#search_date_to').val()
            };

            // Build query string
            var queryParams = [];
            Object.keys(searchData).forEach(key => {
                if (searchData[key]) {
                    queryParams.push(`filter_${key}=${encodeURIComponent(searchData[key])}`);
                }
            });

            if (queryParams.length > 0) {
                window.location.href = '{{ route("contacts.index") }}?' + queryParams.join('&');
            } else {
                $('#searchModal').modal('hide');
            }
        }

        function clearAdvancedSearch() {
            $('#advancedSearchForm')[0].reset();
            window.location.href = '{{ route("contacts.index") }}';
        }

        // Contact Group Functions
        function openCreateContactGroupModal() {
            var selectedContacts = getSelectedContacts();

            if (selectedContacts.length === 0) {
                show_toastr('error', 'Please select at least one lead to add to a contact group');
                return;
            }

            console.log('Selected contacts for group:', selectedContacts);

            // Set selected contacts in hidden input
            $('#selected_contacts_input').val(JSON.stringify(selectedContacts));

            // Load existing contact groups
            loadExistingContactGroups();

            // Reset form
            $('#contact_group_name').val('');
            $('#existing_contact_group').val('');

            $('#createContactGroupModal').modal('show');
        }

        function getSelectedContacts() {
            var selectedContacts = [];
            $('.contact-checkbox:checked').each(function() {
                var contactId = $(this).val();
                var contactType = $(this).data('type');
                // Only include leads for contact groups
                if (contactType === 'Lead') {
                    selectedContacts.push(contactId + ':' + contactType);
                }
            });
            return selectedContacts;
        }

        function getSelectedContactsForAppointment() {
            var selectedContacts = [];
            console.log('🔍 Getting selected contacts for appointment...');

            $('.contact-checkbox:checked').each(function() {
                var $checkbox = $(this);
                var contactId = $checkbox.val();
                var contactType = $checkbox.data('type');
                var contactName = $checkbox.data('name');

                console.log('👤 Processing contact:', {
                    id: contactId,
                    type: contactType,
                    name: contactName,
                    hasDataName: contactName !== undefined
                });

                if (contactName && contactName.trim() !== '') {
                    selectedContacts.push({
                        id: contactId,
                        type: contactType,
                        name: contactName.trim()
                    });
                } else {
                    console.log('⚠️ Contact has no name, skipping:', contactId);
                }
            });

            console.log('📊 Total valid selected contacts:', selectedContacts.length);
            return selectedContacts;
        }

        function loadExistingContactGroups() {
            $.ajax({
                url: '{{ route("contact-groups.get") }}',
                method: 'GET',
                success: function(response) {
                    if (response.success && response.groups) {
                        var select = $('#existing_contact_group');
                        select.html('<option value="">{{ __("Select Contact Group") }}</option>');

                        response.groups.forEach(function(group) {
                            select.append('<option value="' + group.id + '">' + group.name + '</option>');
                        });
                    }
                },
                error: function() {
                    console.error('Failed to load existing contact groups');
                }
            });
        }

        function saveContactGroup() {
            var form = $('#createContactGroupForm')[0];
            var formData = new FormData(form);

            // Get selected contacts
            var selectedContacts = getSelectedContacts();
            if (selectedContacts.length === 0) {
                show_toastr('error', 'No contacts selected');
                return;
            }

            // Add selected contacts to form data
            selectedContacts.forEach(function(contact, index) {
                formData.append('selected_contacts[' + index + ']', contact);
            });

            var groupName = $('#contact_group_name').val().trim();
            var existingGroupId = $('#existing_contact_group').val();

            if (!groupName && !existingGroupId) {
                show_toastr('error', 'Please enter a group name or select an existing group');
                return;
            }

            // Show loading state
            var saveBtn = $('button[onclick="saveContactGroup()"]');
            var originalText = saveBtn.text();
            saveBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Saving...');

            $.ajax({
                url: form.action,
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        $('#createContactGroupModal').modal('hide');
                        show_toastr('success', response.message);

                        // Clear selection
                        clearSelection();

                        console.log('Contact group created successfully:', response.group);
                    } else {
                        show_toastr('error', response.message);
                    }
                },
                error: function(xhr) {
                    var message = xhr.responseJSON?.message || 'Error creating contact group';
                    show_toastr('error', message);
                },
                complete: function() {
                    saveBtn.prop('disabled', false).text(originalText);
                }
            });
        }

        // Contact selection functions
        $(document).ready(function() {
            // Handle select all checkbox
            $('#select-all-contacts').on('change', function() {
                var isChecked = $(this).is(':checked');
                $('.contact-checkbox').prop('checked', isChecked);
                updateBulkActions();
            });

            // Handle individual contact checkboxes
            $(document).on('change', '.contact-checkbox', function() {
                updateBulkActions();

                // Update select all checkbox
                var totalCheckboxes = $('.contact-checkbox').length;
                var checkedCheckboxes = $('.contact-checkbox:checked').length;

                if (checkedCheckboxes === 0) {
                    $('#select-all-contacts').prop('indeterminate', false).prop('checked', false);
                } else if (checkedCheckboxes === totalCheckboxes) {
                    $('#select-all-contacts').prop('indeterminate', false).prop('checked', true);
                } else {
                    $('#select-all-contacts').prop('indeterminate', true);
                }
            });
        });

        function updateBulkActions() {
            var selectedCount = $('.contact-checkbox:checked').length;
            var bulkActions = $('#bulk-actions');
            var selectedCountSpan = $('#selected-count');

            if (selectedCount > 0) {
                bulkActions.show();
                selectedCountSpan.text(selectedCount + ' contact' + (selectedCount > 1 ? 's' : '') + ' selected');
            } else {
                bulkActions.hide();
            }
        }

        function clearSelection() {
            $('.contact-checkbox').prop('checked', false);
            $('#select-all-contacts').prop('checked', false).prop('indeterminate', false);
            updateBulkActions();
        }

        function bulkDeleteContacts() {
            var selectedContacts = getSelectedContacts();

            if (selectedContacts.length === 0) {
                show_toastr('error', 'Please select contacts to delete');
                return;
            }

            if (confirm('Are you sure you want to delete ' + selectedContacts.length + ' selected contact(s)? This action cannot be undone.')) {
                // Implementation for bulk delete
                console.log('Bulk delete contacts:', selectedContacts);
                show_toastr('info', 'Bulk delete functionality to be implemented');
            }
        }

        // Add Pipeline Functions
        function openAddPipelineModal(contactId, contactName) {
            console.log('📋 Opening Add Pipeline modal for contact:', contactId, contactName);

            $('#pipeline_contact_id').val(contactId);
            $('#pipeline_contact_name').val(contactName);

            // Reset form
            $('#pipeline_select').val('');
            $('#stage_select').html('<option value="">{{ __("Select Stage") }}</option>').prop('disabled', true);
            $('#pipeline_notes').val('');

            // Update modal title to be more descriptive
            $('#addPipelineModalLabel').text('Move Contact to Pipeline');

            // Add helpful text
            var helpText = '<div class="alert alert-info mb-3" id="pipeline-help-text">' +
                          '<i class="fas fa-info-circle me-2"></i>' +
                          'Select a pipeline and stage to move this contact. If the contact is already in a pipeline, it will be moved to the new selection.' +
                          '</div>';

            // Remove existing help text and add new one
            $('#addPipelineForm .alert-info').remove();
            $('#addPipelineForm').prepend(helpText);

            $('#addPipelineModal').modal('show');
        }

        function savePipelineAssignment() {
            // Clear any previous error messages
            $('.alert-danger').remove();

            var contactId = $('#pipeline_contact_id').val();
            var pipelineId = $('#pipeline_select').val();
            var stageId = $('#stage_select').val();
            var notes = $('#pipeline_notes').val();

            // Validate required fields
            if (!pipelineId) {
                showPipelineError('Please select a pipeline');
                return;
            }

            if (!stageId) {
                showPipelineError('Please select a stage');
                return;
            }

            // Show loading state
            var saveBtn = $('button[onclick="savePipelineAssignment()"]');
            var originalText = saveBtn.text();
            saveBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Updating...');

            $.ajax({
                url: '/api/contacts/' + contactId + '/pipeline',
                method: 'PUT',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    pipeline_id: pipelineId,
                    stage_id: stageId,
                    notes: notes
                },
                success: function(response) {
                    console.log('✅ Pipeline assignment successful:', response);

                    $('#addPipelineModal').modal('hide');

                    if (response.success && response.contact) {
                        var contact = response.contact;
                        var successMessage = `Contact "${contact.name}" moved to pipeline "${contact.pipeline_name}" and stage "${contact.stage_name}" successfully!`;
                        show_toastr('success', successMessage);

                        // Update the contact display in the table immediately
                        updateContactInTable(contact);

                        console.log('Contact moved successfully:', {
                            name: contact.name,
                            pipeline: contact.pipeline_name,
                            stage: contact.stage_name
                        });
                    } else {
                        show_toastr('success', 'Contact moved to pipeline successfully!');

                        // Fallback: reload page after a short delay
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    }
                },
                error: function(xhr) {
                    var errors = xhr.responseJSON?.errors || {};
                    var message = xhr.responseJSON?.message || 'An error occurred while updating pipeline';

                    if (Object.keys(errors).length > 0) {
                        var errorMessages = [];
                        Object.values(errors).forEach(function(error) {
                            if (Array.isArray(error)) {
                                errorMessages.push(error[0]);
                            } else {
                                errorMessages.push(error);
                            }
                        });
                        showPipelineError(errorMessages.join('<br>'));
                    } else {
                        showPipelineError(message);
                    }
                },
                complete: function() {
                    // Reset button state
                    saveBtn.prop('disabled', false).text(originalText);
                }
            });
        }

        function showPipelineError(message) {
            var errorHtml = '<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
                           '<i class="fas fa-exclamation-triangle me-2"></i>' + message +
                           '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                           '</div>';
            $('#addPipelineForm').prepend(errorHtml);
        }

        function updateContactInTable(contact) {
            console.log('🔄 Updating contact in table:', contact);

            // Find the contact row in the table
            var contactRow = null;
            $('#contacts-table tbody tr').each(function() {
                var rowContactId = $(this).find('.contact-checkbox').val();
                if (rowContactId == contact.id) {
                    contactRow = $(this);
                    return false; // Break the loop
                }
            });

            if (contactRow) {
                console.log('📍 Found contact row, updating display');

                // Add a visual indicator that the contact was moved
                contactRow.addClass('table-success');

                // Add a small badge or indicator to show the contact has been moved
                var nameCell = contactRow.find('td:nth-child(2)');
                var existingBadge = nameCell.find('.pipeline-moved-badge');

                if (existingBadge.length === 0) {
                    var badge = '<span class="badge bg-success pipeline-moved-badge ms-2" title="Recently moved to ' + contact.pipeline_name + ' - ' + contact.stage_name + '">Moved</span>';
                    nameCell.find('strong').after(badge);
                }

                // Remove the highlight after a few seconds
                setTimeout(function() {
                    contactRow.removeClass('table-success');
                    contactRow.find('.pipeline-moved-badge').fadeOut(function() {
                        $(this).remove();
                    });
                }, 5000);

                console.log('✅ Contact display updated successfully');
            } else {
                console.log('⚠️ Contact row not found in table, will reload page');

                // If we can't find the row, reload the page to show updated data
                setTimeout(function() {
                    location.reload();
                }, 2000);
            }
        }

        // Handle pipeline selection change to load stages
        $(document).ready(function() {
            console.log('🚀 Document ready - setting up pipeline handlers');

            // Test function to verify AJAX endpoint
            window.testPipelineStages = function(pipelineId) {
                console.log('🧪 Testing pipeline stages for ID:', pipelineId);
                $.ajax({
                    url: '{{ route("api.get-pipeline-stages") }}',
                    method: 'POST',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content'),
                        pipeline_id: pipelineId
                    },
                    success: function(response) {
                        console.log('✅ Test response:', response);
                    },
                    error: function(xhr, status, error) {
                        console.error('❌ Test error:', error, xhr.responseText);
                    }
                });
            };

            // Pipeline selection change handler
            $('#pipeline_select').on('change', function() {
                var pipelineId = $(this).val();
                var stageSelect = $('#stage_select');

                console.log('🔄 Pipeline changed to ID:', pipelineId);

                // Reset stage dropdown
                stageSelect.html('<option value="">{{ __("Select Stage") }}</option>');
                stageSelect.prop('disabled', true);

                if (pipelineId && pipelineId !== '') {
                    console.log('📡 Making AJAX request for stages...');

                    // Show loading
                    stageSelect.html('<option value="">Loading stages...</option>');

                    // Direct AJAX call (simplified)
                    $.ajax({
                        url: '{{ route("api.get-pipeline-stages") }}',
                        method: 'POST',
                        data: {
                            _token: $('meta[name="csrf-token"]').attr('content'),
                            pipeline_id: pipelineId
                        },
                        success: function(response) {
                            console.log('📥 AJAX Success:', response);

                            // Reset dropdown
                            stageSelect.html('<option value="">{{ __("Select Stage") }}</option>');

                            if (response.success && response.stages && response.stages.length > 0) {
                                // Add stages to dropdown
                                $.each(response.stages, function(index, stage) {
                                    stageSelect.append('<option value="' + stage.id + '">' + stage.name + '</option>');
                                });
                                stageSelect.prop('disabled', false);
                                console.log('✅ Added', response.stages.length, 'stages to dropdown');
                            } else if (response.stages && response.stages.length > 0) {
                                // Fallback for different response format
                                $.each(response.stages, function(index, stage) {
                                    stageSelect.append('<option value="' + stage.id + '">' + stage.name + '</option>');
                                });
                                stageSelect.prop('disabled', false);
                                console.log('✅ Added', response.stages.length, 'stages (fallback format)');
                            } else {
                                stageSelect.html('<option value="">No stages available</option>');
                                console.log('⚠️ No stages found for pipeline:', pipelineId);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('❌ AJAX Error:', error);
                            console.error('Response Text:', xhr.responseText);
                            console.error('Status:', status);

                            stageSelect.html('<option value="">Error loading stages</option>');
                        }
                    });
                }
            });

            // Reset form when modal opens
            $('#addPipelineModal').on('shown.bs.modal', function() {
                console.log('📋 Add Pipeline modal opened');
                $('#pipeline_select').val('');
                $('#stage_select').html('<option value="">{{ __("Select Stage") }}</option>').prop('disabled', true);
                $('#pipeline_notes').val('');

                // Log available pipelines
                console.log('Available pipelines:', $('#pipeline_select option').length - 1);
            });

            // Appointment Booking Modal Functions
            window.openAppointmentModal = function() {
                console.log('🚀 Opening appointment modal...');

                // Clear previous values
                $('#appointment_contact_name').val('');
                $('#selected_contact_ids').val('');
                $('#appointment_warning').addClass('d-none');

                // Get checked checkboxes directly
                const checkedBoxes = $('.contact-checkbox:checked');
                console.log('📋 Found checked boxes:', checkedBoxes.length);

                if (checkedBoxes.length === 0) {
                    console.log('⚠️ No contacts selected');
                    $('#appointment_warning').removeClass('d-none');
                } else {
                    // Extract contact information directly from checkboxes
                    const selectedContacts = [];
                    const contactNames = [];
                    const contactIds = [];

                    checkedBoxes.each(function() {
                        const $checkbox = $(this);
                        const contactId = $checkbox.val();
                        const contactType = $checkbox.data('type');
                        const contactName = $checkbox.data('name');

                        console.log('📝 Processing contact:', {
                            id: contactId,
                            type: contactType,
                            name: contactName
                        });

                        if (contactName && contactName.trim() !== '') {
                            contactNames.push(contactName.trim());
                            contactIds.push(contactId);
                            selectedContacts.push({
                                id: contactId,
                                type: contactType,
                                name: contactName.trim()
                            });
                        }
                    });

                    if (contactNames.length > 0) {
                        const namesString = contactNames.join(', ');
                        $('#appointment_contact_name').val(namesString);
                        $('#selected_contact_ids').val(JSON.stringify(contactIds));
                        $('#appointment_warning').addClass('d-none');

                        console.log('✅ Contact names set:', namesString);
                        console.log('✅ Contact IDs set:', contactIds);
                    } else {
                        console.log('❌ No valid contact names found');
                        $('#appointment_warning').removeClass('d-none');
                    }
                }

                // Load calendar events
                loadCalendarEvents();

                // Show the modal
                $('#appointmentBookingModal').modal('show');
            };

            function loadCalendarEvents() {
                console.log('Loading calendar events...');
                $.ajax({
                    url: '{{ route("calendar-events.available") }}',
                    method: 'GET',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                        'Accept': 'application/json'
                    },
                    success: function(response) {
                        console.log('Calendar events response:', response);
                        const eventSelect = $('#calendar_event_select');
                        eventSelect.html('<option value="">{{ __("Select Event") }}</option>');

                        if (response.success && response.data && response.data.length > 0) {
                            response.data.forEach(function(event) {
                                eventSelect.append(`<option value="${event.id}">${event.title}</option>`);
                            });
                            console.log(`Loaded ${response.data.length} calendar events`);
                        } else {
                            console.log('No calendar events found');
                            eventSelect.append('<option value="">No events available</option>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Failed to load calendar events:', error);
                        console.error('Response:', xhr.responseText);
                        console.error('Status:', status);

                        const eventSelect = $('#calendar_event_select');
                        eventSelect.html('<option value="">Error loading events</option>');

                        let errorMessage = 'Failed to load calendar events';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }
                        show_toastr('error', errorMessage);
                    }
                });
            }

            // Calendar event selection handler
            $('#calendar_event_select').on('change', function() {
                const eventId = $(this).val();
                const locationSelect = $('#event_location_select');
                const locationValue = $('#event_location_value');

                // Reset dependent fields
                locationSelect.html('<option value="">{{ __("Please select event") }}</option>');
                locationValue.val('');
                $('#timeslots_select').html('<option value="">{{ __("Please select event and date") }}</option>');

                if (eventId) {
                    // Fetch event details to populate location options
                    $.ajax({
                        url: `/calendar-events/${eventId}/edit-data`,
                        method: 'GET',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            if (response.success && response.data) {
                                const event = response.data;

                                // Populate location options based on event settings
                                locationSelect.html('<option value="">{{ __("Select Location") }}</option>');

                                if (event.location === 'in_person' && event.physical_address) {
                                    locationSelect.append(`<option value="in_person">In Person</option>`);
                                }
                                if (event.location === 'zoom' && event.meet_link) {
                                    locationSelect.append(`<option value="zoom">Zoom</option>`);
                                }
                                if (event.location === 'skype' && event.meet_link) {
                                    locationSelect.append(`<option value="skype">Skype</option>`);
                                }
                                if (event.location === 'meet' && event.meet_link) {
                                    locationSelect.append(`<option value="meet">Google Meet</option>`);
                                }
                                if (event.location === 'others' && event.meet_link) {
                                    locationSelect.append(`<option value="others">Others</option>`);
                                }

                                // Store event data for later use
                                $('#calendar_event_select').data('event-data', event);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Failed to load event details:', error);
                            show_toastr('error', 'Failed to load event details');
                        }
                    });
                }
            });

            // Event location selection handler
            $('#event_location_select').on('change', function() {
                const selectedLocation = $(this).val();
                const eventData = $('#calendar_event_select').data('event-data');
                const locationValue = $('#event_location_value');

                if (selectedLocation && eventData) {
                    let locationValueText = '';

                    switch (selectedLocation) {
                        case 'in_person':
                            locationValueText = eventData.physical_address || '';
                            break;
                        case 'zoom':
                        case 'skype':
                        case 'meet':
                        case 'others':
                            locationValueText = eventData.meet_link || '';
                            break;
                    }

                    locationValue.val(locationValueText);
                } else {
                    locationValue.val('');
                }
            });

            // Event date change handler
            $('#event_date').on('change', function() {
                const eventId = $('#calendar_event_select').val();
                const selectedDate = $(this).val();

                if (eventId && selectedDate) {
                    loadTimeSlots(eventId, selectedDate);
                } else {
                    $('#timeslots_select').html('<option value="">{{ __("Please select event and date") }}</option>');
                }
            });

            function loadTimeSlots(eventId, date) {
                $.ajax({
                    url: '{{ route("calendar-events.timeslots") }}',
                    method: 'GET',
                    data: {
                        event_id: eventId,
                        date: date
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        const timeslotsSelect = $('#timeslots_select');
                        timeslotsSelect.html('<option value="">{{ __("Select Time Slot") }}</option>');

                        if (response.success && response.data && response.data.length > 0) {
                            response.data.forEach(function(slot) {
                                timeslotsSelect.append(`<option value="${slot.time}">${slot.time}</option>`);
                            });
                        } else {
                            timeslotsSelect.html('<option value="">{{ __("No available slots") }}</option>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Failed to load time slots:', error);
                        $('#timeslots_select').html('<option value="">{{ __("Error loading slots") }}</option>');
                    }
                });
            }

            // Save appointment booking
            $('#saveAppointmentBtn').on('click', function() {
                const form = $('#appointmentBookingForm');

                // Validate required fields
                const eventId = $('#calendar_event_select').val();
                const eventLocation = $('#event_location_select').val();
                const eventDate = $('#event_date').val();
                const timeSlot = $('#timeslots_select').val();
                const timezone = $('#timezone_select').val();

                if (!eventId || !eventLocation || !eventDate || !timeSlot || !timezone) {
                    show_toastr('error', 'Please fill in all required fields');
                    return;
                }

                const selectedContactIds = $('#selected_contact_ids').val();
                if (!selectedContactIds) {
                    show_toastr('error', 'Please select at least one contact');
                    return;
                }

                // Disable button to prevent double submission
                $(this).prop('disabled', true).text('{{ __("Saving...") }}');

                $.ajax({
                    url: '{{ route("appointment-bookings.store") }}',
                    method: 'POST',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content'),
                        event_id: eventId,
                        event_location: eventLocation,
                        event_location_value: $('#event_location_value').val(),
                        event_date: eventDate,
                        time_zone: timezone,
                        time_slots: timeSlot,
                        selected_contact_ids: selectedContactIds
                    },
                    success: function(response) {
                        if (response.success) {
                            show_toastr('success', response.message || 'Appointment booking created successfully');
                            $('#appointmentBookingModal').modal('hide');

                            // Reset form
                            form[0].reset();
                            $('#calendar_event_select').html('<option value="">{{ __("Select Event") }}</option>');
                            $('#event_location_select').html('<option value="">{{ __("Please select event") }}</option>');
                            $('#timeslots_select').html('<option value="">{{ __("Please select event and date") }}</option>');
                            $('#event_location_value').val('');

                            // Clear contact selection
                            clearSelection();
                        } else {
                            show_toastr('error', response.message || 'Failed to create appointment booking');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Failed to save appointment booking:', error);
                        let errorMessage = 'Failed to create appointment booking';

                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        show_toastr('error', errorMessage);
                    },
                    complete: function() {
                        $('#saveAppointmentBtn').prop('disabled', false).text('{{ __("ADD") }}');
                    }
                });
            });

            // Reset appointment modal when closed
            $('#appointmentBookingModal').on('hidden.bs.modal', function() {
                const form = $('#appointmentBookingForm');
                form[0].reset();
                $('#appointment_warning').addClass('d-none');
                $('#calendar_event_select').html('<option value="">{{ __("Select Event") }}</option>');
                $('#event_location_select').html('<option value="">{{ __("Please select event") }}</option>');
                $('#timeslots_select').html('<option value="">{{ __("Please select event and date") }}</option>');
                $('#event_location_value').val('');
                $('#saveAppointmentBtn').prop('disabled', false).text('{{ __("ADD") }}');
            });

            // Backup method: Populate contact names when modal is shown
            $('#appointmentBookingModal').on('shown.bs.modal', function() {
                console.log('📋 Appointment modal fully shown, double-checking contact names...');

                const currentValue = $('#appointment_contact_name').val();
                console.log('📝 Current contact name value:', currentValue);

                if (!currentValue || currentValue.trim() === '') {
                    console.log('🔧 Contact name field is empty, re-populating...');

                    // Get checked checkboxes again
                    const checkedBoxes = $('.contact-checkbox:checked');
                    const contactNames = [];
                    const contactIds = [];

                    checkedBoxes.each(function() {
                        const $checkbox = $(this);
                        const contactName = $checkbox.data('name');
                        const contactId = $checkbox.val();

                        if (contactName && contactName.trim() !== '') {
                            contactNames.push(contactName.trim());
                            contactIds.push(contactId);
                        }
                    });

                    if (contactNames.length > 0) {
                        const namesString = contactNames.join(', ');
                        $('#appointment_contact_name').val(namesString);
                        $('#selected_contact_ids').val(JSON.stringify(contactIds));
                        $('#appointment_warning').addClass('d-none');
                        console.log('✅ Contact names re-populated:', namesString);
                    } else {
                        console.log('⚠️ Still no contacts found, showing warning');
                        $('#appointment_warning').removeClass('d-none');
                    }
                } else {
                    console.log('✅ Contact name field already has value:', currentValue);
                }
            });






            // Simple test function (can be called from console)
            window.testContactNames = function() {
                console.log('🧪 Testing contact name extraction...');
                const checkboxes = $('.contact-checkbox');
                console.log('📋 Found', checkboxes.length, 'contact checkboxes');

                checkboxes.each(function(index) {
                    const $cb = $(this);
                    console.log(`Contact ${index + 1}:`, {
                        id: $cb.val(),
                        type: $cb.data('type'),
                        name: $cb.data('name')
                    });
                });

                if (checkboxes.length > 0) {
                    console.log('✅ Contact data is available');
                    return true;
                } else {
                    console.log('❌ No contacts found');
                    return false;
                }
            };
        });
    </script>
@endpush

@push('css-page')
    <style>
        /* Contact Form Modal Styles */
        .modal-xl {
            max-width: 1200px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .form-control {
            border: 1px solid #e0e6ed;
            border-radius: 6px;
            padding: 12px 16px;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .form-control::placeholder {
            color: #a0a9b8;
            font-size: 14px;
        }

        .input-group-text {
            background: #f8f9fa;
            border: 1px solid #e0e6ed;
            border-right: none;
            padding: 12px 16px;
        }

        .input-group .form-control {
            border-left: none;
        }

        .input-group .form-control:focus {
            border-left: none;
        }

        .form-check {
            padding-left: 1.5rem;
        }

        .form-check-input {
            margin-top: 0.25rem;
        }

        .form-check-label {
            font-size: 14px;
            color: #6c757d;
            margin-left: 0.5rem;
        }

        .modal-header {
            border-bottom: 1px solid #e9ecef;
            padding: 1.5rem;
        }

        .modal-body {
            padding: 2rem;
        }

        .modal-footer {
            border-top: 1px solid #e9ecef;
            padding: 1.5rem;
        }

        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
            padding: 10px 24px;
            font-weight: 500;
        }

        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
            padding: 10px 24px;
            font-weight: 500;
        }

        .text-muted {
            color: #6c757d !important;
        }

        .mb-4 {
            margin-bottom: 2rem !important;
        }

        hr {
            border-top: 1px solid #e9ecef;
            margin: 2rem 0;
        }

        .text-center {
            text-align: center !important;
        }

        .alert {
            border-radius: 6px;
            padding: 12px 16px;
            margin-bottom: 1rem;
        }

        .alert-danger {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .alert-dismissible .btn-close {
            position: absolute;
            top: 0;
            right: 0;
            z-index: 2;
            padding: 1.25rem 1rem;
        }

        @media (max-width: 768px) {
            .modal-xl {
                max-width: 95%;
            }

            .modal-body {
                padding: 1rem;
            }
        }

        /* Appointment Booking Modal Styles */
        #appointmentBookingModal .modal-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        #appointmentBookingModal .modal-title {
            color: #495057;
            font-weight: 600;
        }

        #appointmentBookingModal .form-label {
            color: #6c757d;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        #appointmentBookingModal .form-control {
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            padding: 0.75rem;
            font-size: 0.875rem;
        }

        #appointmentBookingModal .form-control:focus {
            border-color: #86b7fe;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        #appointmentBookingModal .btn-success {
            background-color: #198754;
            border-color: #198754;
            font-weight: 500;
            padding: 0.5rem 1.5rem;
        }

        #appointmentBookingModal .btn-success:hover {
            background-color: #157347;
            border-color: #146c43;
        }

        #appointmentBookingModal .alert-warning {
            background-color: #fff3cd;
            border-color: #ffecb5;
            color: #664d03;
            border-radius: 0.375rem;
            padding: 0.75rem 1rem;
        }

        #appointmentBookingModal .modal-footer {
            border-top: 1px solid #dee2e6;
            padding: 1rem 1.5rem;
        }

        /* Custom select styling */
        #appointmentBookingModal select.form-control {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 16px 12px;
            padding-right: 2.5rem;
        }

        /* Responsive adjustments */
        @media (max-width: 576px) {
            #appointmentBookingModal .modal-dialog {
                margin: 0.5rem;
            }

            #appointmentBookingModal .modal-body {
                padding: 1rem;
            }
        }

        /* Preview Contact Modal Styles */
        #previewContactModal .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom: none;
        }

        #previewContactModal .modal-title {
            font-weight: 600;
            font-size: 1.25rem;
        }

        #previewContactModal .btn-close {
            filter: brightness(0) invert(1);
        }

        #previewContactModal .modal-body {
            padding: 2rem;
            background-color: #f8f9fa;
        }

        #previewContactModal .card {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            border: none;
            border-radius: 12px;
        }

        #previewContactModal .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,128,0,0.2) !important;
        }

        #previewContactModal .avatar-lg {
            width: 80px;
            height: 80px;
        }

        #previewContactModal .avatar-title {
            font-size: 2rem;
        }

        #previewContactModal .modal-footer {
            border-top: 1px solid #dee2e6;
            background-color: white;
        }

        /* Loading animation for preview */
        #previewContactModal .spinner-border {
            width: 3rem;
            height: 3rem;
        }

        /* Responsive adjustments for preview modal */
        @media (max-width: 768px) {
            #previewContactModal .modal-xl {
                max-width: 95%;
            }

            #previewContactModal .modal-body {
                padding: 1rem;
            }

            #previewContactModal .avatar-lg {
                width: 60px;
                height: 60px;
            }

            #previewContactModal .avatar-title {
                font-size: 1.5rem;
            }
        }
    </style>
@endpush
