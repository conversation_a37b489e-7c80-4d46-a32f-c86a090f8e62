{"__meta": {"id": "X5bdfc45ed1c1e2a4a097d79600385606", "datetime": "2025-07-29 05:29:10", "utime": **********.397578, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753766949.500469, "end": **********.397604, "duration": 0.8971350193023682, "duration_str": "897ms", "measures": [{"label": "Booting", "start": 1753766949.500469, "relative_start": 0, "end": **********.323328, "relative_end": **********.323328, "duration": 0.8228590488433838, "duration_str": "823ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.323339, "relative_start": 0.****************, "end": **********.397615, "relative_end": 1.0967254638671875e-05, "duration": 0.*****************, "duration_str": "74.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2999\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1805 to 1811\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1805\" onclick=\"\">routes/web.php:1805-1811</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "LosUR9mVCMNFLaBHm3du4ShroeH0UkN10D3vXl27", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-539270944 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-539270944\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-282865379 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-282865379\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1177797662 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1177797662\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1930687971 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1930687971\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1157968255 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1157968255\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-630342909 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:29:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjA2dFdVUnArMG41TzYzNnIxb3FKQ3c9PSIsInZhbHVlIjoiZ0plREhXRWNMbUFweUlhb2VRMGMvRVdiLyt2RndRL2ZJLzdmbXJCSzhKL2NuSXlwdVljTzRZVWV3UUlmdW5MbDd1WEdnQkUyZ1Exc2w5SWRZZFlYR3J1eXpZTXRwSnlPVURwVThhb29OVElMMkNoclkvSC9jKzA1RUFTSXFmNnoybTZRMEd0MzR6RmFCRi9EWDdGY1lPMEdEaS9RQ1FhcFFGdjJselc3WDlqQ09xOUFxb0RTSTJHckN2eGc1OVF3Snh2cytxdytGUFo2c2xYTXFPZGxlVkNCTWJRTzJzcWhTNERPKzVFUkN1TThjaWdtemNQdVNTQWcvYU10aEhLb0cwaE51cTBDUlZFbzFnWDFwWnNJMTNUMGdxVC9BS1hoak1aQzQ4aXJOZTNhU2xYN3pGY1pxL1FrbXl0MnJqTk9yZWxyQWp6cGY2SGJBSWxId0JieFdRNVBqRWNIb3NCVjVEWGw5S2FNbzBneXAvTlh3VEtHekdyUGlBaExtMklwWklPT2xrMUZld2dJR09sSWdhYStUSVlWZ1pMcmx2STVJek5JbHdpWGg1R0oyM0hkS2loNUpxSHRiTmw2UE1nUHBXS0JDZVZXK0V3VzZkQWxmUXBNSXJMdzVIWDJFVENvOVRRSnpxZXMvRytqbnkzekRHb0pzVVNueFY1eEptRjQiLCJtYWMiOiI1YTBmNzk0ZTYxYzhmZTNkYmQ5YmUyYjY0ODc4NDgxZDliM2JkYzJkZjQ5ZGE2YWNjMjZmYjY5ZWJkOWM3ZDViIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:29:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IklpU2tjT3IrdDlQTkF2TmZiYnpFOWc9PSIsInZhbHVlIjoiQzg5VlhjdGFEZXcrQzdVb2NLbCtvV0VhQ1FGVVUxM2ZyZUdWQ0tqdjlTaEVBV1FtdnZlNmJLdzVHWDdvTWE4UWMveXVUK3RmMzdiUmZyZWVVTWZ3aTdMZjAxUEJ0NWtoWFE0UldjQ3lYc1F5MjUrbWg4ckhUMEM5QTBWTnpkaEQ5Mm8xTTFUcUl5d0pZdzFvaHRad3JUalNJMkJTSGdSZi9oMzVraDYvSkF6SThEU1psVzdwUTNuVjZ4SEZ2ak5JdjdkQzRtN3k2L08zV2JrY0lFSTNvNi9WRUJNRHJwQ2pUWklaaDFrNHhDS2xyOEhIUk5XeTQzRTBKVmdOSUNSbzB6d1NWOTNmekgvMWQzT0ZQeGtzUk95elBzRlNxSTVoR25zY05kY21CQkdCQzVJOSt3aWc0QUxJZkg3NGoyY2dRbnd3NjltdVNBemFaNWlta2RSc3FZNTlsVXFLUEpuN0JveEVUcDJVT3FRNnp0NTlEZlkrK21EaktiUUxSZnFCb2pTWkR2eStBZFpVYXNVc1hva3hqWW1EKzhTejJyN3hJMC9oTXphdElSNk1HUmJ0TUdkc05DaEhNWWtnbSt4Yzh2dlg0ZUtPMWloeWhFTjlHS3c4WGswNm5vZjVHNFBJVThWTXBNUDZ5UDI2YmFGZGxQMEhvdnJSY0gxbkJISkQiLCJtYWMiOiJjNmU5N2ZmNDlmNWUxYzAyNWI4ZDc3Nzg4MDAyNDEyMmY3NjY0OTI0YWQ1ODFkMjYzMDQ0ZDAxNzgyY2QzNTEyIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:29:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjA2dFdVUnArMG41TzYzNnIxb3FKQ3c9PSIsInZhbHVlIjoiZ0plREhXRWNMbUFweUlhb2VRMGMvRVdiLyt2RndRL2ZJLzdmbXJCSzhKL2NuSXlwdVljTzRZVWV3UUlmdW5MbDd1WEdnQkUyZ1Exc2w5SWRZZFlYR3J1eXpZTXRwSnlPVURwVThhb29OVElMMkNoclkvSC9jKzA1RUFTSXFmNnoybTZRMEd0MzR6RmFCRi9EWDdGY1lPMEdEaS9RQ1FhcFFGdjJselc3WDlqQ09xOUFxb0RTSTJHckN2eGc1OVF3Snh2cytxdytGUFo2c2xYTXFPZGxlVkNCTWJRTzJzcWhTNERPKzVFUkN1TThjaWdtemNQdVNTQWcvYU10aEhLb0cwaE51cTBDUlZFbzFnWDFwWnNJMTNUMGdxVC9BS1hoak1aQzQ4aXJOZTNhU2xYN3pGY1pxL1FrbXl0MnJqTk9yZWxyQWp6cGY2SGJBSWxId0JieFdRNVBqRWNIb3NCVjVEWGw5S2FNbzBneXAvTlh3VEtHekdyUGlBaExtMklwWklPT2xrMUZld2dJR09sSWdhYStUSVlWZ1pMcmx2STVJek5JbHdpWGg1R0oyM0hkS2loNUpxSHRiTmw2UE1nUHBXS0JDZVZXK0V3VzZkQWxmUXBNSXJMdzVIWDJFVENvOVRRSnpxZXMvRytqbnkzekRHb0pzVVNueFY1eEptRjQiLCJtYWMiOiI1YTBmNzk0ZTYxYzhmZTNkYmQ5YmUyYjY0ODc4NDgxZDliM2JkYzJkZjQ5ZGE2YWNjMjZmYjY5ZWJkOWM3ZDViIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:29:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IklpU2tjT3IrdDlQTkF2TmZiYnpFOWc9PSIsInZhbHVlIjoiQzg5VlhjdGFEZXcrQzdVb2NLbCtvV0VhQ1FGVVUxM2ZyZUdWQ0tqdjlTaEVBV1FtdnZlNmJLdzVHWDdvTWE4UWMveXVUK3RmMzdiUmZyZWVVTWZ3aTdMZjAxUEJ0NWtoWFE0UldjQ3lYc1F5MjUrbWg4ckhUMEM5QTBWTnpkaEQ5Mm8xTTFUcUl5d0pZdzFvaHRad3JUalNJMkJTSGdSZi9oMzVraDYvSkF6SThEU1psVzdwUTNuVjZ4SEZ2ak5JdjdkQzRtN3k2L08zV2JrY0lFSTNvNi9WRUJNRHJwQ2pUWklaaDFrNHhDS2xyOEhIUk5XeTQzRTBKVmdOSUNSbzB6d1NWOTNmekgvMWQzT0ZQeGtzUk95elBzRlNxSTVoR25zY05kY21CQkdCQzVJOSt3aWc0QUxJZkg3NGoyY2dRbnd3NjltdVNBemFaNWlta2RSc3FZNTlsVXFLUEpuN0JveEVUcDJVT3FRNnp0NTlEZlkrK21EaktiUUxSZnFCb2pTWkR2eStBZFpVYXNVc1hva3hqWW1EKzhTejJyN3hJMC9oTXphdElSNk1HUmJ0TUdkc05DaEhNWWtnbSt4Yzh2dlg0ZUtPMWloeWhFTjlHS3c4WGswNm5vZjVHNFBJVThWTXBNUDZ5UDI2YmFGZGxQMEhvdnJSY0gxbkJISkQiLCJtYWMiOiJjNmU5N2ZmNDlmNWUxYzAyNWI4ZDc3Nzg4MDAyNDEyMmY3NjY0OTI0YWQ1ODFkMjYzMDQ0ZDAxNzgyY2QzNTEyIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:29:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-630342909\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2058458728 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LosUR9mVCMNFLaBHm3du4ShroeH0UkN10D3vXl27</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2058458728\", {\"maxDepth\":0})</script>\n"}}