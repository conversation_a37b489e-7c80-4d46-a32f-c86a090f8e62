{"__meta": {"id": "X8c45f6dda0848bca5dac4c1bf0b686d2", "datetime": "2025-07-29 05:03:31", "utime": **********.836843, "method": "GET", "uri": "/contact-groups/available-contacts", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753765410.901756, "end": **********.836872, "duration": 0.9351160526275635, "duration_str": "935ms", "measures": [{"label": "Booting", "start": 1753765410.901756, "relative_start": 0, "end": **********.704175, "relative_end": **********.704175, "duration": 0.8024189472198486, "duration_str": "802ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.70419, "relative_start": 0.802433967590332, "end": **********.836875, "relative_end": 2.86102294921875e-06, "duration": 0.13268494606018066, "duration_str": "133ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46638856, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET contact-groups/available-contacts", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\ContactGroupController@getAvailableContacts", "namespace": null, "prefix": "", "where": [], "as": "contact-groups.available-contacts", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=221\" onclick=\"\">app/Http/Controllers/ContactGroupController.php:221-260</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0121, "accumulated_duration_str": "12.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 84 limit 1", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7862349, "duration": 0.01039, "duration_str": "10.39ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 0, "width_percent": 85.868}, {"sql": "select * from `settings` where `created_by` = 84", "type": "query", "params": [], "bindings": ["84"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.815969, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_systam_db", "start_percent": 85.868, "width_percent": 7.273}, {"sql": "select `id`, `name`, `email`, `phone` from `leads` where `created_by` = 84 and `contact_group_id` is null and `is_active` = 1 and `is_deleted` = 0 order by `name` asc", "type": "query", "params": [], "bindings": ["84", "1", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ContactGroupController.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\app\\Http\\Controllers\\ContactGroupController.php", "line": 231}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.821196, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "ContactGroupController.php:231", "source": "app/Http/Controllers/ContactGroupController.php:231", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FContactGroupController.php&line=231", "ajax": false, "filename": "ContactGroupController.php", "line": "231"}, "connection": "omx_sass_systam_db", "start_percent": 93.14, "width_percent": 6.86}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/contact-groups\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "84"}, "request": {"path_info": "/contact-groups/available-contacts", "status_code": "<pre class=sf-dump id=sf-dump-2055978130 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2055978130\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2116765776 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2116765776\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1122283936 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1122283936\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IllTK2RCSG04akVmTlhLRWZ4cVBiRFE9PSIsInZhbHVlIjoiSDlkTTJrWHQ2ZVF2OVNBZmF0YWtWaUlmMHBuRzZqUm5NUHVzeFh5T0lWVngwYWhXUmlLaFdkcWRRcHo4T2U0cG9na2swMG54NTBUeEZYc2xEQnJaMXdzbTIrWlNIb254S291NHk1OFJaM0laZ3ZhdUxEdXZxMjNGM1JUZTA1WklEa1A3QTAzVnpEV3BUaERXYXVZMDhQVXQrRGhVMWFuNTJEMlNPUzlZRDVDVVZGTEhGS3BYczFCdWsrYjA3ZVpEWi9KYlB2d0d4U1hKZkEvYXYveGVpSFZDKzVVR3JVWlVsQUNMZEtBcE1LdThIekNJZlEzVFpYSERibTFlRkdGS094QVhGVENndmlxM1lTaC95aWNwNmJ4eVQ0Z1pqNHc3U2Y4anpJQUU3ZkpUSU5zenFRTVBqb3J0V1l4TnRqR0pvc0JQQzNVeE9IS2RESWZzSnAwdFRQdlpubkhubzNGTjY4aGZmbjNNdkRvR3Evem8wVDlCSytoODBTNENZZmJMNUlHZlZmRC9ieFpmbm95Vno1TmVGajJ1Nm1QdzY4OTdpTnFlNnNBaGNISXAydWFtYjRtQ0JhdFVlUkhEdzg1MWJ3dE5ubW9nbktuRGo4bWRBSlR0UXBpZGRHMENRU1ZXYkZNNElsNS9iRVppU3MwRWNxUXU5N3BOREFhZE9YOEciLCJtYWMiOiI2MTNmZDU5OWI2Yzk3ZTZmOGMyNjljYWNiMmZmZDk0MTBlYzFjYjE0MWNlMDFlZDQyOTA1YzQ5MDBmZmY0YmI0IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6InV6SGR3RXN2OU5NVTROMHVlL1hlTkE9PSIsInZhbHVlIjoiUExSZkN1RGtjZENGSnFHQ1F1YzlGajFJSm5hR0tVWXRrdW5iYmFJTHZaTHcrYk1XVTI1TVQyWmZMRnA5UVlCUzNXRVN0SzFQU29hWEtna0hNMldFSlFGRjdMSlNzdzZUeWc3bjYwbHlhVkJralluNzFhOEJ0bHZWNFFiZW14QlAvSDZ2YUpjZG5Ec2tndFZScUs5SEVoN1lYL05Rb01XYjhJQjdvV3V3aTZNdzRxL0hEbElhREhnTWQwbFhSRngvWUIxSlRhWXRzOGtTRDgyc0wzMTVaU3dUeFdqdXVLeGVFMmdhNlZvcXdQRGJFYkJBOUdQQlJ2YUdocUtEbzI4OHFkcWxhT2pDc1lUbS9aNWZGcU1BOGEzcVFJN3JTRktxZ3A3R1l5ei9YVG85TWRKbzhBL2dUYXhNaXhiRWZ4NUFkUXRuT0R0WGVTZlhuclREUFY2aDJXQXUwTEViYlhNeVhQZnNlQU1lSHc4ZlErWTIrcW1tZlZ3eXA3eFhOT1lZdVNXZjZTZlZ3VzdoeXN2OENUS05xMGRZcy9nMnZxRGFsMElmVmJydHV2UmlseDg3ZU1BLytDYVFLUHZiczVUZFFyRzFnTlo3eTB1SVZGcVFZTjNvZThNcWdTemlKelNNZURKekZHdFYvMzdhL3F3bVVYM3ZhRkh0VmFSdVZkbXoiLCJtYWMiOiIyNzZkZjE4MDM1OTJiYTFlY2JiYzE1NWZmYTdmZWVhYTgyNTZkZWQzYTc3ZDgwOWM3M2JiNDYzZGJiYmUzODBjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1967420398 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">1XDrxTWmYv3gorVrXvmSodhuVHNiE4Wue14QMI4v</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1967420398\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1467187263 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:03:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhWQTMrQ0hjbzJQWUJFNkIrUHpoZWc9PSIsInZhbHVlIjoid2lzUGNJNXhSSTZaVE81NWdRVm1jUy9JMFpKSWlESzROSXlOQnJRRGVRNEIvQUtTSEZCdUpFL1pLd0lVOW40SXdmeEdOZ1hRTEdFVWNpZVluT2tIdmhIN0kxWnovRnptUG9uZERtbzVsSVk5bmFkNjNzUGdTRTkzMlNNNVhBdEV2MjI5VDFNbmNDRTBUZUczbHRuY1lOY2ZxV2JPNjBtamhwUERtQ0NpWkF3YzlUZ3FCKzVHUUJLTXFwZXk0eFZ4dk1qS1lYWnlKNG1qdUtmRU84dm84T2x1a2Exb3NDaWVPUjdEY2xoeHdkMXJlVDcvT0ZkN3hCS0lJR0RQcTZmMnljV3VNK2xnOGpRTG9EQUVhdWQ3THRzeWw0elEzbXNaWlhzdDZrTTlpb204dHp4UGJxa0lHcDNzSnJXWnZlYTVhdDlQSEtEbG5MUzN3YWd6TTA5SkM4VXVQaWhaWlFtWEdYT3Roem8za1JTWEJtNFZiNWV6bWV1OHBmOGNvTUNTbWxZeTlzcjZtSnJOdnZKamlJYy9EeWE0SEFpWHhkekZGdGR0L1o2bDdIYUptbEtDVWxLQmVpb3E4dzdiMFdURDV0L29qVjFoM21DbUFyZlFuOURSTlVjeGJGTlk3alpDNDkyc1lJNE9EYmRjTlB3RXgyQ1ppRm05dk0vWWI3VDIiLCJtYWMiOiJhZDZjNGE5ODE2ZjJiNTgxZGI0ZjhmNzljYTM2YzA2MWVkZjFjODkyMjk2YmIyM2U4YmM0YjdhMTBjOTRkZWVlIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:03:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImtVTDRQb2k0MXo4WmprMGp3ZGFCbGc9PSIsInZhbHVlIjoialN1dmlRejhCVzkzUitFZ1ViU0lwZ0lxbUhPVzBDVWFjZEFkRERPYm5nZUpDUFp4S0lFMHQycXJkUDBPSUdnQ2J6UGJtS1EzamxzeHljR1FTWllydDMxZTIxNDV2WnFHZzZwbElWODZxY2p5NDVnMU40Q2wwNzdMaldybTZYTGxiVUExTkZIbWJlZnBlVVZqUHcvbWJPc3YxVER2OHVoNmM5b1k1MFQvNU0vY085NnRIYzFvZGd6YTR6ZlJCWm5mWVhRanczM21vZDcvOGV5aVJEem8yc2JjaXhHZmZkOWFQLzRoNWE3VnhxMUVhQ042bjdzcVN6amJmRHZYUXRaanAyeTVqZnMrWVB5cjNtMVVjUXZpZVFiTXcva1VsZkZIQlZjTlRhbG5QdUZVcGFUUTBvMTE5L1ZzRHIxRGN0ODA4c2RIb0xLUnNtWlAxd20vdzRuNjN4QlRER0IyeGhtTTB0QWdUN1N4cjRBLzBiQ0I2MHVVbEUyOG1jQzhabTBEL1hBSGRBc2N2YWlJemQ0ODB1RU1yWTRadCtDRGpISWhQZGxSdFFkZWttdnlpMENad21NZE1YenpLMldnTGtyMVRYaXd2UGJZLzc5UU1FeVdKMGFyM0FmWGxHSm8xZXZueElyV0dUc1JCZnVDdWZNZ01DV29WdFJrVWtjQ2h0emQiLCJtYWMiOiI5MmNkOWE0NmVmY2UzNDYzYTFmNGE4ZTdkNDIxNzIxODJiMzIzZjFhZDg3YWM4M2MxM2MyNDRlMDJhNDczOWM3IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:03:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhWQTMrQ0hjbzJQWUJFNkIrUHpoZWc9PSIsInZhbHVlIjoid2lzUGNJNXhSSTZaVE81NWdRVm1jUy9JMFpKSWlESzROSXlOQnJRRGVRNEIvQUtTSEZCdUpFL1pLd0lVOW40SXdmeEdOZ1hRTEdFVWNpZVluT2tIdmhIN0kxWnovRnptUG9uZERtbzVsSVk5bmFkNjNzUGdTRTkzMlNNNVhBdEV2MjI5VDFNbmNDRTBUZUczbHRuY1lOY2ZxV2JPNjBtamhwUERtQ0NpWkF3YzlUZ3FCKzVHUUJLTXFwZXk0eFZ4dk1qS1lYWnlKNG1qdUtmRU84dm84T2x1a2Exb3NDaWVPUjdEY2xoeHdkMXJlVDcvT0ZkN3hCS0lJR0RQcTZmMnljV3VNK2xnOGpRTG9EQUVhdWQ3THRzeWw0elEzbXNaWlhzdDZrTTlpb204dHp4UGJxa0lHcDNzSnJXWnZlYTVhdDlQSEtEbG5MUzN3YWd6TTA5SkM4VXVQaWhaWlFtWEdYT3Roem8za1JTWEJtNFZiNWV6bWV1OHBmOGNvTUNTbWxZeTlzcjZtSnJOdnZKamlJYy9EeWE0SEFpWHhkekZGdGR0L1o2bDdIYUptbEtDVWxLQmVpb3E4dzdiMFdURDV0L29qVjFoM21DbUFyZlFuOURSTlVjeGJGTlk3alpDNDkyc1lJNE9EYmRjTlB3RXgyQ1ppRm05dk0vWWI3VDIiLCJtYWMiOiJhZDZjNGE5ODE2ZjJiNTgxZGI0ZjhmNzljYTM2YzA2MWVkZjFjODkyMjk2YmIyM2U4YmM0YjdhMTBjOTRkZWVlIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:03:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImtVTDRQb2k0MXo4WmprMGp3ZGFCbGc9PSIsInZhbHVlIjoialN1dmlRejhCVzkzUitFZ1ViU0lwZ0lxbUhPVzBDVWFjZEFkRERPYm5nZUpDUFp4S0lFMHQycXJkUDBPSUdnQ2J6UGJtS1EzamxzeHljR1FTWllydDMxZTIxNDV2WnFHZzZwbElWODZxY2p5NDVnMU40Q2wwNzdMaldybTZYTGxiVUExTkZIbWJlZnBlVVZqUHcvbWJPc3YxVER2OHVoNmM5b1k1MFQvNU0vY085NnRIYzFvZGd6YTR6ZlJCWm5mWVhRanczM21vZDcvOGV5aVJEem8yc2JjaXhHZmZkOWFQLzRoNWE3VnhxMUVhQ042bjdzcVN6amJmRHZYUXRaanAyeTVqZnMrWVB5cjNtMVVjUXZpZVFiTXcva1VsZkZIQlZjTlRhbG5QdUZVcGFUUTBvMTE5L1ZzRHIxRGN0ODA4c2RIb0xLUnNtWlAxd20vdzRuNjN4QlRER0IyeGhtTTB0QWdUN1N4cjRBLzBiQ0I2MHVVbEUyOG1jQzhabTBEL1hBSGRBc2N2YWlJemQ0ODB1RU1yWTRadCtDRGpISWhQZGxSdFFkZWttdnlpMENad21NZE1YenpLMldnTGtyMVRYaXd2UGJZLzc5UU1FeVdKMGFyM0FmWGxHSm8xZXZueElyV0dUc1JCZnVDdWZNZ01DV29WdFJrVWtjQ2h0emQiLCJtYWMiOiI5MmNkOWE0NmVmY2UzNDYzYTFmNGE4ZTdkNDIxNzIxODJiMzIzZjFhZDg3YWM4M2MxM2MyNDRlMDJhNDczOWM3IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:03:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1467187263\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-59629245 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pvmR7pkSUplPC9vrNCijkQ1ik5HHP09VuQ9Nsi4U</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>84</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-59629245\", {\"maxDepth\":0})</script>\n"}}