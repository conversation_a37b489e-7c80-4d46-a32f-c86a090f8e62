{"__meta": {"id": "Xf3570c887fc31e62fba9416bfd0a6164", "datetime": "2025-07-29 05:08:00", "utime": **********.008166, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753765678.810435, "end": **********.008198, "duration": 1.1977629661560059, "duration_str": "1.2s", "measures": [{"label": "Booting", "start": 1753765678.810435, "relative_start": 0, "end": 1753765679.922888, "relative_end": 1753765679.922888, "duration": 1.1124529838562012, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1753765679.922906, "relative_start": 1.****************, "end": **********.008201, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "85.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2997\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1801 to 1807\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1801\" onclick=\"\">routes/web.php:1801-1807</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "IW3JCWFFqyUstmt0CdmN9vCgfungFZO2EwpeYQYg", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-232630485 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-232630485\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1040109579 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1040109579\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-653465218 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-653465218\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1226125493 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1226125493\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1350456353 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1350456353\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1875643974 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:07:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlR6QXp2dzlpT0tKeVdINEc1aE1jNHc9PSIsInZhbHVlIjoiNUxaZkZXUk9iTVJEendieFRWTVZZVDdZRHRVaFRpWUdzcEJBM3RlVG1YU1g1S0V6TUxrZUplZ0NQWXFWdm9HQWtnOWVZTGdBZHJZang0UTFmVGl5SzVQbFVCTTJ1MHdaRUwxU2JCT0U1OXNqNTZPVkdmRzVYZVNuenZSRVcxdjFFaE9rNWZ6YjFFU0ZRRGloRi9MbW5RSWl6S2JXbjZJNmhIYXkxQUlRWTVRNllVV016S3l6MUZobnNWZm95dmJuaXluaVpnRXJFL0FnQjBWOWFoMVFidFNxM0poa1FCdEQyc1o1V0hrYjloL25VVEYzMmNNaDZOOVRscVNVU2hoOG5qOWVpVHFqNlRsbVVnaUFOVm9vS0V1UHVOSGJQS1hTRnF5ZDBiMmtJRUF2WG5pR1UzTDRvNUpoT1NDREtNTDZya0E0SGNEZlhJaFY5NU10eWlMSlgwKys5anQyc2tHNGh3c1E4VzJINms5ZnpwZ1Jxb2JWRTZmcTdXQ25veEQ0a3N4QlliWkMvR0t0dkhpd1QwYkJsQlJuaWplZURtaTI4WUV0cDZqQ04zV2hidHlkK0ZrYktjVDBoaHpWRTdpUVV0RlByaUI2RENlVytrdnl6RU5vRFdaNnFxRVFsVFFKb1dOUVJuVVhUS09wbWFWS0t4V1ZCZTZSU1NDSm56cW4iLCJtYWMiOiI1ZWVmYmFkOTJmYzQ0MTNjOGNlZWUwODEwMzkwM2VhM2Y2YjEyNmRkN2QyMTY2YzIyNDA4N2NmOTVlZDg0ZDdmIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:07:59 GMT; Max-Age=7199; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6IkcrcHV6Vi9aM2diTk82NkdtMmZWbWc9PSIsInZhbHVlIjoidTVVRTdoMDlhS2VYNURXNThoVXhNek9YZEh5SUNzcUd3d2xXTmtyOTk2d1V1cGtQRG1xSHNSdkx4ZlRhd2FBbTBUY3Y1TGRkMXBZYUNROW5QK3hXYklrRWZpSHpZTjJLWDZ1MFdTZW9Bb2RyMUxJZGUyQmVLZ0t0dTV1VU5sSXcrcXNmMmoyMWRuRHFRaWlDcGVhcFpEV1hBZG5QWk1UWGkxNVVBdHR0eFQ1VXhpdEM2ZU1CYktYTE1zVEpJbHRJc1FYVERaaXB0NklJNDNQR0RxUU5INzlJWEtmTTlDcU54QlFkVnhmUFVUUlo1S1BlMWl0TnZLQW85bWNEd1JOaEVZMjVoZnVSRm02Q2NwdEw1RklqZzcwQi80cjE3OFBtMnVpTWkydlpocE9iSk5yeGhkby9jaTk0MkZuSURoRlgrNWdaa0dJNmRLSEg5cDFjeUZaZzFRM1dFL3M1OVVibWVmQmhNa1RXemphRDgvZVZFNzhDT1p2Y2V4dURhOW5yb0poMHJ2cDlqaFhSRDFqcGpobENRWU45RXJTWVVkLzVuSFJNOXNHakIwcllHdDg2TENhVkc1TzgzZ0haZG52SXA3RWhDVzBOMURQU1U4N1VzelJGZThCdE93WGJMRm5Nc1UreExnK1VsQldWb0p3UDVHRmNKV0wzS1pjd244dHAiLCJtYWMiOiJhMTc5YjgyMTlkODNiMmNhOWYyYmZhN2I1Y2NmMmQ2MzE4YWY5Mjk3OTBmZTBkMGQ5ZGE0NmZhZjNiYWZmYWI5IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:07:59 GMT; Max-Age=7199; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlR6QXp2dzlpT0tKeVdINEc1aE1jNHc9PSIsInZhbHVlIjoiNUxaZkZXUk9iTVJEendieFRWTVZZVDdZRHRVaFRpWUdzcEJBM3RlVG1YU1g1S0V6TUxrZUplZ0NQWXFWdm9HQWtnOWVZTGdBZHJZang0UTFmVGl5SzVQbFVCTTJ1MHdaRUwxU2JCT0U1OXNqNTZPVkdmRzVYZVNuenZSRVcxdjFFaE9rNWZ6YjFFU0ZRRGloRi9MbW5RSWl6S2JXbjZJNmhIYXkxQUlRWTVRNllVV016S3l6MUZobnNWZm95dmJuaXluaVpnRXJFL0FnQjBWOWFoMVFidFNxM0poa1FCdEQyc1o1V0hrYjloL25VVEYzMmNNaDZOOVRscVNVU2hoOG5qOWVpVHFqNlRsbVVnaUFOVm9vS0V1UHVOSGJQS1hTRnF5ZDBiMmtJRUF2WG5pR1UzTDRvNUpoT1NDREtNTDZya0E0SGNEZlhJaFY5NU10eWlMSlgwKys5anQyc2tHNGh3c1E4VzJINms5ZnpwZ1Jxb2JWRTZmcTdXQ25veEQ0a3N4QlliWkMvR0t0dkhpd1QwYkJsQlJuaWplZURtaTI4WUV0cDZqQ04zV2hidHlkK0ZrYktjVDBoaHpWRTdpUVV0RlByaUI2RENlVytrdnl6RU5vRFdaNnFxRVFsVFFKb1dOUVJuVVhUS09wbWFWS0t4V1ZCZTZSU1NDSm56cW4iLCJtYWMiOiI1ZWVmYmFkOTJmYzQ0MTNjOGNlZWUwODEwMzkwM2VhM2Y2YjEyNmRkN2QyMTY2YzIyNDA4N2NmOTVlZDg0ZDdmIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:07:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6IkcrcHV6Vi9aM2diTk82NkdtMmZWbWc9PSIsInZhbHVlIjoidTVVRTdoMDlhS2VYNURXNThoVXhNek9YZEh5SUNzcUd3d2xXTmtyOTk2d1V1cGtQRG1xSHNSdkx4ZlRhd2FBbTBUY3Y1TGRkMXBZYUNROW5QK3hXYklrRWZpSHpZTjJLWDZ1MFdTZW9Bb2RyMUxJZGUyQmVLZ0t0dTV1VU5sSXcrcXNmMmoyMWRuRHFRaWlDcGVhcFpEV1hBZG5QWk1UWGkxNVVBdHR0eFQ1VXhpdEM2ZU1CYktYTE1zVEpJbHRJc1FYVERaaXB0NklJNDNQR0RxUU5INzlJWEtmTTlDcU54QlFkVnhmUFVUUlo1S1BlMWl0TnZLQW85bWNEd1JOaEVZMjVoZnVSRm02Q2NwdEw1RklqZzcwQi80cjE3OFBtMnVpTWkydlpocE9iSk5yeGhkby9jaTk0MkZuSURoRlgrNWdaa0dJNmRLSEg5cDFjeUZaZzFRM1dFL3M1OVVibWVmQmhNa1RXemphRDgvZVZFNzhDT1p2Y2V4dURhOW5yb0poMHJ2cDlqaFhSRDFqcGpobENRWU45RXJTWVVkLzVuSFJNOXNHakIwcllHdDg2TENhVkc1TzgzZ0haZG52SXA3RWhDVzBOMURQU1U4N1VzelJGZThCdE93WGJMRm5Nc1UreExnK1VsQldWb0p3UDVHRmNKV0wzS1pjd244dHAiLCJtYWMiOiJhMTc5YjgyMTlkODNiMmNhOWYyYmZhN2I1Y2NmMmQ2MzE4YWY5Mjk3OTBmZTBkMGQ5ZGE0NmZhZjNiYWZmYWI5IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:07:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1875643974\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-937679817 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">IW3JCWFFqyUstmt0CdmN9vCgfungFZO2EwpeYQYg</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-937679817\", {\"maxDepth\":0})</script>\n"}}