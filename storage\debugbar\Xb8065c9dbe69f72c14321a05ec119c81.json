{"__meta": {"id": "Xb8065c9dbe69f72c14321a05ec119c81", "datetime": "2025-07-29 04:52:19", "utime": **********.675138, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753764738.45852, "end": **********.675167, "duration": 1.2166471481323242, "duration_str": "1.22s", "measures": [{"label": "Booting", "start": 1753764738.45852, "relative_start": 0, "end": **********.567252, "relative_end": **********.567252, "duration": 1.108731985092163, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.567266, "relative_start": 1.**************, "end": **********.67517, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "108ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2997\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1801 to 1807\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1801\" onclick=\"\">routes/web.php:1801-1807</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "TvkmTSIwfIpGj65hFdj5LXIqhMw0ABBscCckixN6", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1466246244 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1466246244\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-768047481 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-768047481\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1334640104 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1334640104\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1246182824 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1246182824\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1744834769 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1744834769\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1409417088 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 04:52:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJOeHU2RHpaOTdvSjZXSjQ4QzR6NUE9PSIsInZhbHVlIjoiZTlFSER0Z0VoQjJRamNLSHpsR1h6YWo4cFkzQWsvT0Q1SFBkTTliaDV3N1ExaWR4UitoalQ4UXNYbnhnQjVtK2JKc1YzWW1LQm94aHF1WUt0YzI0blFEd0FuTmpmTFg0NU1Xb1ZTdFVid3RhSFhIdXI4dmRWMXJiZkNneVhXc0pZbzhBaXpTcHNKUVBOZEhXYm5tTTlGVUk5K0xaTzdIRnFvQlcrdy9rWTlWRDRBYlplSmRqSmp1ckRtMjVFWk1xTUVzVUVuS01lOW5GNGE3TUV4UnBOY3NhaGtacElLak5PMWFVNjJwU255Uldmb0xCVkp0ZDdPMWtBalVzY2ttV3hicDUwSndxOVZFT1NNcnV1TW5WNGlPL3FCWVVSanVSTjBVc054V3RGeHZhVUE4ZEpKL25BYnZ4a3kxTlpvU0tjNEpYMTRJa0lOdkt4Tm45Q0VqK0Nsc3BDVkRCZUxqVmJaZVdqemE5Q1Z3TXRLSlhsSVNYSTE0RDBYU3B6L3kzMzJyWldka0tud1dMcmFDNmVyZ1FCY2pHWXpYMk02azNnN2NSNldraUhUQmFBSW5uNEEwNXA3UGZPL0liNUxWS056R2x0ZTRHeE0wYk1MY1hjdmNzSlNQY0RrS29HaU5BTlljWUxFUDRzRUl3cVAwVmp6R3JsWW83UzZCTzlUTEQiLCJtYWMiOiJlYzA4Y2JmZTBiYjI0ZTk0NGZiOTc4ODdkN2ZmN2YwNzNiYzRkYTI2Mzg1NDhhZjNlZjc3YTdkOWIwNmYwMjQ1IiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 06:52:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6InpSbnlkSExyNU1hZG8zWUZDNW12M1E9PSIsInZhbHVlIjoidjREYUN5WHF2bWFHZEJyOEltaFdtYlNIOXNGcWxQaS9LUkNpbXFnUEJBL2EwZUw3bUtUTUJDR3hxeFFmbFB4aTg0NURjS1IzQTl1dVNhNGtFN3I2aG9rK1pZZVBDREo3Q2ZGTnJIUXVmVkkwSk1kU1VrVXZHNGRtZzFrRUNCSjkwUWtXRWY2eU15Q3NRcXErTFVJMFJzMUcvTWxwSStBNHJ3OFpTcW9oN2x1KzI2QjZFNmdTaXFjc0xHelhGN0RUVE9SSitoMEdVRHczcG4reGNLdW9UWGl4Qk5meEg5eC9yTmlzVEI3ZG05a2RGYTYyd21HTnNzbTkrdGZqRG5kWmJqUXlyV0lSQTEya1JqaHdqbTZZRFl0QXlROWswMnRJOVBNZE9MU0NJcXRVd2hTeXh5cXpKMVkzSHVUeWVoSk0zQ2NSZjRjQzlXTW9KM0czQjcrYmQ4UmhhdXI4UVRyRVZrM3lmMGNZbE1wSlBodkNjVHVyL1E2TkxZTzYxQTJMbHVaRFo1YkErck1xZjVxSjlBaS9NUUhQN0FyY0creTlsNHdkb3ZsaU9LSk15MS9BanQvMUF4R0NpLzdxcC9PYUs0QTFxK3pZL2VkQVIzOWMyOEVmTUVHdW1PSUd3RzBWc0FlSXlnQVB6cUl3cVl2aFVDQVFTakVJMVU3V21GODEiLCJtYWMiOiIwY2ZkYzRkMTU0N2ViZDc2ZWU4NTRmMDA1YjY5NDg4ODk0MzFhZGU1YWY4ZDljYzc1NzYyNDM4ZTY2Yzc0NmRjIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 06:52:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJOeHU2RHpaOTdvSjZXSjQ4QzR6NUE9PSIsInZhbHVlIjoiZTlFSER0Z0VoQjJRamNLSHpsR1h6YWo4cFkzQWsvT0Q1SFBkTTliaDV3N1ExaWR4UitoalQ4UXNYbnhnQjVtK2JKc1YzWW1LQm94aHF1WUt0YzI0blFEd0FuTmpmTFg0NU1Xb1ZTdFVid3RhSFhIdXI4dmRWMXJiZkNneVhXc0pZbzhBaXpTcHNKUVBOZEhXYm5tTTlGVUk5K0xaTzdIRnFvQlcrdy9rWTlWRDRBYlplSmRqSmp1ckRtMjVFWk1xTUVzVUVuS01lOW5GNGE3TUV4UnBOY3NhaGtacElLak5PMWFVNjJwU255Uldmb0xCVkp0ZDdPMWtBalVzY2ttV3hicDUwSndxOVZFT1NNcnV1TW5WNGlPL3FCWVVSanVSTjBVc054V3RGeHZhVUE4ZEpKL25BYnZ4a3kxTlpvU0tjNEpYMTRJa0lOdkt4Tm45Q0VqK0Nsc3BDVkRCZUxqVmJaZVdqemE5Q1Z3TXRLSlhsSVNYSTE0RDBYU3B6L3kzMzJyWldka0tud1dMcmFDNmVyZ1FCY2pHWXpYMk02azNnN2NSNldraUhUQmFBSW5uNEEwNXA3UGZPL0liNUxWS056R2x0ZTRHeE0wYk1MY1hjdmNzSlNQY0RrS29HaU5BTlljWUxFUDRzRUl3cVAwVmp6R3JsWW83UzZCTzlUTEQiLCJtYWMiOiJlYzA4Y2JmZTBiYjI0ZTk0NGZiOTc4ODdkN2ZmN2YwNzNiYzRkYTI2Mzg1NDhhZjNlZjc3YTdkOWIwNmYwMjQ1IiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 06:52:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6InpSbnlkSExyNU1hZG8zWUZDNW12M1E9PSIsInZhbHVlIjoidjREYUN5WHF2bWFHZEJyOEltaFdtYlNIOXNGcWxQaS9LUkNpbXFnUEJBL2EwZUw3bUtUTUJDR3hxeFFmbFB4aTg0NURjS1IzQTl1dVNhNGtFN3I2aG9rK1pZZVBDREo3Q2ZGTnJIUXVmVkkwSk1kU1VrVXZHNGRtZzFrRUNCSjkwUWtXRWY2eU15Q3NRcXErTFVJMFJzMUcvTWxwSStBNHJ3OFpTcW9oN2x1KzI2QjZFNmdTaXFjc0xHelhGN0RUVE9SSitoMEdVRHczcG4reGNLdW9UWGl4Qk5meEg5eC9yTmlzVEI3ZG05a2RGYTYyd21HTnNzbTkrdGZqRG5kWmJqUXlyV0lSQTEya1JqaHdqbTZZRFl0QXlROWswMnRJOVBNZE9MU0NJcXRVd2hTeXh5cXpKMVkzSHVUeWVoSk0zQ2NSZjRjQzlXTW9KM0czQjcrYmQ4UmhhdXI4UVRyRVZrM3lmMGNZbE1wSlBodkNjVHVyL1E2TkxZTzYxQTJMbHVaRFo1YkErck1xZjVxSjlBaS9NUUhQN0FyY0creTlsNHdkb3ZsaU9LSk15MS9BanQvMUF4R0NpLzdxcC9PYUs0QTFxK3pZL2VkQVIzOWMyOEVmTUVHdW1PSUd3RzBWc0FlSXlnQVB6cUl3cVl2aFVDQVFTakVJMVU3V21GODEiLCJtYWMiOiIwY2ZkYzRkMTU0N2ViZDc2ZWU4NTRmMDA1YjY5NDg4ODk0MzFhZGU1YWY4ZDljYzc1NzYyNDM4ZTY2Yzc0NmRjIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 06:52:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1409417088\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1662386040 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">TvkmTSIwfIpGj65hFdj5LXIqhMw0ABBscCckixN6</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1662386040\", {\"maxDepth\":0})</script>\n"}}