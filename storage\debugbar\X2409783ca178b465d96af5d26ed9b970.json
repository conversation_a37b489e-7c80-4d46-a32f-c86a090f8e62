{"__meta": {"id": "X2409783ca178b465d96af5d26ed9b970", "datetime": "2025-07-29 05:03:49", "utime": **********.941673, "method": "GET", "uri": "/storage/uploads/avatar/avatar.png", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753765428.879648, "end": **********.941706, "duration": 1.0620579719543457, "duration_str": "1.06s", "measures": [{"label": "Booting", "start": 1753765428.879648, "relative_start": 0, "end": **********.869054, "relative_end": **********.869054, "duration": 0.9894061088562012, "duration_str": "989ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.869069, "relative_start": 0.****************, "end": **********.941709, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "72.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET storage/{path}", "middleware": "web, verified", "uses": "Closure($path) {#2997\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#466 …}\n  file: \"C:\\xampp\\htdocs\\omx-new-saas\\routes\\web.php\"\n  line: \"1801 to 1807\"\n}", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fomx-new-saas%2Froutes%2Fweb.php&line=1801\" onclick=\"\">routes/web.php:1801-1807</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "4faFszBycxARCKtZPtFI6TRCNMAC2lBF9WYthN7d", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/storage/uploads/avatar/avatar.png\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/storage/uploads/avatar/avatar.png", "status_code": "<pre class=sf-dump id=sf-dump-1584110449 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1584110449\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1461884695 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1461884695\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1520241770 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1520241770\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1172969563 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,bn;q=0.8,xh;q=0.7,hi;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1172969563\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1402503514 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1402503514\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1691157053 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 05:03:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">http://localhost:8000/verify</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVIMUpjUFdnSXRNT3FzM3kybDJ1T2c9PSIsInZhbHVlIjoiT1N4WEprRitrdEtoMERFYlozWGpMVWw4STNqU09HM1dMUUM3VC90YmNEZGpRREk0ZktPRkFwRXR3Q2JLQ2xpL3diT2IzOWQwWnp0RkFqL2dwQ3lxNUZWdndWL0RlTlF5MjNrZG5aWHpQTWk1d1Q0SE1rRk9iYjdBYS9ueG5PTFlOdDRmWGxjb3ArZHhYM1QrM016UHd3N1hxTFBubzB0L1VnVkZVbjVJbGpYRWpqOXNQaWc5QklGeGFyeWk0ZmtuVEhXbGxSTmhkSU5NUWFBQnlLUFIxbXhRakdvbWUraythRXlzNGowSDg0cjU5NFlxSmFWay9mRUNaV09yU1pFdHBQN3JIZGVLbm1TbXpCSUhjTnZjV3dTdDBZbTQ0OTRnR3BGTEtHS1BMQkhNUXlLUFp6WFFxYkpQRFRnTjlEZnRaejVDUFdxT2tFRlJwbWd3Y2JTanIydzU3WC93LzAvMUIvQzJJcmt3RnE5YlJIN3dZcVJtZmo2cC9udExRODRoSjJxVEVMazJRWGo1MkkwM1FKQlZYcGNiNVBoK3FwWHgyNlRXbUFyTU1GNDNudEZpTEkvTVBkN1NmNElLR1MxV3JXRlNzaXlwbUkyaVMyejI4TmhaWGJhYmFQeTdxYjVZN0RRV0ZVa2s2S2VlTWNHNVFGZjFONUdXTWQ3VlBiUk0iLCJtYWMiOiIwZjg0YTE4YTUyYzNmM2NmM2Y0YjE5ZjYwOGQ2ZDljMjUyMWNiNWVjN2FjNjIzNDFlYjEzMmU1YjM4MDc3OGRkIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:03:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"941 characters\">krishna_session=eyJpdiI6ImMxM211V0xQMUF1RmV1VVJISWtiU1E9PSIsInZhbHVlIjoiNW5IY2tHeTk1aFYwMFpBNm9uejlWRHRaNXk1SG5qV1BVN2pXdlVUYmNuT29oem5FUkF6Uk5KWDNqa20zd2JBUjZ1UHlEc2pBdzN3V3hZMXZZbHd3OC9zSVZxMTVsOFN4RDg3cGEvUkNmS05FZzNCMWw1TXJJY2NHVmo1WjFXTHlVejAvU2RZN3FVenhCMEd5Y3J3STFLL0tOaVNTWlRuRDcvb3VHd3J0eTNLdmFzOVVZeGZ0NENiTjd3Ym54N3A2NTdsY0RTc28zWFhqTEk3bFBjVVdyZDdxQzluSnI4Z1d5eWtmVTNhUUhmYWI3QmxYQlJhVzhrc1pwbWQzNEZKdWY3M2J0TjNWSGVvckk4K1RtRVZHMWFHS3d6YlRYWTVlNWVlc1ZCMVBtRlJLQXZ4QzRVd1pxSnBuM2FmSHovZXV0NC9CdzFFMUtBcUxIQjl2amk3T0pRNUREWEg3cmhVRm9UWkZIQThJcEdQVG9LZEFmcTFzcUJLdVFrSmo1TEt5RXNNRktySGhIdFVYZmZMajhSQkZ1dnplK3NvbHFiaXIwN0xMbnYyelJFdlR0NTNqVEw5N3oxL3FaM3VtV2lsYzZDb1diOW5mdDU1Sm11dFpidFNPdENQdVMralArekJkZTZJUkJsYVc4MytzN1RsS3pFUmF0NFdlU2hQY3doOVgiLCJtYWMiOiJhMzQyMjY0MDVjMDM0Yjc1MjI5NGM5OWE2ZDYyYmE1YWRiOWE1ZmE1MGNiMmVlYTdiYzQ5YjMzMGY2YWFiMDMxIiwidGFnIjoiIn0%3D; expires=Tue, 29 Jul 2025 07:03:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVIMUpjUFdnSXRNT3FzM3kybDJ1T2c9PSIsInZhbHVlIjoiT1N4WEprRitrdEtoMERFYlozWGpMVWw4STNqU09HM1dMUUM3VC90YmNEZGpRREk0ZktPRkFwRXR3Q2JLQ2xpL3diT2IzOWQwWnp0RkFqL2dwQ3lxNUZWdndWL0RlTlF5MjNrZG5aWHpQTWk1d1Q0SE1rRk9iYjdBYS9ueG5PTFlOdDRmWGxjb3ArZHhYM1QrM016UHd3N1hxTFBubzB0L1VnVkZVbjVJbGpYRWpqOXNQaWc5QklGeGFyeWk0ZmtuVEhXbGxSTmhkSU5NUWFBQnlLUFIxbXhRakdvbWUraythRXlzNGowSDg0cjU5NFlxSmFWay9mRUNaV09yU1pFdHBQN3JIZGVLbm1TbXpCSUhjTnZjV3dTdDBZbTQ0OTRnR3BGTEtHS1BMQkhNUXlLUFp6WFFxYkpQRFRnTjlEZnRaejVDUFdxT2tFRlJwbWd3Y2JTanIydzU3WC93LzAvMUIvQzJJcmt3RnE5YlJIN3dZcVJtZmo2cC9udExRODRoSjJxVEVMazJRWGo1MkkwM1FKQlZYcGNiNVBoK3FwWHgyNlRXbUFyTU1GNDNudEZpTEkvTVBkN1NmNElLR1MxV3JXRlNzaXlwbUkyaVMyejI4TmhaWGJhYmFQeTdxYjVZN0RRV0ZVa2s2S2VlTWNHNVFGZjFONUdXTWQ3VlBiUk0iLCJtYWMiOiIwZjg0YTE4YTUyYzNmM2NmM2Y0YjE5ZjYwOGQ2ZDljMjUyMWNiNWVjN2FjNjIzNDFlYjEzMmU1YjM4MDc3OGRkIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:03:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"927 characters\">krishna_session=eyJpdiI6ImMxM211V0xQMUF1RmV1VVJISWtiU1E9PSIsInZhbHVlIjoiNW5IY2tHeTk1aFYwMFpBNm9uejlWRHRaNXk1SG5qV1BVN2pXdlVUYmNuT29oem5FUkF6Uk5KWDNqa20zd2JBUjZ1UHlEc2pBdzN3V3hZMXZZbHd3OC9zSVZxMTVsOFN4RDg3cGEvUkNmS05FZzNCMWw1TXJJY2NHVmo1WjFXTHlVejAvU2RZN3FVenhCMEd5Y3J3STFLL0tOaVNTWlRuRDcvb3VHd3J0eTNLdmFzOVVZeGZ0NENiTjd3Ym54N3A2NTdsY0RTc28zWFhqTEk3bFBjVVdyZDdxQzluSnI4Z1d5eWtmVTNhUUhmYWI3QmxYQlJhVzhrc1pwbWQzNEZKdWY3M2J0TjNWSGVvckk4K1RtRVZHMWFHS3d6YlRYWTVlNWVlc1ZCMVBtRlJLQXZ4QzRVd1pxSnBuM2FmSHovZXV0NC9CdzFFMUtBcUxIQjl2amk3T0pRNUREWEg3cmhVRm9UWkZIQThJcEdQVG9LZEFmcTFzcUJLdVFrSmo1TEt5RXNNRktySGhIdFVYZmZMajhSQkZ1dnplK3NvbHFiaXIwN0xMbnYyelJFdlR0NTNqVEw5N3oxL3FaM3VtV2lsYzZDb1diOW5mdDU1Sm11dFpidFNPdENQdVMralArekJkZTZJUkJsYVc4MytzN1RsS3pFUmF0NFdlU2hQY3doOVgiLCJtYWMiOiJhMzQyMjY0MDVjMDM0Yjc1MjI5NGM5OWE2ZDYyYmE1YWRiOWE1ZmE1MGNiMmVlYTdiYzQ5YjMzMGY2YWFiMDMxIiwidGFnIjoiIn0%3D; expires=Tue, 29-Jul-2025 07:03:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1691157053\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1746807624 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">4faFszBycxARCKtZPtFI6TRCNMAC2lBF9WYthN7d</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/storage/uploads/avatar/avatar.png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1746807624\", {\"maxDepth\":0})</script>\n"}}